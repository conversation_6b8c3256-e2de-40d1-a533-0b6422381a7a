@import 'tailwindcss';

/* color vars */
/*
- primary background: #2f242c
- secondary bg: #c179b9
- sencodary bg light: #eeb4b3
- primary btn bg: #a42cd6
- secondary btn bg: #502274
- text dark: #000f08 (for lighter bg)
- text light: #f4fff8 (for darker bg)
*/

/* declare variables */
@theme {
  --color-primary-bg: #051923;
  --color-secondary-bg: #c179b9;
  --color-secondary-bg-light: #eeb4b3;
  --color-primary-btn-bg: #a42cd6;
  --color-secondary-btn-bg: #502274;
  --color-fg-dark: #000f08; /* for lighter bg */
  --color-fg-light: #f4fff8; /* for darker bg */
  --color-muted: #9a9c99; /* muted text color */

  --font-source: 'Source Code Pro', monospace;
  --font-display: 'DM Sans', sans-serif;

  /* Animation variables */
  --animate-overlayShow: overlayShow 120ms ease-in-out forwards;
  --animate-contentShow: contentShow 120ms ease-in-out forwards;
  --animate-reka-slideIn: rekaSlideIn 200ms ease-in-out forwards;
  --animate-reka-slideOut: rekaSlideOut 200ms ease-in-out forwards;
  --animate-reka-swipeOut: swipeOut 200ms ease-in-out forwards;

  /* Toast variables */
  --viewport-padding: 25px;

  @keyframes overlayShow {
    from {
      opacity: 0;
    }

    to {
      opacity: 1;
    }
  }

  @keyframes contentShow {
    from {
      opacity: 0;
      transform: scale(0.92);
      box-shadow: 0 8px 32px 0 rgba(80, 34, 116, 0.12);
    }
    60% {
      opacity: 0.7;
      transform: scale(0.98);
      box-shadow: 0 12px 40px 0 rgba(80, 34, 116, 0.18);
    }
    to {
      opacity: 1;
      transform: scale(1);
      box-shadow: 0 16px 48px 0 rgba(80, 34, 116, 0.22);
    }
  }
}

/* Custom animations for reka-ui toast */
@keyframes rekaSlideIn {
  from {
    transform: translateX(calc(100% + var(--viewport-padding)));
  }
  to {
    transform: translateX(0);
  }
}

@keyframes rekaSlideOut {
  to {
    transform: translateX(calc(100% + var(--viewport-padding)));
  }
}

@keyframes swipeOut {
  to {
    transform: translateX(100%);
  }
}

body {
  @apply font-display text-fg-light bg-primary-bg overflow-x-hidden;
}

/* total width */
.scrollbar::-webkit-scrollbar {
  background-color: transparent;
  width: 3px;
}

/* background of the scrollbar except button or resizer */
.scrollbar::-webkit-scrollbar-track {
  background-color: transparent;
  width: 3px;
}

/* scrollbar itself */
.scrollbar::-webkit-scrollbar-thumb {
  @apply bg-secondary-bg;
  border-radius: 8px;
}
.scrollbar::-webkit-scrollbar-thumb:hover {
  @apply bg-secondary-bg-light;
}

/* set button(top and bottom of the scrollbar) */
.scrollbar::-webkit-scrollbar-button {
  display: none;
}

.link {
  @apply text-secondary-bg hover:text-secondary-bg-light;
}

label {
  @apply text-fg-light text-xs font-semibold mb-1 block;
}
