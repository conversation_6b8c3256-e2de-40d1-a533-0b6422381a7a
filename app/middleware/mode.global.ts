export default defineNuxtRouteMiddleware((to, from) => {
  const runtimeConfig = useRuntimeConfig()
  const comingSoon = runtimeConfig.public.comingSoon === 'true'
  const maintenanceMode = runtimeConfig.public.maintenanceMode === 'true'
  const mobileComingSoon = runtimeConfig.public.mobileComingSoon === 'true'

  const breakpoints = useBreakpoints({
    mobile: 640,
    tablet: 768,
    laptop: 1024,
    desktop: 1280
  })

  const isMobile = breakpoints.smaller('tablet') // smaller than 768px
  // Only check for mobile coming soon on client side where breakpoints are available
  if (import.meta.client && mobileComingSoon) {
    if (isMobile.value && to.path !== '/coming-soon') {
      return navigateTo('/coming-soon')
    } else if (!isMobile.value && to.path === '/coming-soon') {
      return navigateTo('/')
    }
  }

  if (comingSoon && to.path !== '/coming-soon' && !isMobile.value) {
    return navigateTo('/coming-soon')
  } else if (!comingSoon && to.path === '/coming-soon' && !isMobile.value) {
    return navigateTo('/')
  }

  if (maintenanceMode) {
    console.log('Maintenance mode active:', maintenanceMode)
  }
})
