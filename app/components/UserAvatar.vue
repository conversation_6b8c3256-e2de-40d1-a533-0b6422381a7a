<script setup lang="ts">
import mg from '~/utils/tailwind'

interface Props {
  class?: string
  user?:
    | {
        id: number | null
        name?: string | null
        username?: string | null
        displayName?: string | null
        profilePicture?: string | null
        profileImage?: string | null
        avatar?: string | null
      }
    | null
    | undefined
  size?: 'sm' | 'md' | 'lg'
}

withDefaults(defineProps<Props>(), {
  size: 'sm',
  user: null,
  class: ''
})
</script>

<template>
  <template v-if="user">
    <div
      :class="
        mg(
          'w-10 h-10 rounded-full overflow-hidden bg-secondary-bg flex items-center justify-center',
          $props.class ?? '',
          size === 'sm' ? 'w-8 h-8' : '',
          size === 'md' ? 'w-12 h-12' : '',
          size === 'lg' ? 'w-20 h-20' : ''
        )
      "
    >
      <img
        v-if="user?.profilePicture || user?.avatar || user?.profileImage"
        :src="user.profilePicture ?? user?.avatar ?? user?.profileImage ?? ''"
        :alt="
          user?.displayName || user?.name || user?.username || 'User avatar'
        "
        class="w-full h-full object-cover"
      />
      <span v-else class="text-white font-bold">
        {{
          (user?.displayName || user?.name || user?.username)
            ?.charAt(0)
            .toUpperCase()
        }}
      </span>
    </div>
  </template>
  <!-- just show anonymous user lucide icon instead of img/initials -->
  <template v-else>
    <div
      :class="
        mg(
          'w-10 h-10 rounded-full overflow-hidden bg-secondary-bg flex items-center justify-center',
          $props.class ?? '',
          size === 'sm' ? 'w-8 h-8' : '',
          size === 'md' ? 'w-12 h-12' : '',
          size === 'lg' ? 'w-20 h-20' : ''
        )
      "
    >
      <Icon name="mdi:anonymous" class="w-full h-full text-secondary" />
    </div>
  </template>
</template>
