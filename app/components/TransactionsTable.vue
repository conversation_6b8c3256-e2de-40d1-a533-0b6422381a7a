<script setup lang="ts">
import type { TransactionSerialized } from '~/types/database'

const props = defineProps<{
  transactions: TransactionSerialized[] | null
  tokenInfo: TokenInfo | null
}>()

const lampToUsd = (lamports: number): string => {
  if (!props.tokenInfo || !props.tokenInfo.currentPrice?.currentSolPrice)
    return '0.00'
  return (
    (lamports / 1e9) *
    props.tokenInfo.currentPrice.currentSolPrice
  ).toFixed(2)
}

// 9 fractions for SOL to Lamports conversion, omits trailing zero
const lampToSol = (lamports: number): string => {
  return (lamports / 1e9).toString().replace(/\.?0+$/, '')
}

// 1k, 1M, 1B, etc. formatting for token amounts
const formatTokenAmount = (amount: number): string => {
  const a = amount / 1e9
  if (a >= 1e9) {
    return `${(a / 1e9).toFixed(2)}B`
  } else if (a >= 1e6) {
    return `${(a / 1e6).toFixed(2)}M`
  } else if (a >= 1e3) {
    return `${(a / 1e3).toFixed(2)}K`
  }
  return a.toString()
}

const formatAge = (createdAt: string): string => {
  if (!createdAt) return 'Error'

  const now = new Date()
  const date = new Date(createdAt)
  const diff = now.getTime() - date.getTime()
  const seconds = Math.floor(diff / 1000)

  if (seconds < 60)
    return `${seconds} ${seconds === 1 ? 'second' : 'seconds'} ago`
  const minutes = Math.floor(seconds / 60)
  if (minutes < 60)
    return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`
  const hours = Math.floor(minutes / 60)
  if (hours < 24) return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`
  const days = Math.floor(hours / 24)
  if (days < 30) return `${days} ${days === 1 ? 'day' : 'days'} ago`
  const months = Math.floor(days / 30)
  if (months < 12) return `${months} ${months === 1 ? 'month' : 'months'} ago`
  const years = Math.floor(months / 12)
  return `${years} ${years === 1 ? 'year' : 'years'} ago`
}
</script>

<template>
  <div class="space-y-3">
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead>
          <tr class="border-b border-gray-100/10">
            <th class="text-left py-2 px-2 text-xs font-semibold text-muted">
              Type
            </th>
            <th class="text-left py-2 px-2 text-xs font-semibold text-muted">
              User
            </th>
            <th class="text-left py-2 px-2 text-xs font-semibold text-muted">
              Amount (SOL)
            </th>
            <th class="text-left py-2 px-2 text-xs font-semibold text-muted">
              Amount (USD)
            </th>
            <th class="text-left py-2 px-2 text-xs font-semibold text-muted">
              Tokens
            </th>
            <th class="text-left py-2 px-2 text-xs font-semibold text-muted">
              Age
            </th>
            <th class="text-left py-2 px-2 text-xs font-semibold text-muted">
              Hash
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="tx in transactions"
            :key="tx.id"
            class="border-b border-gray-100/5 hover:bg-gray-50/5"
          >
            <td class="py-2 px-2">
              <span
                :class="[
                  'text-xs font-semibold px-2 py-0.5 rounded-full',
                  tx.type === 'BUY'
                    ? 'bg-green-500/20 text-green-400'
                    : 'bg-red-500/20 text-red-400'
                ]"
              >
                {{ tx.type }}
              </span>
            </td>
            <td class="py-2 px-2">
              <div class="flex items-center gap-2">
                <div
                  class="w-5 h-5 rounded-full bg-secondary-bg flex items-center justify-center"
                >
                  <span class="text-white font-bold text-xs">{{
                    tx.user?.name?.charAt(0)
                  }}</span>
                </div>
                <div>
                  <div class="font-semibold text-xs">{{ tx.user?.name }}</div>
                  <div class="text-xs text-muted">
                    {{ tx.user?.address?.slice(0, 6) }}...{{
                      tx.user?.address?.slice(-4)
                    }}
                  </div>
                </div>
              </div>
            </td>
            <td class="py-2 px-2 font-semibold text-xs">
              {{ lampToSol(parseInt(tx.amountLamports ?? '0')) }}
            </td>
            <td class="py-2 px-2 font-semibold text-xs">
              ${{ lampToUsd(parseInt(tx.amountLamports ?? '0')) }}
            </td>
            <td class="py-2 px-2 font-semibold text-xs">
              {{ formatTokenAmount(parseInt(tx.tokenAmount ?? '0')) }}
            </td>
            <td class="py-2 px-2 text-xs text-muted">
              {{ formatAge(tx.createdAt ?? '') }}
            </td>
            <td class="py-2 px-2">
              <button
                class="text-xs text-blue-400 hover:text-blue-300 font-mono"
              >
                {{ tx.transactionHash?.slice(0, 6) }}...
                {{ tx.transactionHash?.slice(-4) }}
              </button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- <div class="text-center pt-2">
      <button class="text-xs text-blue-400 hover:text-blue-300 font-semibold">
        View all
      </button>
    </div> -->
  </div>
</template>
