<script setup lang="ts">
import Toast from './toast/index.vue'
import { useToast } from '~/composables/useToast'

const { toasts, removeToast } = useToast()
</script>

<template>
  <ClientOnly>
    <div class="toaster-container">
      <div v-for="toast in toasts" :key="toast.id" class="toast-wrapper">
        <Toast
          :open="true"
          :title="toast.title"
          :description="toast.description"
          :type="toast.type"
          :duration="0"
          :action="toast.action"
          @close="removeToast(toast.id)"
        />
      </div>
    </div>
  </ClientOnly>
</template>

<style scoped>
@reference 'tailwindcss';

.toaster-container {
  position: fixed;
  bottom: 0;
  right: 0;
  z-index: 2147483647;
  pointer-events: none;
}

.toast-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 25px;
  width: 390px;
  max-width: 100vw;
}

.toast-wrapper {
  pointer-events: auto;
}

/* Transition animations */
.toast-enter-active {
  transition: all 0.3s ease-out;
}

.toast-leave-active {
  transition: all 0.3s ease-in;
}

.toast-enter-from {
  transform: translateX(100%);
  opacity: 0;
}

.toast-leave-to {
  transform: translateX(100%);
  opacity: 0;
}

.toast-move {
  transition: transform 0.3s ease;
}
</style>
