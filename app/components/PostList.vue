<script setup lang="ts">
import ReplyPost from '~/components/ReplyPost.vue'
import { useInteractionTracking } from '~/composables/useInteractionTracking'
import { InteractionType } from '~/types/database.d'
import type {
  Post,
  PostMedia,
  PostWithRelationAndCounts
} from '~/types/database.d'
import type { RecommendedItem } from '~/types/recommender'

const { trackView, trackTokenView } = useInteractionTracking()

// Use the posts store as fallback for when recommendations are empty
const posts = usePostsStore()

// current selected post for overlay
const currentSelectedPost = ref<Post | null>()

// Fetch recommended content (posts and replies mixed)
const { data: recommendedContent, pending: postsLoading } = await useLazyFetch<{
  content: Array<RecommendedItem>
  hasMore: boolean
}>('/api/recommendations/feed?type=mixed&limit=30', {
  server: false
})

const { data: recommendedTokens } = await useLazyFetch<{
  tokens: Array<Token>
}>('/api/recommendations/tokens?limit=10', {
  server: false
})

// Combine all recommended content into a unified feed
const combinedFeed = computed(() => {
  const recommendedContentList = recommendedContent.value?.content || []
  const tokens = recommendedTokens.value?.tokens || []

  // Use fallback posts from store if no recommended content is available
  const finalContent: RecommendedItem[] =
    recommendedContentList.length > 0
      ? recommendedContentList
      : (posts.posts || []).map(
          (post): RecommendedItem => ({
            id: post.id,
            content: post.content,
            createdAt: post.createdAt.toString(),
            creator: post.user,
            interactions: {
              id: 0,
              userId: 0,
              type: InteractionType.VIEW,
              createdAt: new Date(),
              updatedAt: new Date()
            },
            type: 'post' as const,
            originalPost: post as PostWithRelationAndCounts
          })
        )

  const combined: RecommendedItem[] = []

  // Add content (recommended or fallback)
  finalContent.forEach(item => {
    combined.push(item)
  })

  // If we still have no content, show a message encouraging posting
  if (combined.length === 0) {
    return []
  }

  // Intersperse tokens every few items
  let tokenIndex = 0
  const itemsWithTokens: RecommendedItem[] = []

  combined.forEach((item, index) => {
    itemsWithTokens.push(item)

    // Add token every 4th item
    if ((index + 1) % 4 === 0 && tokenIndex < tokens.length) {
      const token = tokens[tokenIndex]
      itemsWithTokens.push({
        id: token.id,
        content: '',
        creator: { id: token.userId || 0, name: null, address: '' },
        createdAt: token.createdAt,
        interactions: {
          id: 0,
          userId: 0,
          type: InteractionType.VIEW,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        type: 'token' as const,
        token: {
          ...token,
          createdAt: new Date(token.createdAt),
          updatedAt: new Date(token.updatedAt)
        }
      })
      tokenIndex++
    }
  })

  return itemsWithTokens
})

// Track views when content comes into view
const trackContentView = (item: RecommendedItem) => {
  if (item.type === 'post') {
    trackView(item.id)
  } else if (item.type === 'reply') {
    trackView(item.id)
  } else if (item.type === 'token') {
    trackTokenView(item.id)
  }
}

const handlePostMediaCLick = (
  media: PostMedia,
  post: Record<string, unknown>
) => {
  currentSelectedPost.value = post as Post
}
</script>

<template>
  <div class="flex flex-col rounded overflow-hidden pb-10">
    <template v-if="postsLoading || posts.isLoading">
      <SkeletonsPostSkeleton v-for="i in 3" :key="i" />
    </template>
    <template v-else-if="combinedFeed.length">
      <template v-for="item in combinedFeed" :key="`${item.type}-${item.id}`">
        <!-- Token recommendation -->
        <div
          v-if="item.type === 'token'"
          class="border-b border-gray-100/5 p-4 gap-4 hover:bg-white/[1%] cursor-pointer"
          @click="trackContentView(item)"
        >
          <div class="flex items-center gap-2 mb-3 text-amber-600">
            <Icon name="solar:flame-bold-duotone" size="16" />
            <span class="text-xs font-semibold">Hot Token</span>
          </div>

          <div class="flex items-center gap-3">
            <div
              class="w-10 h-10 rounded-full bg-secondary-bg flex items-center justify-center"
            >
              <span class="text-white font-bold">{{
                item.token?.symbol.charAt(0) || '?'
              }}</span>
            </div>
            <div class="flex-1 space-y-px text-sm">
              <div class="font-bold">{{ item.token?.name }}</div>
              <div class="flex items-center gap-2 text-xs text-muted">
                <span>${{ item.token?.symbol }}</span>
                <div class="w-1 h-1 bg-muted rounded-full" />
                <span
                  >{{
                    item.token?.transactions?.length || 0
                  }}
                  transactions</span
                >
              </div>
            </div>
          </div>

          <!-- Action buttons -->
          <div class="flex items-center justify-between text-muted mt-3">
            <div class="h-6 rounded w-1/4">
              <button
                class="flex items-center px-2 py-1 cursor-pointer hover:bg-gray-50/20 hover:text-pink-400 rounded-full gap-2"
              >
                <Icon name="lucide:activity" size="16" />
                <span class="text-xs">{{
                  item.token?.interactions?.length || 0
                }}</span>
              </button>
            </div>
            <div class="h-6 rounded w-1/4 flex items-center justify-end">
              <button
                class="flex items-center px-2 py-1 cursor-pointer hover:bg-gray-50/20 hover:text-green-400 rounded-full gap-2"
              >
                <Icon name="lucide:trending-up" size="16" />
                <span class="text-xs">{{
                  item.token?.transactions?.length || 0
                }}</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Post -->
        <PostCard
          v-else-if="item.type === 'post'"
          :post="item.originalPost"
          @view="trackContentView(item)"
          @media-click="handlePostMediaCLick"
        />

        <!-- Reply -->
        <ReplyPost
          v-else-if="item.type === 'reply'"
          :reply="item.reply"
          :original-post="item.originalPost"
          @view="trackContentView(item)"
        />
      </template>
    </template>
    <template v-else>
      <div class="text-center py-8 text-white/60">
        No posts yet. Be the first to bump a coin!
      </div>
    </template>
  </div>

  <PostMediaOverlay v-if="posts.postOverlayVisible" />
</template>
