<script setup lang="ts">
import mg from '~/utils/tailwind'

interface Props {
  class?: string
  href?: string
  count?: number
}

const layout = useLayout()

defineEmits<{
  (e: 'click'): void
}>()

withDefaults(defineProps<Props>(), {
  class: '',
  href: '#',
  count: 0
})
const route = useRoute()

const isActive = (href?: string): boolean => {
  return route.path === href
}
</script>

<template>
  <NuxtLink
    :href="$props.href"
    :class="
      mg(
        'p-2 hover:bg-gray-50/5  w-full text-lg text-left rounded-xs relative',
        $props.class ?? '',
        layout.isFullLeftSidebar
          ? 'w-full'
          : 'w-10 h-10 flex justify-center items-center',
        isActive($props.href) ? 'bg-gray-50/5' : ''
      )
    "
    @click="$emit('click')"
  >
    <slot name="icon" :size="layout.isFullLeftSidebar ? 25 : 20" />
    <slot v-if="layout.isFullLeftSidebar" name="text" />
    <span
      v-if="count"
      class="absolute top-1/2 -translate-y-1/2 right-2 bg-red-500 font-bold text-fg-light rounded-full h-5 w-5 flex items-center justify-center text-xs"
      :class="{ hidden: !layout.isFullLeftSidebar }"
    >
      {{ count }}
    </span>
  </NuxtLink>
</template>
