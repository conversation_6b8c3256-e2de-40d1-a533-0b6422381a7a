<script setup lang="ts">
import { useFollow } from '~/composables/useFollow'

interface Props {
  userId: number
  isFollowing: boolean
  variant?: 'default' | 'small'
  disabled?: boolean
}

interface Emits {
  (e: 'update:isFollowing', value: boolean): void
}

const props = withDefaults(defineProps<Props>(), {
  variant: 'default',
  disabled: false
})

const emit = defineEmits<Emits>()

const { toggleFollow, isLoading } = useFollow()

const handleClick = async () => {
  if (props.disabled || isLoading.value) return

  const newFollowingState = await toggleFollow(props.userId, props.isFollowing)

  if (newFollowingState !== undefined) {
    emit('update:isFollowing', newFollowingState)
  }
}

const buttonText = computed(() => {
  if (isLoading.value) return 'Loading...'
  return props.isFollowing ? 'Unfollow' : 'Follow'
})

const buttonClass = computed(() => {
  const baseClass =
    'font-semibold transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed'

  if (props.variant === 'small') {
    return `${baseClass} text-xs px-2 py-1 rounded`
  }

  return `${baseClass} text-sm px-4 py-2 rounded-lg`
})

const colorClass = computed(() => {
  if (props.isFollowing) {
    return 'bg-gray-100/5 hover:bg-gray-100/10 border border-gray-100/20 text-fg-light'
  }
  return 'bg-gray-100/10 hover:bg-gray-100/20 border border-gray-100/20 text-fg-light'
})
</script>

<template>
  <button
    :class="[buttonClass, colorClass]"
    :disabled="disabled || isLoading"
    @click="handleClick"
  >
    <Icon
      v-if="isLoading"
      name="lucide:loader-2"
      size="14"
      class="mr-1 animate-spin"
    />
    {{ buttonText }}
  </button>
</template>

<style></style>
