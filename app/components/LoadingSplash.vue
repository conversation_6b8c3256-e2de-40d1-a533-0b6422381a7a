<script setup lang="ts">
onMounted(() => {
  // add fixed to the body
  document.body.classList.add('fixed', 'overflow-hidden')
})

onUnmounted(() => {
  // remove fixed from the body
  document.body.classList.remove('fixed', 'overflow-hidden')
})
</script>

<template>
  <div
    class="fixed z-[9999] h-screen w-screen bg-primary-bg flex flex-col items-center justify-center"
  >
    <Logo class="size-24" />
  </div>
</template>
