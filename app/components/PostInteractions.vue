<script setup lang="ts">
interface Props {
  postId: number
  showReplyButton?: boolean
  size?: 'small' | 'normal'
}

const props = withDefaults(defineProps<Props>(), {
  showReplyButton: true,
  size: 'normal'
})

const emit = defineEmits<{
  'reply-click': []
}>()

const { toggleLike, triggerConfetti } = usePostInteractions()
const { loggedIn } = useUserSession()

// Use batch interactions for this post
const { usePostInteractions: useBatchPostInteractions } = useBatchInteractions()
const { interactions, updateInteractions } = useBatchPostInteractions(
  props.postId
)

const isLiking = ref(false)
// const isSharing = ref(false)

// Handle like interaction
const handleLike = async (event: Event) => {
  event.stopPropagation()

  if (!loggedIn.value) {
    // TODO: Show toast message or highlight the login button
    return
  }

  if (isLiking.value) return
  isLiking.value = true

  try {
    const likeButton = event.currentTarget as HTMLElement
    const result = await toggleLike(props.postId)

    if (result.success) {
      // Update batch interactions immediately
      updateInteractions({
        ...interactions.value,
        liked: result.data?.liked || false,
        likeCount: result.data?.likeCount || 0
      })

      // Trigger confetti animation on like
      if (result.data?.liked) {
        triggerConfetti(likeButton)
      }
    }
  } catch (error) {
    console.error('Failed to toggle like:', error)
    // TODO: Show error toast
  } finally {
    isLiking.value = false
  }
}

// Handle share interaction
const handleShare = async (event: Event) => {
  event.stopPropagation()

  // TODO:...
}

// Handle reply click
const handleReplyClick = (event: Event) => {
  event.stopPropagation()
  emit('reply-click')
}

// Computed properties for styling
const iconSize = computed(() => (props.size === 'small' ? '14' : '16'))
const textSize = computed(() =>
  props.size === 'small' ? 'text-2xs' : 'text-xs'
)
const gap = computed(() => (props.size === 'small' ? 'gap-3' : 'gap-4'))
</script>

<template>
  <div :class="['flex items-center text-gray-400', gap]">
    <!-- Reply Button -->
    <button
      v-if="showReplyButton"
      class="flex items-center gap-1 hover:text-blue-400 transition-colors cursor-pointer"
      @click="handleReplyClick"
    >
      <Icon name="lucide:message-circle" :size="iconSize" />
      <span :class="textSize">{{ interactions.replyCount }}</span>
    </button>

    <!-- Share Button -->
    <button
      :class="[
        'flex items-center gap-1 transition-colors cursor-pointer',
        interactions.shared ? 'text-green-400' : 'hover:text-green-400'
      ]"
      @click="handleShare"
    >
      <Icon name="lucide:repeat" :size="iconSize" />
      <span :class="textSize">{{ interactions.shareCount }}</span>
    </button>

    <!-- Like Button -->
    <button
      :class="[
        'flex items-center gap-1 transition-colors cursor-pointer interaction-button',
        interactions.liked ? 'text-red-400' : 'hover:text-red-400'
      ]"
      @click="handleLike"
    >
      <Icon v-if="interactions.liked" name="mdi:heart" :size="iconSize" />
      <Icon v-else name="lucide:heart" :size="iconSize" />
      <span :class="textSize">{{ interactions.likeCount }}</span>
    </button>

    <!-- Share External Button -->
    <button
      class="flex items-center gap-1 hover:text-gray-300 transition-colors cursor-pointer"
    >
      <Icon name="lucide:share" :size="iconSize" />
    </button>
  </div>
</template>
