<script setup lang="ts">
// Mock data for top trades
const trades = ref([
  {
    id: 1,
    type: 'Buy',
    user: { name: 'Whale7777', address: '1Qz3...M9kJ' },
    solAmount: '10.0',
    tokens: '125,000',
    priceImpact: '+2.5%',
    profit: '+1.2 SOL',
    time: '25m ago'
  },
  {
    id: 2,
    type: 'Sell',
    user: { name: 'DiamondHand<PERSON>', address: '7xKd...J9mP' },
    solAmount: '5.8',
    tokens: '72,000',
    priceImpact: '-1.8%',
    profit: '+0.8 SOL',
    time: '1h ago'
  },
  {
    id: 3,
    type: 'Buy',
    user: { name: '<PERSON><PERSON><PERSON>', address: '9vNp...K2jR' },
    solAmount: '8.5',
    tokens: '98,500',
    priceImpact: '+3.2%',
    profit: '+2.1 SOL',
    time: '2h ago'
  },
  {
    id: 4,
    type: '<PERSON>ll',
    user: { name: '<PERSON>', address: '2Hj8...N6wT' },
    solAmount: '3.2',
    tokens: '45,000',
    priceImpact: '-0.9%',
    profit: '+0.3 SOL',
    time: '3h ago'
  }
])
</script>

<template>
  <div class="space-y-3">
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead>
          <tr class="border-b border-gray-100/10">
            <th class="text-left py-2 px-2 text-xs font-semibold text-muted">
              Type
            </th>
            <th class="text-left py-2 px-2 text-xs font-semibold text-muted">
              Trader
            </th>
            <th class="text-left py-2 px-2 text-xs font-semibold text-muted">
              SOL
            </th>
            <th class="text-left py-2 px-2 text-xs font-semibold text-muted">
              Tokens
            </th>
            <th class="text-left py-2 px-2 text-xs font-semibold text-muted">
              Impact
            </th>
            <th class="text-left py-2 px-2 text-xs font-semibold text-muted">
              P&L
            </th>
            <th class="text-left py-2 px-2 text-xs font-semibold text-muted">
              Time
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="trade in trades"
            :key="trade.id"
            class="border-b border-gray-100/5 hover:bg-gray-50/5"
          >
            <td class="py-2 px-2">
              <span
                :class="[
                  'text-xs font-semibold px-2 py-0.5 rounded-full',
                  trade.type === 'Buy'
                    ? 'bg-green-500/20 text-green-400'
                    : 'bg-red-500/20 text-red-400'
                ]"
              >
                {{ trade.type }}
              </span>
            </td>
            <td class="py-2 px-2">
              <div class="flex items-center gap-2">
                <div
                  class="w-5 h-5 rounded-full bg-secondary-bg flex items-center justify-center"
                >
                  <span class="text-white font-bold text-xs">{{
                    trade.user.name.charAt(0)
                  }}</span>
                </div>
                <div>
                  <div class="font-semibold text-xs">{{ trade.user.name }}</div>
                  <div class="text-xs text-muted">{{ trade.user.address }}</div>
                </div>
              </div>
            </td>
            <td class="py-2 px-2 font-semibold text-xs">
              {{ trade.solAmount }}
            </td>
            <td class="py-2 px-2 font-semibold text-xs">{{ trade.tokens }}</td>
            <td class="py-2 px-2">
              <span
                :class="[
                  'text-xs font-semibold',
                  trade.priceImpact.startsWith('+')
                    ? 'text-green-400'
                    : 'text-red-400'
                ]"
              >
                {{ trade.priceImpact }}
              </span>
            </td>
            <td class="py-2 px-2">
              <span
                :class="[
                  'text-xs font-semibold',
                  trade.profit.startsWith('+')
                    ? 'text-green-400'
                    : 'text-red-400'
                ]"
              >
                {{ trade.profit }}
              </span>
            </td>
            <td class="py-2 px-2 text-xs text-muted">{{ trade.time }}</td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- <div class="text-center pt-2">
      <button class="text-xs text-blue-400 hover:text-blue-300 font-semibold">
        View all
      </button>
    </div> -->
  </div>
</template>
