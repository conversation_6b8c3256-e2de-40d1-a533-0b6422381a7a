<script setup lang="ts">
import type { PostWithRelationAndCounts } from '~/types/database'

interface Props {
  reply?: PostWithRelationAndCounts
  originalPost?: PostWithRelationAndCounts
}

const props = withDefaults(defineProps<Props>(), {
  reply: undefined,
  originalPost: undefined
})

const posts = usePostsStore()

const parsedContent = computed(() => {
  return posts.parseContent(props.reply?.content || '')
})

const renderContent = (content: string) => {
  return content
    .replace(
      /#(\w+)/g,
      '<span class="text-blue-400 font-semibold cursor-pointer hover:underline">#$1</span>'
    )
    .replace(
      /\$(\w+)/g,
      '<span class="text-green-400 font-semibold cursor-pointer hover:underline">$$$1</span>'
    )
    .replace(
      /@(\w+)/g,
      '<span class="text-purple-400 font-semibold cursor-pointer hover:underline">@$1</span>'
    )
}
</script>

<template>
  <div
    class="border-b border-gray-100/5 p-4 hover:bg-white/[1%] cursor-pointer"
  >
    <!-- Original post being replied to -->
    <div
      class="bg-gray-100/5 rounded-lg p-3 mb-3 border-l-2 border-gray-100/20"
    >
      <div class="flex items-center gap-2 mb-2">
        <div
          class="w-6 h-6 rounded-full bg-secondary-bg flex items-center justify-center"
        >
          <span class="text-white font-bold text-xs">
            {{ originalPost?.user?.name?.charAt(0).toUpperCase() || 'Unnamed' }}
          </span>
        </div>
        <div class="text-xs text-muted">
          <span class="font-semibold">{{ originalPost?.user?.name }}</span>
          <span class="mx-1">•</span>
          <span>{{ originalPost?.user?.address.slice(0, 8) }}...</span>
        </div>
      </div>
      <div
        class="text-sm text-muted pl-8"
        v-html="renderContent(originalPost?.content || '')"
      />
    </div>

    <!-- Reply content -->
    <div class="flex flex-col gap-3">
      <div class="flex items-center gap-3">
        <div
          class="w-10 h-10 rounded-full overflow-hidden bg-secondary-bg flex items-center justify-center"
        >
          <span class="text-white font-bold">
            {{ reply?.user?.name?.charAt(0).toUpperCase() }}
          </span>
        </div>
        <div class="flex-1 space-y-px text-sm">
          <div class="flex items-center gap-2">
            <div class="rounded w-auto font-bold">
              {{ reply?.user?.name }}
            </div>
            <div
              class="flex items-center gap-1 text-xs font-semibold text-muted"
            >
              <div>{{ reply?.user?.address?.slice(0, 8) }}...</div>
              <div class="w-1 h-1 bg-muted rounded-full text-muted" />
              <div>{{ formatTime(reply?.createdAt.toString()) }}</div>
            </div>
          </div>
          <div class="flex items-center gap-1 text-xs text-blue-400">
            <Icon name="lucide:reply" size="12" />
            <span>Replying to @{{ originalPost?.user?.name }}</span>
          </div>
        </div>
      </div>

      <div class="max-h-48 pl-13" v-html="renderContent(parsedContent)" />

      <div class="flex items-center justify-between text-muted pl-13">
        <div class="h-6 rounded w-1/4">
          <button
            class="flex items-center px-2 py-1 cursor-pointer hover:bg-gray-50/20 hover:text-pink-400 rounded-full gap-2"
          >
            <Icon name="lucide:heart" size="16" />
            <span class="text-xs">0</span>
          </button>
        </div>
        <div class="h-6 rounded w-1/4 flex items-center justify-end">
          <button
            class="flex items-center px-2 py-1 cursor-pointer hover:bg-gray-50/20 hover:text-green-400 rounded-full gap-2"
          >
            <Icon name="lucide:share" size="16" />
            <span class="text-xs">0</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
