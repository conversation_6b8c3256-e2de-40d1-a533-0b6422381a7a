<script setup lang="ts">
import type { Post, PostMedia, PostWithUser } from '~/types/database'

interface Props {
  post?: PostWithUser & {
    likeCount?: number
    replyCount?: number
    shareCount?: number
    media?: PostMedia[]
  }
}

defineEmits<{
  'media-click': [media: PostMedia, post: Post]
}>()

const props = withDefaults(defineProps<Props>(), {
  post: undefined
})

const router = useRouter()

const handlePostClick = async (event: Event) => {
  if (!props.post) return
  // Don't navigate if clicking on interactive buttons
  const target = event.target as HTMLElement
  if (target.closest('.interaction-button')) {
    return
  }
  router.push(`/post/${props.post.id}`)
}

const handleReplyClick = () => {
  if (!props.post) return
  router.push(`/post/${props.post.id}`)
}
</script>

<template>
  <div
    class="flex flex-col border-b border-gray-100/5 p-4 hover:bg-white/[1%] cursor-pointer"
    @click="handlePostClick"
  >
    <div class="flex items-center gap-3">
      <UserAvatar :user="post.user" size="sm" />
      <div class="flex-1 space-y-px text-sm">
        <div class="rounded w-auto font-bold">
          {{ post.user?.displayName || post.user?.name }}
        </div>
        <div class="flex items-center gap-1 text-xs font-semibold text-muted">
          <div>{{ post.user?.address?.slice(0, 8) }}...</div>
          <div class="w-1 h-1 bg-muted rounded-full text-muted" />
          <div>{{ formatTime(post.createdAt.toString()) }}</div>
        </div>
      </div>
    </div>
    <PostParsedContent :content="post.content" class="mt-2" />
    <!-- Post Images -->
    <PostImages
      v-if="post.media && post.media.length > 0"
      :media="post.media"
      :post-id="post.id"
    />

    <PostInteractions :post-id="post.id" @reply-click="handleReplyClick" />
  </div>
</template>
