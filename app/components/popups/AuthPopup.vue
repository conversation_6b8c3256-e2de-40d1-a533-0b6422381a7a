<script setup lang="ts">
import EmailLoginForm from '../auth/EmailLoginForm.vue'
import BasePopup from './BasePopup.vue'
import { TransitionPresets, useTransition } from '@vueuse/core'

type AuthMethod = 'email' | 'wallet' | 'none'
const shouldShowMethods = ref(true)
const chosenAuthMethod = shallowRef<AuthMethod>('none')
const prevTabSource = shallowRef(0)
const currentTabSource = shallowRef(120)
const prevTabTransitionFn = useTransition(prevTabSource, {
  duration: 300,
  transition: TransitionPresets.easeInOutCubic
})
const currentTabTransitionFn = useTransition(currentTabSource, {
  duration: 300,
  transition: TransitionPresets.easeInOutCubic,
  onFinished () {
    if (currentTabSource.value === -10) {
      shouldShowMethods.value = false
    }
  }
})

const goBackToMethods = () => {
  shouldShowMethods.value = true
  currentTabSource.value = 120
  prevTabSource.value = 0
  setTimeout(() => {
    chosenAuthMethod.value = 'none'
  }, 300)
}

const changeChosenView = () => {
  prevTabSource.value = 100
  currentTabSource.value = -10
}

const auth = useAuth()

watch(chosenAuthMethod, newMethod => {
  if (newMethod === 'email') {
    changeChosenView()
  } else if (newMethod === 'wallet') {
    // Connect wallet and login
    auth
      .login()
      .then(() => {
        // Reset the auth popup state
        chosenAuthMethod.value = 'none'
        shouldShowMethods.value = true
      })
      .catch(error => {
        console.error('Wallet login failed:', error)
        // Reset state on error
        chosenAuthMethod.value = 'none'
        shouldShowMethods.value = true
      })
  }
})
</script>

<template>
  <BasePopup
    trigger="Login"
    title="Login to your account"
    :disable-close="auth.isLoading"
  >
    <template #content>
      <div class="relative min-h-48 w-sm">
        <!-- Loading state -->
        <div
          v-if="auth.isLoading"
          class="w-full absolute top-0 left-0 flex flex-col items-center justify-center h-48"
        >
          <div
            class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-4"
          />
          <p class="text-sm text-muted">
            Connecting wallet and setting up account...
          </p>
        </div>

        <!-- Auth methods -->
        <AuthMethods
          v-if="shouldShowMethods && !auth.isLoading"
          class="w-full absolute top-0 left-0"
          :style="{
            transform: prevTabTransitionFn
              ? `translateX(-${prevTabTransitionFn}%)`
              : 'none',
            marginRight: chosenAuthMethod === 'email' ? '20px' : '0'
          }"
          @email-chosen="chosenAuthMethod = 'email'"
          @wallet-chosen="chosenAuthMethod = 'wallet'"
        />

        <!-- Email form -->
        <EmailLoginForm
          v-show="chosenAuthMethod === 'email' && !auth.isLoading"
          class="w-full absolute left-0 ps-px"
          :style="{
            transform: `translateX(${currentTabTransitionFn}%)`,
            left: '10%'
          }"
          @back="goBackToMethods"
        />
      </div>
    </template>
  </BasePopup>
</template>
