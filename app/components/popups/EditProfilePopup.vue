<script setup lang="ts">
import mg from '~/utils/tailwind'
import BasePopup from './BasePopup.vue'

interface Props {
  triggerClasses?: string
  trigger?: string
  userData?: {
    name: string
    username: string
    bio: string
    profileImage?: string
    coverImage?: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  triggerClasses:
    'bg-gray-100/5 hover:bg-gray-100/10 border-none text-fg-light text-sm font-semibold px-4 py-2 rounded-md',
  trigger: 'Edit Profile',
  userData: () => ({
    name: '',
    username: '',
    bio: '',
    profileImage: '',
    coverImage: ''
  })
})

const emit = defineEmits<{
  'update:profile': [
    data: {
      name: string
      bio: string
      profileImage?: string
      coverImage?: string
    }
  ]
}>()

// Form data
const formData = ref({
  name: props.userData.name,
  bio: props.userData.bio,
  profileImage: props.userData.profileImage || '',
  coverImage: props.userData.coverImage || ''
})

// File input refs
const profileImageInput = ref<HTMLInputElement>()
const coverImageInput = ref<HTMLInputElement>()

// Preview URLs
const profileImagePreview = ref(props.userData.profileImage || '')
const coverImagePreview = ref(props.userData.coverImage || '')

const isSubmitting = ref(false)
const { addToast } = useToast()

// Handle file uploads
const handleProfileImageChange = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      addToast({
        title: 'Invalid file type',
        description: 'Please select an image file',
        type: 'error'
      })
      return
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      addToast({
        title: 'File too large',
        description: 'Profile image must be smaller than 5MB',
        type: 'error'
      })
      return
    }

    const reader = new FileReader()
    reader.onload = e => {
      profileImagePreview.value = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}

const handleCoverImageChange = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      addToast({
        title: 'Invalid file type',
        description: 'Please select an image file',
        type: 'error'
      })
      return
    }

    // Validate file size (10MB max)
    if (file.size > 10 * 1024 * 1024) {
      addToast({
        title: 'File too large',
        description: 'Cover image must be smaller than 10MB',
        type: 'error'
      })
      return
    }

    const reader = new FileReader()
    reader.onload = e => {
      coverImagePreview.value = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}

const removeProfileImage = () => {
  profileImagePreview.value = ''
  formData.value.profileImage = ''
  if (profileImageInput.value) {
    profileImageInput.value.value = ''
  }
}

const removeCoverImage = () => {
  coverImagePreview.value = ''
  formData.value.coverImage = ''
  if (coverImageInput.value) {
    coverImageInput.value.value = ''
  }
}

const handleSubmit = async () => {
  // Validate form data
  if (!formData.value.name.trim()) {
    addToast({
      title: 'Validation Error',
      description: 'Display name is required',
      type: 'error'
    })
    return
  }

  if (formData.value.name.trim().length > 50) {
    addToast({
      title: 'Validation Error',
      description: 'Display name must be 50 characters or less',
      type: 'error'
    })
    return
  }

  if (formData.value.bio.length > 160) {
    addToast({
      title: 'Validation Error',
      description: 'Bio must be 160 characters or less',
      type: 'error'
    })
    return
  }

  isSubmitting.value = true

  try {
    // Create FormData for multipart/form-data submission
    const formDataToSend = new FormData()
    formDataToSend.append('displayName', formData.value.name.trim())
    formDataToSend.append('bio', formData.value.bio.trim())

    // Add profile image file if selected
    if (profileImageInput.value?.files?.[0]) {
      formDataToSend.append('profileImage', profileImageInput.value.files[0])
    }

    // Add cover image file if selected
    if (coverImageInput.value?.files?.[0]) {
      formDataToSend.append('coverImage', coverImageInput.value.files[0])
    }

    // Submit to API
    const response = await $fetch<{
      success: boolean
      message: string
      user: {
        id: number
        displayName: string | null
        bio: string | null
        profilePicture: string | null
        coverImage: string | null
        updatedAt: string
      }
    }>('/api/user/update-profile-info', {
      method: 'POST',
      body: formDataToSend
    })

    if (response.success) {
      addToast({
        title: 'Success',
        description: 'Profile updated successfully',
        type: 'success'
      })

      // Emit the updated profile data
      emit('update:profile', {
        name: response.user.displayName || '',
        bio: response.user.bio || '',
        profileImage: response.user.profilePicture || '',
        coverImage: response.user.coverImage || ''
      })

      // Update form data with server response
      formData.value = {
        name: response.user.displayName || '',
        bio: response.user.bio || '',
        profileImage: response.user.profilePicture || '',
        coverImage: response.user.coverImage || ''
      }

      // Update preview URLs
      profileImagePreview.value = response.user.profilePicture || ''
      coverImagePreview.value = response.user.coverImage || ''
    }
  } catch (error: any) {
    console.error('Failed to update profile:', error)

    addToast({
      title: 'Error',
      description:
        error.data?.message || 'Failed to update profile. Please try again.',
      type: 'error'
    })
  } finally {
    isSubmitting.value = false
  }
}

// Reset form when userData changes
watch(
  () => props.userData,
  newData => {
    formData.value = {
      name: newData.name,
      bio: newData.bio,
      profileImage: newData.profileImage || '',
      coverImage: newData.coverImage || ''
    }
    profileImagePreview.value = newData.profileImage || ''
    coverImagePreview.value = newData.coverImage || ''
  },
  { deep: true }
)
</script>

<template>
  <BasePopup
    title="Edit Profile"
    description="Update your profile information"
    :trigger-classes="triggerClasses"
    :trigger="trigger"
  >
    <template #content>
      <div class="w-full max-w-md lg:w-md">
        <form class="space-y-6" @submit.prevent="handleSubmit">
          <!-- Cover Image -->
          <div class="space-y-2">
            <label class="text-sm font-semibold text-fg-light"
              >Cover Photo</label
            >
            <div class="relative">
              <div
                class="w-full h-32 bg-gray-100/5 border border-gray-100/10 rounded-lg overflow-hidden"
              >
                <img
                  v-if="coverImagePreview"
                  :src="coverImagePreview"
                  alt="Cover preview"
                  class="w-full h-full object-cover"
                />
                <div
                  v-else
                  class="w-full h-full flex items-center justify-center text-muted"
                >
                  <Icon name="lucide:image" size="24" />
                </div>
              </div>
              <div class="absolute top-2 right-2 flex gap-2">
                <button
                  type="button"
                  class="bg-gray-900/80 hover:bg-gray-900 text-white p-2 h-7 w-7 flex items-center rounded-full transition-colors"
                  @click="coverImageInput?.click()"
                >
                  <Icon name="lucide:camera" />
                </button>
                <button
                  v-if="coverImagePreview"
                  type="button"
                  class="bg-red-600/80 hover:bg-red-600 text-white p-2 h-7 w-7 flex items-center rounded-full transition-colors"
                  @click="removeCoverImage"
                >
                  <Icon name="lucide:trash-2" />
                </button>
              </div>
            </div>
            <input
              ref="coverImageInput"
              type="file"
              accept="image/*"
              class="hidden"
              @change="handleCoverImageChange"
            />
          </div>

          <!-- Profile Image -->
          <div class="space-y-2">
            <label class="text-sm font-semibold text-fg-light"
              >Profile Photo</label
            >
            <div class="flex items-center gap-4">
              <div class="relative">
                <div
                  class="w-20 h-20 rounded-full bg-secondary-bg border-4 border-primary-bg overflow-hidden"
                >
                  <img
                    v-if="profileImagePreview"
                    :src="profileImagePreview"
                    alt="Profile preview"
                    class="w-full h-full object-cover"
                  />
                  <div
                    v-else
                    class="w-full h-full flex items-center justify-center text-white font-bold text-xl"
                  >
                    {{ formData.name.charAt(0).toUpperCase() || '?' }}
                  </div>
                </div>
                <button
                  type="button"
                  class="absolute -bottom-1 -right-1 bg-primary-btn-bg hover:bg-primary-btn-bg/90 text-white p-2 h-7 w-7 flex items-center rounded-full transition-colors"
                  @click="profileImageInput?.click()"
                >
                  <Icon name="lucide:camera" size="14" />
                </button>
              </div>
              <div class="flex-1">
                <button
                  type="button"
                  class="text-sm text-secondary-bg hover:text-secondary-bg-light transition-colors hover:underline hover:cursor-pointer"
                  @click="profileImageInput?.click()"
                >
                  Change photo
                </button>
                <button
                  v-if="profileImagePreview"
                  type="button"
                  class="block text-sm text-red-400 hover:text-red-300 transition-colors mt-1 hover:underline hover:cursor-pointer"
                  @click="removeProfileImage"
                >
                  Remove photo
                </button>
              </div>
            </div>
            <input
              ref="profileImageInput"
              type="file"
              accept="image/*"
              class="hidden"
              @change="handleProfileImageChange"
            />
          </div>

          <!-- Name -->
          <div class="space-y-2">
            <label for="name" class="text-sm font-semibold text-fg-light"
              >Display Name</label
            >
            <input
              id="name"
              v-model="formData.name"
              type="text"
              placeholder="Enter your display name"
              maxlength="50"
              required
              class="w-full px-3 py-2 bg-gray-100/5 border border-gray-100/10 rounded-md text-fg-light placeholder-muted focus:outline-none focus:border-secondary-bg transition-colors"
            />
            <div class="text-xs text-muted text-right">
              {{ formData.name.length }}/50
            </div>
          </div>

          <!-- Bio -->
          <div class="space-y-2">
            <label for="bio" class="text-sm font-semibold text-fg-light"
              >Bio</label
            >
            <textarea
              id="bio"
              v-model="formData.bio"
              placeholder="Tell us about yourself..."
              maxlength="160"
              rows="3"
              class="w-full px-3 py-2 bg-gray-100/5 border border-gray-100/10 rounded-md text-fg-light placeholder-muted focus:outline-none focus:border-secondary-bg transition-colors resize-none"
            />
            <div class="text-xs text-muted text-right">
              {{ formData.bio.length }}/160
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end gap-3 pt-4">
            <button
              type="button"
              class="px-4 py-2 text-sm font-semibold text-muted hover:text-fg-light transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="isSubmitting || !formData.name.trim()"
              class="px-6 py-2 bg-primary-btn-bg hover:bg-primary-btn-bg/90 disabled:bg-gray-500 disabled:cursor-not-allowed text-fg-light text-sm font-semibold rounded-md transition-colors"
            >
              {{ isSubmitting ? 'Saving...' : 'Save Changes' }}
            </button>
          </div>
        </form>
      </div>
    </template>
  </BasePopup>
</template>
