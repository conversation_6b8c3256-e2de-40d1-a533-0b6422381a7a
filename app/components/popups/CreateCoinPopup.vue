<script setup lang="ts">
import mg from '~/utils/tailwind'
import CreateCoinForm from '../forms/CreateCoinForm.vue'
import BasePopup from './BasePopup.vue'

const layout = useLayout()
const createCoinFormRef = ref()
const isCreating = computed(
  () => createCoinFormRef.value?.isSubmitting || false
)
</script>

<template>
  <BasePopup
    title="Create Coin"
    :disable-close="isCreating"
    :trigger-classes="
      mg(
        'flex items-center font-normal text-lg gap-2 bg-primary-btn-bg hover:bg-primary-btn-bg/90 border rounded-md border-green-500 w-full md:flex md:gap-2',
        'md:flex-row flex-col md:text-lg text-xs md:p-2 p-2 md:bg-primary-btn-bg bg-transparent md:border border-none md:text-white text-gray-300',
        layout.isFullLeftSidebar
          ? 'md:w-full'
          : 'md:w-10 md:h-10 md:justify-center md:items-center'
      )
    "
    trigger="Create coin"
  >
    <template #trigger-content="{ trigger }">
      <Icon name="lucide:plus" :size="layout.isFullLeftSidebar ? 25 : 20" />
      <span v-if="layout.isFullLeftSidebar" class="md:inline hidden">{{
        trigger
      }}</span>
      <span class="md:hidden inline text-xs">Create</span>
    </template>
    <template #content>
      <div class="min-h-0 flex-1 md:w-md">
        <CreateCoinForm ref="createCoinFormRef" class="mt-4" />
      </div>
    </template>
  </BasePopup>
</template>
