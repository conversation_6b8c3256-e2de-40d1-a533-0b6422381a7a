<script setup lang="ts">
import {
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  DialogTitle,
  DialogTrigger
} from 'reka-ui'
import mg from '~/utils/tailwind'

interface Props {
  triggerClasses?: string | undefined
  trigger: string | undefined
  title?: string | undefined
  description?: string | undefined
  disableClose?: boolean
}

withDefaults(defineProps<Props>(), {
  triggerClasses: '',
  trigger: 'Open',
  title: '',
  description: '',
  disableClose: false
})
</script>

<template>
  <DialogRoot>
    <DialogTrigger
      :class="
        mg(
          'p-2 flex-auto rounded focus:outline-1 outline-fg-light border border-secondary-bg-light bg-green-500 hover:bg-green-500/90 text-sm font-semibold',
          triggerClasses ?? ''
        )
      "
    >
      <template v-if="trigger && $slots['trigger-content']">
        <slot name="trigger-content" :trigger="trigger" />
      </template>
      <template v-else>
        {{ trigger }}
      </template>
    </DialogTrigger>
    <DialogPortal>
      <DialogOverlay
        class="backdrop-blur-md bg-gray-50/5 opacity-10 data-[state=open]:animate-overlayShow fixed inset-0 z-30"
      />
      <DialogContent
        class="data-[state=open]:animate-contentShow fixed focus:outline-none z-[100] bg-primary-bg border border-primary-btn-bg shadow-md inset-0 rounded-none p-0 overflow-y-auto scrollbar max-h-none max-w-none translate-x-0 translate-y-0 top-0 left-0 flex flex-col md:top-[50%] md:left-[50%] md:max-h-[85vh] md:max-w-[550px] md:translate-x-[-50%] md:translate-y-[-50%] md:rounded-[6px] md:p-[25px] md:inset-auto"
        @interact-outside="
          event => {
            if (disableClose) event.preventDefault()
          }
        "
      >
        <!-- Mobile Header with Back Button - only visible on mobile -->
        <div
          class="sticky top-0 bg-primary-bg border-b border-gray-100/5 px-4 py-3 flex items-center justify-between z-10 md:hidden"
        >
          <!-- Account for iOS status bar -->
          <div class="w-full pt-safe-top">
            <div class="flex items-center justify-between">
              <DialogClose
                v-if="!disableClose"
                class="flex items-center gap-2 text-gray-300 hover:text-white"
              >
                <Icon name="lucide:chevron-left" size="24" />
                <span class="text-sm font-medium">Back</span>
              </DialogClose>
              <div v-else class="flex items-center gap-2 text-gray-500">
                <Icon name="lucide:chevron-left" size="24" />
                <span class="text-sm font-medium">Back</span>
              </div>

              <DialogTitle
                v-if="title && title.length > 0"
                class="text-white text-lg font-semibold"
              >
                {{ title }}
              </DialogTitle>

              <div class="w-16" />
              <!-- Spacer for centering -->
            </div>
          </div>
        </div>

        <!-- Desktop Header (original behavior) - only visible on desktop -->
        <template class="hidden md:block">
          <DialogTitle
            v-if="title && title.length > 0"
            class="text-mauve12 m-0 text-[17px] font-semibold hidden md:block"
          >
            {{ title }}
          </DialogTitle>
          <DialogDescription
            v-if="description && description.length > 0"
            class="text-mauve11 text-muted mb-5 text-sm leading-normal hidden md:block"
          >
            {{ description }}
          </DialogDescription>
        </template>

        <!-- Content Area -->
        <div class="flex-1 p-4 pb-safe-bottom md:flex-none md:p-0 md:pb-0">
          <slot name="content" />
        </div>

        <!-- Desktop Close Button (original behavior) - only visible on desktop -->
        <DialogClose
          v-if="!disableClose"
          class="hover:bg-green4 focus:shadow-green7 absolute top-[10px] right-[10px] h-[25px] w-[25px] appearance-none items-center justify-center rounded-full focus:shadow-[0_0_0_2px] focus:outline-none hidden md:inline-flex"
          aria-label="Close"
        >
          <Icon name="lucide:x" />
        </DialogClose>
      </DialogContent>
    </DialogPortal>
  </DialogRoot>
</template>

<style scoped>
/* iOS safe area support */
.pt-safe-top {
  padding-top: env(safe-area-inset-top);
}

.pb-safe-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
