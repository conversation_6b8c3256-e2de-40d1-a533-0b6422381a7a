<!-- Example component showing how to use both WebSocket composables together -->
<template>
  <div class="token-realtime-monitor">
    <div class="connection-status">
      <div
        :class="{
          connected: mainWs.isConnected,
          disconnected: !mainWs.isConnected
        }"
      >
        Main WS: {{ mainWs.isConnected ? 'Connected' : 'Disconnected' }}
      </div>
      <div
        :class="{
          connected: solanaWs.isConnected,
          disconnected: !solanaWs.isConnected
        }"
      >
        Solana WS: {{ solanaWs.isConnected ? 'Connected' : 'Disconnected' }}
      </div>
    </div>

    <div v-if="tokenAddress" class="token-data">
      <h3>{{ tokenAddress }}</h3>

      <!-- Price data from main WebSocket -->
      <div v-if="priceData.data.value.length > 0" class="price-section">
        <h4>Price Data (Main WS)</h4>
        <div v-for="price in priceData.data.value" :key="price.tokenId">
          <p>Price: {{ price.price }} SOL</p>
          <p>Volume 24h: {{ price.volume24h }}</p>
          <p>Market Cap: {{ price.marketCap?.real?.formatted || 'N/A' }}</p>
          <p>
            Last Update: {{ new Date(price.timestamp).toLocaleTimeString() }}
          </p>
        </div>
      </div>

      <!-- Curve updates from Solana WebSocket -->
      <div class="curve-section">
        <h4>Real-time Curve Updates (Solana WS)</h4>
        <div v-if="latestCurveUpdate">
          <p>SOL Reserves: {{ latestCurveUpdate.solReserves }}</p>
          <p>Token Reserves: {{ latestCurveUpdate.tokenReserves }}</p>
          <p>Price: {{ latestCurveUpdate.price }} SOL</p>
          <p>
            Last Update:
            {{ new Date(latestCurveUpdate.timestamp).toLocaleTimeString() }}
          </p>
          <p class="source">Source: {{ latestCurveUpdate.source }}</p>
        </div>
        <p v-else>No curve updates yet</p>
      </div>
    </div>

    <div class="controls">
      <input
        v-model="inputTokenAddress"
        placeholder="Enter token address"
        @keyup.enter="subscribeToToken"
      />
      <button :disabled="!inputTokenAddress" @click="subscribeToToken">
        Subscribe to Token
      </button>
      <button :disabled="!tokenAddress" @click="unsubscribeFromToken">
        Unsubscribe
      </button>
      <button :disabled="!tokenAddress" @click="refreshMainData">
        Refresh Main Data
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  initialTokenAddress?: string
}

const props = withDefaults(defineProps<Props>(), {
  initialTokenAddress: ''
})

// State
const tokenAddress = ref<string>(props.initialTokenAddress)
const inputTokenAddress = ref<string>(props.initialTokenAddress)
const latestCurveUpdate = ref<any>(null)

// Main WebSocket for price data, transactions, etc.
const priceData = useWebSocket(
  'price',
  computed(() => ({
    tokenAddress: tokenAddress.value
  }))
)

// Computed for connection status
const mainWs = computed(() => ({
  isConnected: priceData.isConnected.value
}))

// Solana WebSocket for real-time curve account monitoring
const solanaWs = useSolanaWebSocket()

// Subscribe to token when component mounts (if initial address provided)
onMounted(() => {
  if (tokenAddress.value) {
    subscribeToSolanaUpdates()
  }
})

// Subscribe to Solana curve updates for the current token
const subscribeToSolanaUpdates = () => {
  if (!tokenAddress.value) return

  // Subscribe to Solana WebSocket for curve updates
  solanaWs.subscribeToToken(tokenAddress.value)

  // Register callback for curve updates
  const unsubscribeCallback = solanaWs.onCurveUpdate(
    tokenAddress.value,
    curveUpdate => {
      console.log('🔥 Received curve update:', curveUpdate)
      latestCurveUpdate.value = curveUpdate

      // When we get a curve update, refresh the main WebSocket data
      // This ensures we get updated price data, market cap, etc.
      refreshMainData()
    }
  )

  // Clean up callback when component unmounts or token changes
  onUnmounted(() => {
    if (unsubscribeCallback && typeof unsubscribeCallback === 'function') {
      unsubscribeCallback()
    }
  })
}

const subscribeToToken = () => {
  if (!inputTokenAddress.value) return

  // Unsubscribe from previous token if any
  if (tokenAddress.value) {
    unsubscribeFromToken()
  }

  // Set new token address
  tokenAddress.value = inputTokenAddress.value
  latestCurveUpdate.value = null

  // Subscribe to both WebSockets
  subscribeToSolanaUpdates()

  console.log(`✅ Subscribed to token: ${tokenAddress.value}`)
}

const unsubscribeFromToken = () => {
  if (!tokenAddress.value) return

  // Unsubscribe from Solana WebSocket
  solanaWs.unsubscribeFromToken(tokenAddress.value)

  // Clear state
  tokenAddress.value = ''
  inputTokenAddress.value = ''
  latestCurveUpdate.value = null

  console.log('🔴 Unsubscribed from token')
}

const refreshMainData = () => {
  if (!tokenAddress.value) return

  // Refresh main WebSocket data (price, market cap, etc.)
  priceData.refresh()

  console.log('🔄 Refreshed main WebSocket data')
}

// Clean up on unmount
onUnmounted(() => {
  if (tokenAddress.value) {
    solanaWs.unsubscribeFromToken(tokenAddress.value)
  }
})
</script>

<style scoped>
.token-realtime-monitor {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.connection-status {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.connected {
  color: green;
  font-weight: bold;
}

.disconnected {
  color: red;
  font-weight: bold;
}

.token-data {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.price-section,
.curve-section {
  background: white;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 15px;
}

.source {
  font-style: italic;
  color: #666;
  font-size: 0.9em;
}

.controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

input {
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  flex: 1;
}

button {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background: #007bff;
  color: white;
  cursor: pointer;
}

button:disabled {
  background: #ccc;
  cursor: not-allowed;
}

button:hover:not(:disabled) {
  background: #0056b3;
}

h3,
h4 {
  margin-top: 0;
}
</style>
