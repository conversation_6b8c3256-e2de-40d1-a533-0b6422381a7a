<script setup lang="ts">
import { useToast } from '~/composables/useToast'

const toast = useToast()

const showSuccessToast = () => {
  toast.success('Success!', 'Your action was completed successfully.')
}

const showErrorToast = () => {
  toast.error('Error occurred', 'Something went wrong. Please try again.')
}

const showWarningToast = () => {
  toast.warning('Warning', 'Please check your input before proceeding.')
}

const showInfoToast = () => {
  toast.info('Information', 'Here is some useful information for you.')
}

const showCustomToast = () => {
  toast.addToast({
    title: 'Custom Toast',
    description: 'This toast has a custom action.',
    type: 'info',
    duration: 0, // Won't auto-close
    action: {
      label: 'Retry',
      onClick: () => {
        console.log('Retry clicked!')
        // Add your retry logic here
      }
    }
  })
}

const clearAllToasts = () => {
  toast.clearAllToasts()
}
</script>

<template>
  <div class="p-8 space-y-4">
    <h1 class="text-2xl font-bold mb-6">Toast Examples</h1>

    <div class="space-x-4">
      <button
        class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
        @click="showSuccessToast"
      >
        Success Toast
      </button>

      <button
        class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        @click="showErrorToast"
      >
        Error Toast
      </button>

      <button
        class="px-4 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600"
        @click="showWarningToast"
      >
        Warning Toast
      </button>

      <button
        class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        @click="showInfoToast"
      >
        Info Toast
      </button>

      <button
        class="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600"
        @click="showCustomToast"
      >
        Custom Toast with Action
      </button>

      <button
        class="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600"
        @click="clearAllToasts"
      >
        Clear All
      </button>
    </div>
  </div>
</template>
