<script setup lang="ts">
import CreateCoinPopup from './popups/CreateCoinPopup.vue'

const { loggedIn, user } = useUserSession()
const notificationsStore = useNotificationsStore()
</script>

<template>
  <!-- Mobile Bottom Navigation -->
  <nav
    class="fixed bottom-0 left-0 right-0 z-50 bg-black/95 backdrop-blur-sm border-t border-gray-100/5 md:hidden"
  >
    <!-- Account for iOS bottom safe area -->
    <div class="pb-safe-bottom">
      <div class="flex items-center justify-around px-2 py-2">
        <!-- Create Coin Button -->
        <div class="flex-1 flex justify-center">
          <CreateCoinPopup />
        </div>

        <!-- Home -->
        <div class="flex-1 flex justify-center">
          <NuxtLink
            href="/"
            class="flex flex-col items-center gap-1 p-2 text-gray-300 hover:text-white"
          >
            <Icon name="lucide:home" size="20" />
            <span class="text-xs">Home</span>
          </NuxtLink>
        </div>

        <!-- Realtime -->
        <div class="flex-1 flex justify-center">
          <NuxtLink
            href="/realtime"
            class="flex flex-col items-center gap-1 p-2 text-gray-300 hover:text-white"
          >
            <Icon name="lucide:flame" size="20" />
            <span class="text-xs">Realtime</span>
          </NuxtLink>
        </div>

        <!-- Notifications (only show if logged in) -->
        <div v-if="loggedIn" class="flex-1 flex justify-center">
          <NuxtLink
            href="/notifications"
            class="flex flex-col items-center gap-1 p-2 text-gray-300 hover:text-white relative"
          >
            <Icon name="lucide:bell" size="20" />
            <span class="text-xs">Alerts</span>
            <span
              v-if="notificationsStore.unreadCount > 0"
              class="absolute -top-1 -right-1 bg-red-500 font-bold text-white rounded-full h-4 w-4 flex items-center justify-center text-xs"
            >
              {{ notificationsStore.unreadCount }}
            </span>
          </NuxtLink>
        </div>

        <!-- Profile -->
        <div class="flex-1 flex justify-center">
          <NuxtLink
            :href="`/${user?.name || ''}`"
            class="flex flex-col items-center gap-1 p-2 text-gray-300 hover:text-white"
          >
            <Icon name="lucide:user-circle" size="20" />
            <span class="text-xs">Profile</span>
          </NuxtLink>
        </div>
      </div>
    </div>
  </nav>
</template>

<style scoped>
/* iOS safe area support */
.pb-safe-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
