<script setup lang="ts">
import { computed, nextTick, onMounted, onUnmounted } from 'vue'

interface Props {
  placeholder?: string
  buttonText?: string
  isReply?: boolean
  postId?: number
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Bump a coin',
  buttonText: 'Post',
  isReply: false
})

const emit = defineEmits<{
  'reply-added': [reply: any]
}>()

const isFocused = ref(false)
const contentElement = ref<HTMLElement | null>(null)
const shouldShowBottomBar = ref(false)
const postContent = ref('')
const showHashtagPopover = ref(false)
const showTokenPopover = ref(false)
const popoverPosition = ref({ x: 0, y: 0 })
const currentQuery = ref('')
const selectedIndex = ref(0)
const MAX_CHARACTERS = 270
const mainContent = ref<Element | null>(null)
const postFormContainer = ref<HTMLElement | null>(null)
const selectedImages = ref<File[]>([])
const fileInput = ref<HTMLInputElement | null>(null)

onMounted(() => {
  mainContent.value = document.querySelector('.main-content')

  // Listen for selection changes to update popover position
  document.addEventListener('selectionchange', handleSelectionChange)
})

onUnmounted(() => {
  document.removeEventListener('selectionchange', handleSelectionChange)

  // Clean up all image URLs
  imagePreviewUrls.value.forEach(url => {
    URL.revokeObjectURL(url)
  })
  imagePreviewUrls.value.clear()
})

const handleSelectionChange = () => {
  // Only update position if a popover is shown and the selection is in our contenteditable
  if (
    (showHashtagPopover.value || showTokenPopover.value) &&
    contentElement.value
  ) {
    const selection = window.getSelection()
    if (
      selection?.rangeCount &&
      contentElement.value.contains(selection.anchorNode)
    ) {
      updatePopoverPosition()
    }
  }
}

const posts = usePostsStore()

watch(isFocused, focused => {
  if (focused && contentElement.value) {
    setTimeout(() => {
      contentElement.value?.focus()
      shouldShowBottomBar.value = true

      // Clean up any browser-generated content when focusing
      if (contentElement.value) {
        const text = getTextContentWithLineBreaks()
        const innerHTML = contentElement.value.innerHTML

        // If there's no content, ensure we start with a paragraph
        if (!text.trim() && (!innerHTML.trim() || innerHTML === '')) {
          const initialParagraph = document.createElement('p')
          initialParagraph.style.margin = '0'
          initialParagraph.style.lineHeight = '1.5'
          initialParagraph.innerHTML = '\u200B'
          contentElement.value.innerHTML = ''
          contentElement.value.appendChild(initialParagraph)

          // Set cursor in the paragraph
          const range = document.createRange()
          const selection = window.getSelection()
          range.setStart(initialParagraph, 0)
          range.setEnd(initialParagraph, 0)
          selection?.removeAllRanges()
          selection?.addRange(range)

          postContent.value = ''
        }
        // Only intervene if there are serious structural problems
        // If we already have paragraph structure, leave it completely alone
        else if (!innerHTML.includes('<p>')) {
          // Only clean up if we don't have any paragraph structure at all
          if (
            innerHTML.includes('<b>') ||
            innerHTML.includes('<div>') ||
            innerHTML.includes('<i>') ||
            innerHTML.includes('<u>') ||
            innerHTML.includes('<span>') ||
            innerHTML.includes('<br>')
          ) {
            // Has problematic tags and no paragraphs - clean up
            renderStyledContent(text)
          }
          // If content exists but no paragraphs at all, wrap in paragraphs
          else if (text.trim()) {
            renderStyledContent(text)
          }
        }
        // If we have <p> tags, leave the content completely alone
      }
    })
  } else {
    shouldShowBottomBar.value = false
    showHashtagPopover.value = false
    showTokenPopover.value = false
    selectedIndex.value = 0
  }
})

const handleKeydown = (event: KeyboardEvent) => {
  // Handle popover navigation
  if (showHashtagPopover.value || showTokenPopover.value) {
    const items = showHashtagPopover.value
      ? filteredHashtags.value
      : filteredTokens.value

    switch (event.key) {
      case 'ArrowDown':
        event.preventDefault()
        event.stopPropagation()
        selectedIndex.value = Math.min(
          selectedIndex.value + 1,
          items.length - 1
        )
        return // Important: return early to prevent any further processing
      case 'ArrowUp':
        event.preventDefault()
        event.stopPropagation()
        selectedIndex.value = Math.max(selectedIndex.value - 1, 0)
        return // Important: return early to prevent any further processing
      case 'Enter':
      case 'Tab':
        event.preventDefault()
        event.stopPropagation()
        if (items[selectedIndex.value]) {
          const type = showHashtagPopover.value ? 'hashtag' : 'token'
          insertMention(type, items[selectedIndex.value].id)
        }
        return // Important: return early to prevent any further processing
      case 'Escape':
        event.preventDefault()
        event.stopPropagation()
        showHashtagPopover.value = false
        showTokenPopover.value = false
        return // Important: return early to prevent any further processing
    }
    // If we reach here, it's some other key while popover is open
    // Let it fall through to normal handling, but don't reset selectedIndex
  }

  // Handle backspace/delete to clear HTML when content becomes empty
  if (event.key === 'Backspace' || event.key === 'Delete') {
    // Use setTimeout to check content after the key event is processed
    setTimeout(() => {
      if (contentElement.value) {
        const text = getTextContentWithLineBreaks()
        if (!text.trim() || text.replace(/[\u200B\u00A0]/g, '').trim() === '') {
          // Aggressively clear everything including browser-generated tags only when truly empty
          const initialParagraph = document.createElement('p')
          initialParagraph.style.margin = '0'
          initialParagraph.style.lineHeight = '1.5'
          initialParagraph.innerHTML = '\u200B'
          contentElement.value.innerHTML = ''
          contentElement.value.appendChild(initialParagraph)
          postContent.value = ''
          showHashtagPopover.value = false
          showTokenPopover.value = false
          selectedIndex.value = 0
        } else {
          // Check for unwanted browser tags even when content exists
          const innerHTML = contentElement.value.innerHTML
          // Don't clean up <p> tags as they're our preferred block elements
          if (
            innerHTML.includes('<b>') ||
            innerHTML.includes('<div>') ||
            innerHTML.includes('<i>') ||
            innerHTML.includes('<u>') ||
            innerHTML.includes('<span>') ||
            innerHTML.includes('<br>')
          ) {
            // Browser added unwanted tags, clean them up
            const cleanText = text
            if (cleanText.includes('#') || cleanText.includes('$')) {
              // Has mentions, re-render with proper styling
              renderStyledContent(cleanText)
            } else if (!innerHTML.includes('<p>')) {
              // Only clean up if we don't have proper paragraph structure
              // This prevents destroying existing multi-paragraph content
              const currentPos = getCaretPosition()
              renderStyledContent(cleanText)
              // Restore cursor position
              nextTick(() => {
                if (contentElement.value) {
                  contentElement.value.focus()
                  setCaretPositionSafe(currentPos)
                }
              })
            }
          }
        }
      }
    }, 0)
  }

  if (event.key === 'Escape') {
    isFocused.value = false
  }

  // Handle Enter key to create new paragraphs
  if (event.key === 'Enter') {
    event.preventDefault()

    if (!contentElement.value) return

    const selection = window.getSelection()
    if (!selection?.rangeCount) return

    const range = selection.getRangeAt(0)

    // Create a new paragraph
    const newParagraph = document.createElement('p')
    newParagraph.style.margin = '0'
    newParagraph.style.lineHeight = '1.5'
    newParagraph.innerHTML = '\u200B' // Zero-width space to maintain height

    // Find the current paragraph
    let currentElement = range.startContainer
    while (
      currentElement &&
      currentElement.nodeName !== 'P' &&
      currentElement.parentNode
    ) {
      currentElement = currentElement.parentNode
    }

    if (currentElement && currentElement.nodeName === 'P') {
      // Insert the new paragraph after the current one
      currentElement.parentNode?.insertBefore(
        newParagraph,
        currentElement.nextSibling
      )

      // Move cursor to the new paragraph
      const newRange = document.createRange()
      newRange.setStart(newParagraph, 0)
      newRange.setEnd(newParagraph, 0)
      selection.removeAllRanges()
      selection.addRange(newRange)
    } else {
      // Fallback: append to the end
      contentElement.value.appendChild(newParagraph)

      const newRange = document.createRange()
      newRange.setStart(newParagraph, 0)
      newRange.setEnd(newParagraph, 0)
      selection.removeAllRanges()
      selection.addRange(newRange)
    }

    // Update the content model
    nextTick(() => {
      handleInput()
    })
  }
}

const handlePaste = async (event: KeyboardEvent | ClipboardEvent) => {
  event.preventDefault()

  const clipboardData =
    (event as ClipboardEvent).clipboardData || (window as any).clipboardData
  if (!clipboardData) {
    return
  }

  const items = clipboardData.items
  let hasImage = false

  // Check for images first
  for (let i = 0; i < items.length; i++) {
    const item = items[i]

    if (item.type.indexOf('image') === 0) {
      hasImage = true
      const file = item.getAsFile()
      if (file) {
        selectedImages.value.push(file)
      } else {
        console.warn('Failed to get file from clipboard item')
      }
    }
  }

  // If no images, handle text pasting (plain text only)
  if (!hasImage) {
    const text = clipboardData.getData('text/plain')
    if (text && contentElement.value) {
      const selection = window.getSelection()
      if (selection?.rangeCount) {
        const range = selection.getRangeAt(0)
        range.deleteContents()

        // Insert plain text only
        const textNode = document.createTextNode(text)
        range.insertNode(textNode)

        // Move cursor to end of inserted text
        const newRange = document.createRange()
        newRange.setStartAfter(textNode)
        newRange.setEndAfter(textNode)
        selection.removeAllRanges()
        selection.addRange(newRange)

        // Update content model
        handleInput()
      }
    }
  }
}

const handleImageButtonClick = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const files = target.files
  if (files) {
    for (let i = 0; i < files.length; i++) {
      const file = files[i]
      if (file.type.indexOf('image') === 0) {
        selectedImages.value.push(file)
      }
    }
  }
  // Reset input so same file can be selected again
  target.value = ''
}

const removeImage = (index: number) => {
  const file = selectedImages.value[index]
  cleanupImageUrl(file)
  selectedImages.value.splice(index, 1)
}

// Cache for image preview URLs to prevent recreating them on every render
const imagePreviewUrls = ref<Map<File, string>>(new Map())

const getImagePreviewUrl = (file: File) => {
  if (!imagePreviewUrls.value.has(file)) {
    imagePreviewUrls.value.set(file, URL.createObjectURL(file))
  }
  return imagePreviewUrls.value.get(file)!
}

// Computed property for stable image URLs to prevent template re-renders
const imagePreviewsWithUrls = computed(() => {
  return selectedImages.value.map((file, index) => ({
    file,
    url: getImagePreviewUrl(file),
    index
  }))
})

// Clean up URLs when images are removed
const cleanupImageUrl = (file: File) => {
  const url = imagePreviewUrls.value.get(file)
  if (url) {
    URL.revokeObjectURL(url)
    imagePreviewUrls.value.delete(file)
  }
}

const handleClickOutside = (event: MouseEvent) => {
  if (
    contentElement.value &&
    !contentElement.value.contains(event.target as Node)
  ) {
    isFocused.value = false
  }
}

onClickOutside(contentElement, handleClickOutside)

const getTextContentWithLineBreaks = () => {
  if (!contentElement.value) return ''

  // Get all paragraph elements
  const paragraphs = contentElement.value.querySelectorAll('p')
  if (paragraphs.length === 0) {
    return contentElement.value.textContent || ''
  }

  // Extract text from each paragraph and join with line breaks
  const lines = Array.from(paragraphs).map(p => p.textContent || '')
  return lines.join('\n')
}

const handleInput = () => {
  const text = getTextContentWithLineBreaks()

  // If content is effectively empty (only whitespace, zero-width spaces, etc.), ensure clean paragraph structure
  if (!text.trim() || text.replace(/[\u200B\u00A0]/g, '').trim() === '') {
    if (contentElement.value) {
      // Only recreate structure if we don't already have a proper empty paragraph
      const hasEmptyParagraph =
        contentElement.value.children.length === 1 &&
        contentElement.value.children[0].tagName === 'P' &&
        !contentElement.value.children[0].textContent
          ?.replace(/[\u200B\u00A0]/g, '')
          .trim()

      if (!hasEmptyParagraph) {
        // Clear all content and create a fresh paragraph
        const initialParagraph = document.createElement('p')
        initialParagraph.style.margin = '0'
        initialParagraph.style.lineHeight = '1.5'
        initialParagraph.innerHTML = '\u200B'
        contentElement.value.innerHTML = ''
        contentElement.value.appendChild(initialParagraph)

        // Set cursor in the paragraph
        nextTick(() => {
          if (contentElement.value && initialParagraph) {
            contentElement.value.focus()
            const range = document.createRange()
            const selection = window.getSelection()
            range.setStart(initialParagraph, 0)
            range.setEnd(initialParagraph, 0)
            selection?.removeAllRanges()
            selection?.addRange(range)
          }
        })
      }

      postContent.value = ''
    }
    // Hide popovers since there's no content
    showHashtagPopover.value = false
    showTokenPopover.value = false
    selectedIndex.value = 0
    return
  }

  // Only check for unwanted browser-generated HTML tags if there are problematic tags
  // Avoid interfering with normal typing
  if (contentElement.value && contentElement.value.innerHTML.includes('<')) {
    const innerHTML = contentElement.value.innerHTML
    // Only clean up if there are specifically problematic tags like <b>, <div>
    // Don't interfere with normal text input or paragraph tags (<p>) or strong tags (<strong>)
    if (
      innerHTML.includes('<b>') ||
      innerHTML.includes('<div>') ||
      innerHTML.includes('<i>') ||
      innerHTML.includes('<u>') ||
      innerHTML.includes('<span>') ||
      innerHTML.includes('<br>')
    ) {
      const cleanText = text
      // Only clean up if we don't already have proper paragraph structure
      if (cleanText.trim() && !innerHTML.includes('<p>')) {
        const currentPos = getCaretPosition()
        renderStyledContent(cleanText)
        // Restore cursor position after cleanup
        nextTick(() => {
          if (contentElement.value) {
            contentElement.value.focus()
            setCaretPositionSafe(currentPos)
          }
        })
      }
    }
  }

  // Enforce character limit
  if (text.length > MAX_CHARACTERS) {
    const truncatedText = text.slice(0, MAX_CHARACTERS)
    if (contentElement.value) {
      renderStyledContent(truncatedText)
    }
    postContent.value = truncatedText

    // Move cursor to end
    nextTick(() => {
      if (contentElement.value) {
        contentElement.value.focus()
        const range = document.createRange()
        const selection = window.getSelection()
        range.selectNodeContents(contentElement.value)
        range.collapse(false)
        selection?.removeAllRanges()
        selection?.addRange(range)
      }
    })
    return
  }

  postContent.value = text
  checkForTriggers(text)
}

const checkForTriggers = (text: string) => {
  const cursorPos = getCaretPosition()
  const beforeCursor = text.slice(0, cursorPos)

  const hashMatch = beforeCursor.match(/#(\w*)$/)
  const tokenMatch = beforeCursor.match(/\$(\w*)$/)

  if (hashMatch) {
    const newQuery = hashMatch[1]
    const wasAlreadyShowingHashtag = showHashtagPopover.value
    const previousQuery = currentQuery.value

    currentQuery.value = newQuery
    showHashtagPopover.value = true
    showTokenPopover.value = false

    // Only reset selectedIndex if this is a new hashtag trigger or query changed
    if (!wasAlreadyShowingHashtag || previousQuery !== newQuery) {
      selectedIndex.value = 0
    }

    // Small delay to ensure DOM is updated
    nextTick(() => updatePopoverPosition())
  } else if (tokenMatch) {
    const newQuery = tokenMatch[1]
    const wasAlreadyShowingToken = showTokenPopover.value
    const previousQuery = currentQuery.value

    currentQuery.value = newQuery
    showTokenPopover.value = true
    showHashtagPopover.value = false

    // Only reset selectedIndex if this is a new token trigger or query changed
    if (!wasAlreadyShowingToken || previousQuery !== newQuery) {
      selectedIndex.value = 0
    }

    // Small delay to ensure DOM is updated
    nextTick(() => updatePopoverPosition())
  } else {
    showHashtagPopover.value = false
    showTokenPopover.value = false
    selectedIndex.value = 0
  }
}

const getCaretPosition = () => {
  if (!contentElement.value) return 0
  const selection = window.getSelection()
  if (!selection?.rangeCount) return 0
  const range = selection.getRangeAt(0)
  const preCaretRange = range.cloneRange()
  preCaretRange.selectNodeContents(contentElement.value)
  preCaretRange.setEnd(range.endContainer, range.endOffset)
  return preCaretRange.toString().length
}

const updatePopoverPosition = () => {
  if (!contentElement.value || !postFormContainer.value) return

  const selection = window.getSelection()
  if (!selection?.rangeCount) return

  const range = selection.getRangeAt(0)
  const containerRect = postFormContainer.value.getBoundingClientRect()

  let caretRect: DOMRect

  try {
    // Get the caret position using Range.getBoundingClientRect()
    caretRect = range.getBoundingClientRect()

    // If the range has no dimensions, create a more accurate measurement
    if (caretRect.width === 0 && caretRect.height === 0) {
      // Create a temporary text node at the cursor position for measurement
      const tempRange = range.cloneRange()
      const tempNode = document.createTextNode('\u200b') // Zero-width space

      try {
        tempRange.insertNode(tempNode)
        caretRect = tempRange.getBoundingClientRect()

        // Clean up immediately
        tempNode.remove()

        // Restore the original selection
        selection.removeAllRanges()
        selection.addRange(range)
      } catch (e) {
        // If insertion fails, fall back to the contenteditable element bounds
        tempNode.remove()
        caretRect = contentElement.value.getBoundingClientRect()
        caretRect = new DOMRect(caretRect.left, caretRect.bottom - 20, 0, 20)
      }
    }
  } catch (error) {
    // Fallback: use contenteditable element position
    const contentRect = contentElement.value.getBoundingClientRect()
    caretRect = new DOMRect(contentRect.left, contentRect.bottom - 20, 0, 20)
  }

  // Calculate position relative to the form container
  let x = caretRect.left - containerRect.left
  let y = caretRect.bottom - containerRect.top + 2 // Small gap below caret

  // Popover dimensions for bounds checking
  const popoverWidth = 200
  const popoverHeight = 150
  const padding = 8

  // Right boundary check
  if (x + popoverWidth > containerRect.width - padding) {
    x = containerRect.width - popoverWidth - padding
  }

  // Left boundary check
  if (x < padding) {
    x = padding
  }

  // Bottom boundary check - if popover would overflow, show above caret
  if (y + popoverHeight > containerRect.height - padding) {
    y = caretRect.top - containerRect.top - popoverHeight - 2
  }

  // Top boundary check
  if (y < padding) {
    y = padding
  }

  popoverPosition.value = { x, y }
}

const renderStyledContent = (content: string) => {
  if (!contentElement.value) return

  // Clear the content
  contentElement.value.innerHTML = ''

  // Split content by line breaks and create paragraph elements
  const lines = content.split('\n')

  lines.forEach(line => {
    if (!contentElement.value) return

    // Create a paragraph element for each line
    const paragraph = document.createElement('p')
    paragraph.style.margin = '0' // Remove default paragraph margins
    paragraph.style.lineHeight = '1.5'

    // If the line is empty, add a zero-width space to maintain structure
    if (!line.trim()) {
      paragraph.innerHTML = '\u200B' // Zero-width space to maintain paragraph height
      contentElement.value.appendChild(paragraph)
      return
    }

    // Split each line into parts and style hashtags/tokens within the paragraph
    const parts = line.split(/(\#\w+|\$\w+)/)

    parts.forEach(part => {
      if (part.match(/^#\w+$/)) {
        // Create a bold hashtag
        const strong = document.createElement('strong')
        strong.textContent = part
        paragraph.appendChild(strong)

        // Add a zero-width space after the strong element to prevent cursor trapping
        const separator = document.createTextNode('\u200B')
        paragraph.appendChild(separator)
      } else if (part.match(/^\$\w+$/)) {
        // Create a bold token
        const strong = document.createElement('strong')
        strong.textContent = part
        paragraph.appendChild(strong)

        // Add a zero-width space after the strong element to prevent cursor trapping
        const separator = document.createTextNode('\u200B')
        paragraph.appendChild(separator)
      } else if (part) {
        // Regular text
        const textNode = document.createTextNode(part)
        paragraph.appendChild(textNode)
      }
    })

    contentElement.value.appendChild(paragraph)
  })
}

const setCaretPositionSafe = (pos: number) => {
  if (!contentElement.value) return

  const selection = window.getSelection()
  if (!selection) return

  // Get all text nodes
  const textNodes: Text[] = []
  const walker = document.createTreeWalker(
    contentElement.value,
    NodeFilter.SHOW_TEXT,
    null
  )

  let node
  while ((node = walker.nextNode())) {
    textNodes.push(node as Text)
  }

  // Find the position across all text nodes
  let currentPos = 0
  let targetNode: Text | null = null
  let targetOffset = 0

  for (const textNode of textNodes) {
    const textLength = textNode.textContent?.length || 0

    if (currentPos + textLength >= pos) {
      targetNode = textNode
      targetOffset = pos - currentPos
      break
    }

    currentPos += textLength
  }

  // If we can't find the exact position, place cursor after the last non-zero-width text node
  if (!targetNode) {
    // Find the last meaningful text node (not just zero-width spaces)
    for (let i = textNodes.length - 1; i >= 0; i--) {
      const node = textNodes[i]
      if (node.textContent && node.textContent !== '\u200B') {
        targetNode = node
        targetOffset = node.textContent.length
        break
      }
    }

    // If still no target, create a new text node at the end
    if (!targetNode) {
      const newTextNode = document.createTextNode('')
      contentElement.value.appendChild(newTextNode)
      targetNode = newTextNode
      targetOffset = 0
    }
  }

  try {
    const range = document.createRange()
    range.setStart(targetNode, targetOffset)
    range.setEnd(targetNode, targetOffset)

    selection.removeAllRanges()
    selection.addRange(range)
  } catch (error) {
    // Fallback: place cursor at the very end
    const range = document.createRange()
    range.selectNodeContents(contentElement.value)
    range.collapse(false)
    selection.removeAllRanges()
    selection.addRange(range)
  }
}

const insertMention = (type: 'hashtag' | 'token', id: number) => {
  if (!contentElement.value) return

  const selection = window.getSelection()
  if (!selection?.rangeCount) return

  const triggerChar = type === 'hashtag' ? '#' : '$'

  // Get the selected item
  const items =
    type === 'hashtag' ? filteredHashtags.value : filteredTokens.value
  const selectedItem = items[selectedIndex.value]
  if (!selectedItem) return

  // Create the mention text - tokens have symbol, hashtags use name
  const mentionText =
    type === 'hashtag'
      ? `#${selectedItem.name}`
      : `$${(selectedItem as any).symbol || selectedItem.name}`

  // Find the trigger character position by walking backwards from cursor
  const text = postContent.value
  const cursorPos = getCaretPosition()
  const beforeCursor = text.slice(0, cursorPos)
  const lastTriggerIndex = beforeCursor.lastIndexOf(triggerChar)

  if (lastTriggerIndex === -1) return

  // Calculate how many characters to delete (trigger + query)
  const queryLength = cursorPos - lastTriggerIndex - 1
  const totalDeleteLength = queryLength + 1 // +1 for the trigger character

  // Find the actual DOM position for the trigger character
  let currentPos = 0
  let targetNode: Text | null = null
  let targetOffset = 0

  const textNodes: Text[] = []
  const walker = document.createTreeWalker(
    contentElement.value,
    NodeFilter.SHOW_TEXT,
    null
  )

  let node
  while ((node = walker.nextNode())) {
    textNodes.push(node as Text)
  }

  // Find the text node containing the trigger character
  for (const textNode of textNodes) {
    const textLength = textNode.textContent?.length || 0

    if (currentPos + textLength >= lastTriggerIndex) {
      targetNode = textNode
      targetOffset = lastTriggerIndex - currentPos
      break
    }

    currentPos += textLength
  }

  if (!targetNode) return

  try {
    // Create range to select the trigger + query text
    const deleteRange = document.createRange()
    deleteRange.setStart(targetNode, targetOffset)

    // Find the end position for deletion
    let endPos = 0
    let endNode: Text | null = null
    let endOffset = 0

    for (const textNode of textNodes) {
      const textLength = textNode.textContent?.length || 0

      if (endPos + textLength >= lastTriggerIndex + totalDeleteLength) {
        endNode = textNode
        endOffset = lastTriggerIndex + totalDeleteLength - endPos
        break
      }

      endPos += textLength
    }

    if (endNode) {
      deleteRange.setEnd(endNode, endOffset)
    } else {
      // Fallback to end of target node
      deleteRange.setEnd(targetNode, targetNode.textContent?.length || 0)
    }

    // Delete the selected text
    deleteRange.deleteContents()

    // Create the mention element
    const strong = document.createElement('strong')
    strong.textContent = mentionText

    // Insert the mention
    deleteRange.insertNode(strong)

    // Add a space after the mention
    const spaceNode = document.createTextNode(' ')
    strong.parentNode?.insertBefore(spaceNode, strong.nextSibling)

    // Add zero-width space after the strong element to prevent cursor trapping
    const separator = document.createTextNode('\u200B')
    spaceNode.parentNode?.insertBefore(separator, spaceNode.nextSibling)

    // Position cursor after the space
    const newRange = document.createRange()
    newRange.setStartAfter(spaceNode)
    newRange.setEndAfter(spaceNode)
    selection.removeAllRanges()
    selection.addRange(newRange)

    // Update the text content model
    postContent.value = getTextContentWithLineBreaks()
  } catch (error) {
    console.error('Error inserting mention:', error)
    // Fallback to the old method
    const newContent =
      beforeCursor.slice(0, lastTriggerIndex) +
      mentionText +
      ' ' +
      text.slice(cursorPos)
    renderStyledContent(newContent)
    postContent.value = newContent
  }

  showHashtagPopover.value = false
  showTokenPopover.value = false
  selectedIndex.value = 0
}

const setCaretPosition = (pos: number) => {
  // Use the safer version
  setCaretPositionSafe(pos)
}

const filteredHashtags = computed(() => {
  if (!currentQuery.value) return posts.hashtags
  return posts.hashtags.filter(h =>
    h.name.toLowerCase().includes(currentQuery.value.toLowerCase())
  )
})

const filteredTokens = computed(() => {
  if (!currentQuery.value) return posts.tokens
  return posts.tokens.filter(
    t =>
      t.symbol.toLowerCase().includes(currentQuery.value.toLowerCase()) ||
      t.name.toLowerCase().includes(currentQuery.value.toLowerCase())
  )
})

const characterProgress = computed(() => {
  const progress = (postContent.value.length / MAX_CHARACTERS) * 100
  return Math.min(progress, 100)
})

const isCharacterLimitExceeded = computed(() => {
  return postContent.value.length > MAX_CHARACTERS
})

const createPost = async () => {
  if (!postContent.value.trim() && selectedImages.value.length === 0) return

  try {
    if (props.isReply && props.postId) {
      // Handle reply creation
      const { addReply } = usePostInteractions()
      const result = await addReply(props.postId, postContent.value.trim())

      if (result.success && 'data' in result && result.data) {
        emit('reply-added', (result as any).data.reply)
      }
    } else {
      // Handle regular post creation
      await posts.createPost(postContent.value, selectedImages.value)
    }

    // Reset form
    isFocused.value = false
    postContent.value = ''

    // Clean up image URLs before clearing array
    selectedImages.value.forEach(file => cleanupImageUrl(file))
    selectedImages.value = []
    if (contentElement.value) {
      // Clear HTML structure completely and reset to initial paragraph
      const initialParagraph = document.createElement('p')
      initialParagraph.style.margin = '0'
      initialParagraph.style.lineHeight = '1.5'
      initialParagraph.innerHTML = '\u200B'
      contentElement.value.innerHTML = ''
      contentElement.value.appendChild(initialParagraph)
    }
  } catch (error) {
    console.error('Failed to create post:', error)
  }
}
</script>

<template>
  <div
    ref="postFormContainer"
    :class="{
      focused: isFocused,
      'border-white/10 shadow-none drop-shadow-sm': isFocused
    }"
    class="post-form-container scrollbar flex flex-col border border-black/50 bg-gray-50/5 w-full rounded shadow cursor-pointer hover:border-white/10 hover:shadow-none hover:drop-shadow-sm transition-all duration-100 relative"
    @click="isFocused = true"
  >
    <h3 v-show="!isFocused" class="text-muted font-medium flex-1 p-4">
      {{ placeholder }}
    </h3>
    <div
      v-show="isFocused"
      ref="contentElement"
      class="max-h-[80vh] flex-1 outline-none content p-4 tracking-wide overflow-y-auto"
      :contenteditable="isFocused"
      @keydown="handleKeydown"
      @input="handleInput"
      @keyup="handleInput"
      @click="handleInput"
      @paste="handlePaste"
    />

    <!-- Image Previews -->
    <div
      v-if="selectedImages.length > 0 && (isFocused || shouldShowBottomBar)"
      class="p-4 pt-0"
    >
      <div class="flex flex-wrap gap-2">
        <div
          v-for="imagePreview in imagePreviewsWithUrls"
          :key="imagePreview.index"
          class="relative group"
        >
          <img
            :src="imagePreview.url"
            :alt="`Preview ${imagePreview.index + 1}`"
            class="w-20 h-20 object-cover rounded border border-gray-600"
          />
          <button
            class="absolute -top-1 -right-1 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs opacity-0 group-hover:opacity-100 transition-opacity"
            @click="removeImage(imagePreview.index)"
          >
            ×
          </button>
        </div>
      </div>
    </div>

    <!-- Hidden file input -->
    <input
      ref="fileInput"
      type="file"
      multiple
      accept="image/*"
      class="hidden"
      @change="handleFileSelect"
    />

    <!-- Hashtag Popover -->
    <div
      v-if="showHashtagPopover"
      class="absolute z-50 bg-primary-bg border border-gray-600 rounded-lg shadow-lg p-2 min-w-[200px]"
      :style="{
        left: popoverPosition.x + 'px',
        top: popoverPosition.y + 'px'
      }"
    >
      <div class="text-xs text-gray-400 mb-2">Hashtags</div>
      <div class="space-y-1">
        <button
          v-for="(hashtag, index) in filteredHashtags"
          :key="hashtag.id"
          :class="{
            'bg-gray-700': index === selectedIndex,
            'hover:bg-gray-700': index !== selectedIndex
          }"
          class="w-full text-left px-2 py-1 rounded text-sm text-white flex items-center"
          @click="insertMention('hashtag', hashtag.id)"
        >
          <span class="text-blue-400">#</span>{{ hashtag.name }}
        </button>
      </div>
    </div>

    <!-- Token Popover -->
    <div
      v-if="showTokenPopover"
      class="absolute z-50 bg-primary-bg border border-gray-600 rounded-lg shadow-lg p-2 min-w-[200px]"
      :style="{ left: popoverPosition.x + 'px', top: popoverPosition.y + 'px' }"
    >
      <div class="text-xs text-gray-400 mb-2">Tokens</div>
      <div class="space-y-1">
        <button
          v-for="(token, index) in filteredTokens"
          :key="token.id"
          :class="{
            'bg-gray-700': index === selectedIndex,
            'hover:bg-gray-700': index !== selectedIndex
          }"
          class="w-full text-left px-2 py-1 rounded text-sm text-white flex items-center"
          @click="insertMention('token', token.id)"
        >
          <span class="text-green-400">$</span>{{ token.symbol }}
          <span class="text-gray-400 ml-2">{{ token.name }}</span>
        </button>
      </div>
    </div>

    <div
      v-if="shouldShowBottomBar"
      class="flex items-center p-2 px-3 justify-between"
    >
      <!-- left toolbar -->
      <div>
        <button
          class="text-secondary-bg size-4 hover:text-primary-btn-bg cursor-pointer rounded-full flex items-center justify-center"
          @click="handleImageButtonClick"
        >
          <Icon name="lucide:image" size="16" />
        </button>
      </div>

      <!-- right toolbar -->
      <div class="flex items-center gap-2">
        <!-- Character counter circle -->
        <div class="relative w-5 h-5">
          <svg class="w-5 h-5 transform -rotate-90" viewBox="0 0 24 24">
            <!-- Background circle -->
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="3"
              fill="none"
              class="text-gray-600"
            />
            <!-- Progress circle -->
            <circle
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              stroke-width="3"
              fill="none"
              stroke-linecap="round"
              :stroke-dasharray="63"
              :stroke-dashoffset="63 - (characterProgress * 63) / 100"
              :class="
                isCharacterLimitExceeded
                  ? 'text-red-500'
                  : 'text-primary-btn-bg'
              "
              class="transition-all duration-200"
            />
          </svg>
        </div>

        <button
          :disabled="
            (!postContent.trim() && selectedImages.length === 0) ||
            posts.isCreating ||
            isCharacterLimitExceeded
          "
          class="text-xs bg-primary-btn-bg px-2 py-1 rounded font-bold tracking-wide cursor-pointer disabled:opacity-50"
          @click="createPost"
        >
          {{ posts.isCreating ? 'Posting...' : buttonText }}
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.post-form-container {
  height: 60px;
}

.post-form-container.focused {
  height: auto;
  min-height: 150px;
  max-height: 80vh;
}
</style>
