<script setup lang="ts">
import ImageInput from '../form/ImageInput.vue'
import TextArea from '../form/TextArea.vue'
import TextInput from '../form/TextInput.vue'
import {
  CollapsibleContent,
  CollapsibleRoot,
  CollapsibleTrigger
} from 'reka-ui'

interface CreateTokenResponse {
  success: boolean
  token: {
    id: number
    name: string
    symbol: string
    description?: string
    image?: string
    twitter?: string
    telegram?: string
    website?: string
    instagram?: string
    discord?: string
    mintAddress: string
    tokenCount: number
    creator: {
      id: number
      name: string
      address: string
    }
    onChainId: number
    createdAt: string
  }
}

const coinData = reactive<{
  name: string
  symbol: string
  description: string
  image: File | null
  socials: {
    twitter: string
    telegram: string
    website: string
    instagram: string
    discord: string
  }
}>({
  name: '',
  symbol: '',
  description: '',
  image: null,
  socials: {
    twitter: '',
    telegram: '',
    website: '',
    instagram: '',
    discord: ''
  }
})

const errors = reactive<{
  name: string | null
  symbol: string | null
  description: string | null
  general: string | null
}>({
  name: null,
  symbol: null,
  description: null,
  general: null
})

const isSubmitting = ref(false)
const successMessage = ref('')
const isFormValid = ref(true)

// Expose the submitting state for parent components
defineExpose({
  isSubmitting: readonly(isSubmitting)
})

// Validation functions
const validateName = () => {
  if (!coinData.name.trim()) {
    errors.name = 'Token name is required'
    return false
  }
  if (coinData.name.length > 50) {
    errors.name = 'Token name must be less than 50 characters'
    return false
  }
  errors.name = ''
  return true
}

const validateSymbol = () => {
  if (!coinData.symbol.trim()) {
    errors.symbol = 'Token symbol is required'
    return false
  }
  if (coinData.symbol.length > 10) {
    errors.symbol = 'Token symbol must be less than 10 characters'
    return false
  }
  if (!/^[A-Za-z0-9]+$/.test(coinData.symbol)) {
    errors.symbol = 'Token symbol can only contain letters and numbers'
    return false
  }
  errors.symbol = ''
  return true
}

const validateDescription = () => {
  if (coinData.description && coinData.description.length > 200) {
    errors.description = 'Description must be less than 200 characters'
    return false
  }
  errors.description = ''
  return true
}

const validateForm = () => {
  const isNameValid = validateName()
  const isSymbolValid = validateSymbol()
  const isDescriptionValid = validateDescription()

  isFormValid.value = isNameValid && isSymbolValid && isDescriptionValid
  return isFormValid.value
}

const resetForm = () => {
  coinData.name = ''
  coinData.symbol = ''
  coinData.description = ''
  coinData.image = null
  coinData.socials.twitter = ''
  coinData.socials.telegram = ''
  coinData.socials.website = ''
  coinData.socials.instagram = ''
  coinData.socials.discord = ''
  errors.name = ''
  errors.symbol = ''
  errors.description = ''
  errors.general = ''
}

const submit = async () => {
  if (!validateForm()) {
    return
  }

  isSubmitting.value = true
  errors.general = ''
  successMessage.value = ''

  try {
    // Create FormData for multipart form submission
    const formData = new FormData()
    formData.append('name', coinData.name.trim())
    formData.append('symbol', coinData.symbol.trim().toUpperCase())
    formData.append(
      'description',
      coinData.description.trim() || `${coinData.name.trim()} token`
    )

    // Add image if provided
    if (coinData.image) {
      formData.append('image', coinData.image)
    }

    // Add socials
    formData.append('twitter', coinData.socials.twitter.trim())
    formData.append('telegram', coinData.socials.telegram.trim())
    formData.append('website', coinData.socials.website.trim())
    formData.append('instagram', coinData.socials.instagram.trim())
    formData.append('discord', coinData.socials.discord.trim())

    const response = await $fetch<CreateTokenResponse>('/api/fin/new-coin', {
      method: 'POST',
      body: formData
    })

    if (response.success) {
      successMessage.value = `Token ${response.token.symbol} created successfully!...`
      resetForm()

      // Emit success event if parent component wants to handle it
      emit('tokenCreated', response.token)
    }
  } catch (error: any) {
    console.error('Error creating token:', error)

    if (error.statusCode === 400) {
      errors.general = error.statusMessage || 'Invalid input data'
    } else if (error.statusCode === 500) {
      errors.general = error.message || 'Failed to create token on blockchain'
    } else {
      errors.general = 'Failed to create token. Please try again.'
    }
  } finally {
    isSubmitting.value = false
  }
}

const emit = defineEmits<{
  tokenCreated: [token: CreateTokenResponse['token']]
}>()

// Auto-uppercase symbol as user types
watch(
  () => coinData.symbol,
  newSymbol => {
    coinData.symbol = newSymbol.toUpperCase()
  }
)

// Real-time validation
watch(() => coinData.name, validateName)
watch(() => coinData.symbol, validateSymbol)
watch(() => coinData.description, validateDescription)
</script>

<template>
  <form class="space-y-4" @submit.prevent="submit">
    <!-- Success Message -->
    <div
      v-if="successMessage"
      class="p-3 bg-green-500/20 border border-green-500 rounded-md"
    >
      <p class="text-green-400 text-sm">{{ successMessage }}</p>
    </div>

    <!-- General Error -->
    <div
      v-if="errors.general"
      class="p-3 bg-red-500/20 border border-red-500 rounded-md"
    >
      <p class="text-red-400 text-sm">{{ errors.general }}</p>
    </div>

    <!-- Token Name and Symbol -->
    <div class="flex w-full gap-2">
      <div class="flex-1">
        <label for="token-name">Name</label>
        <TextInput
          id="token-name"
          v-model="coinData.name"
          class="w-full"
          placeholder="Token name"
          :error="errors.name"
          :disabled="isSubmitting"
        />
      </div>
      <div class="flex-1">
        <label for="token-symbol">Symbol</label>
        <TextInput
          id="token-symbol"
          v-model="coinData.symbol"
          class="w-full"
          placeholder="SYMBOL"
          :error="errors.symbol"
          :disabled="isSubmitting"
        />
      </div>
    </div>

    <!-- Description -->
    <div>
      <label for="token-description">Description</label>
      <TextArea
        id="token-description"
        v-model="coinData.description"
        class="w-full"
        placeholder="Token description"
        :error="errors.description"
        :disabled="isSubmitting"
      />
    </div>

    <!-- Image Upload -->
    <div>
      <label for="image-upload">Image</label>
      <ImageInput
        v-model="coinData.image"
        class="w-full"
        placeholder="Token image"
        :disabled="isSubmitting"
      />
    </div>

    <CollapsibleRoot v-slot="{ open }">
      <CollapsibleTrigger>
        <button
          class="link text-sm flex items-center gap-1 font-bold cursor-pointer"
        >
          Socials
          <Icon v-if="open" name="lucide:chevron-up" />
          <Icon v-else name="lucide:chevron-down" />
        </button>
      </CollapsibleTrigger>
      <CollapsibleContent>
        <div class="flex flex-col gap-2 mt-2">
          <TextInput
            v-model="coinData.socials.twitter"
            placeholder="X handle"
            :disabled="isSubmitting"
          />
          <TextInput
            v-model="coinData.socials.telegram"
            placeholder="Telegram handle"
            :disabled="isSubmitting"
          />
          <TextInput
            v-model="coinData.socials.website"
            placeholder="Website URL"
            :disabled="isSubmitting"
          />
          <TextInput
            v-model="coinData.socials.instagram"
            placeholder="Instagram username"
            :disabled="isSubmitting"
          />
          <TextInput
            v-model="coinData.socials.discord"
            placeholder="Discord invite"
            :disabled="isSubmitting"
          />
        </div>
      </CollapsibleContent>
    </CollapsibleRoot>

    <!-- Submit Button -->
    <div>
      <button
        type="submit"
        :disabled="isSubmitting || !isFormValid"
        class="w-full bg-green-500 hover:bg-green-400/90 disabled:bg-gray-500 disabled:cursor-not-allowed text-white font-semibold py-2 px-4 rounded-md transition-colors"
      >
        <span v-if="isSubmitting">Creating Token on Blockchain...</span>
        <span v-else>Create Token</span>
      </button>
    </div>

    <!-- Helper Text -->
    <div class="text-xs text-muted">
      <p>
        Make sure your token info are correct, name, symbol and image cannot be
        changed later.
      </p>
    </div>
  </form>
</template>
