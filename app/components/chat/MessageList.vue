<script setup></script>

<template>
  <div class="flex-1 flex flex-col p-4 space-y-2 overflow-y-auto scrollbar">
    <div class="w-full">
      <div class="msg msg-received">Lorem ipsum,</div>
      <div class="text-xs text-muted flex items-center gap-1">
        <span>20/21/20</span>
      </div>
    </div>

    <div class="w-full me-auto">
      <div class="msg msg-received">
        Lorem ipsum dolor, sit amet consectetur adipisicing elit. Cupiditate
        impedit consequatur aliquid unde dolore laboriosam voluptatem, aliquam
        at repellat explicabo aspernatur dolores maiores adipisci, laborum dicta
        officiis nam accusamus nisi.
      </div>
      <div class="text-xs text-muted flex items-center gap-1">
        <span>20/21/20</span>
      </div>
    </div>
    <div class="w-full me-auto">
      <div class="msg msg-sent">
        Lorem ipsum dolor, sit amet consectetur adipisicing elit. Cupiditate
        impedit consequatur aliquid unde dolore laboriosam voluptatem, aliquam
        at repellat explicabo aspernatur dolores maiores adipisci, laborum dicta
        officiis nam accusamus nisi.
      </div>
      <div class="text-xs text-muted flex items-center gap-1">
        <span>20/21/20 seen</span>
        <span class="text-blue-500">
          <Icon name="ri:check-double-fill" size="16" />
        </span>
      </div>
    </div>
    <div class="w-full me-auto">
      <div class="msg msg-sent">
        Lorem ipsum dolor, sit amet consectetur adipisicing elit. Cupiditate
        impedit consequatur aliquid unde dolore laboriosam voluptatem, aliquam
        at repellat explicabo aspernatur dolores maiores adipisci, laborum dicta
        officiis nam accusamus nisi.
      </div>
      <div class="text-xs text-muted flex items-center gap-1">
        <span>20/21/20 seen</span>
        <span class="text-blue-500">
          <Icon name="ri:check-double-fill" size="16" />
        </span>
      </div>
    </div>
    <div class="w-full me-auto">
      <div class="msg msg-sent">
        Lorem ipsum dolor, sit amet consectetur adipisicing elit. Cupiditate
        impedit consequatur aliquid unde dolore laboriosam voluptatem, aliquam
        at repellat explicabo aspernatur dolores maiores adipisci, laborum dicta
        officiis nam accusamus nisi.
      </div>
      <div class="text-xs text-muted flex items-center gap-1">
        <span>20/21/20 seen</span>
        <span class="text-blue-500">
          <Icon name="ri:check-double-fill" size="16" />
        </span>
      </div>
    </div>
    <div class="w-full me-auto">
      <div class="msg msg-sent">
        Lorem ipsum dolor, sit amet consectetur adipisicing elit. Cupiditate
        impedit consequatur aliquid unde dolore laboriosam voluptatem, aliquam
        at repellat explicabo aspernatur dolores maiores adipisci, laborum dicta
        officiis nam accusamus nisi.
      </div>
      <div class="text-xs text-muted flex items-center gap-1">
        <span>20/21/20 seen</span>
        <span class="text-blue-500">
          <Icon name="ri:check-double-fill" size="16" />
        </span>
      </div>
    </div>

    <div class="w-full">
      <div class="msg msg-sent">Lorem ipsum dolor, sit amet</div>
      <div class="text-xs text-muted flex items-center gap-1">
        <span>20/21/20 seen</span>
        <span class="text-blue-500">
          <Icon name="ri:check-double-fill" size="16" />
        </span>
      </div>
    </div>
    <div class="w-full">
      <div class="msg msg-received">Lorem ipsum,</div>
      <div class="text-xs text-muted flex items-center gap-1">
        <span>20/21/20</span>
      </div>
    </div>
  </div>
</template>

<style scoped>
@reference "tailwindcss";
@reference "@/assets/css/main.css";

.msg {
  @apply relative max-w-[70%] p-2 rounded-lg font-medium;
}

/* chat bubble triangle for sent messages */
.msg-sent::after {
  @apply content-[''] absolute border-t-12 border-t-blue-500 border-x-12 border-x-transparent border-b-0;
  right: 0;
  bottom: 50%;
  transform: translate(50%, 50%) rotate(-90deg);
}

.msg-sent {
  @apply bg-blue-500;
}

/* chat bubble triangle for received messages */
.msg-received::before {
  @apply content-[''] absolute border-t-12 border-t-gray-200 border-x-12 border-x-transparent border-b-0;
  left: 0;
  bottom: 50%;
  transform: translate(-50%, 50%) rotate(90deg);
}

.msg-received {
  @apply bg-gray-200 text-fg-dark;
}

div:has(.msg-sent) {
  @apply flex flex-col items-end gap-1;
}

div:has(.msg-received) {
  @apply flex flex-col items-start gap-1;
}
</style>
