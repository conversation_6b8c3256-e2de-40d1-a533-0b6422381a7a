<script setup lang="ts">
import type { PostWithRelationAndCounts } from '~/types/database'

interface Props {
  post?: PostWithRelationAndCounts | null
}

const props = withDefaults(defineProps<Props>(), {
  post: undefined
})

const router = useRouter()

const handleReplyClick = () => {
  if (!props.post) return
  router.push(`/post/${props.post.id}`)
}
</script>

<template>
  <div class="fixed top-0 right-0 z-[9999] hidden lg:block">
    <div class="w-[400px] bg-primary-bg h-screen border-l border-gray-100/5">
      <div
        class="flex flex-col border-b border-gray-100/5 p-4 gap-4 hover:bg-white/[1%] cursor-pointer"
      >
        <div class="flex items-center gap-3">
          <UserAvatar :user="post?.user" size="sm" />
          <div class="flex-1 space-y-px text-sm">
            <div class="rounded w-auto font-bold">
              {{ post?.user?.displayName || post?.user?.name }}
            </div>
            <div
              class="flex items-center gap-1 text-xs font-semibold text-muted"
            >
              <div>{{ post?.user?.address?.slice(0, 8) }}...</div>
              <div class="w-1 h-1 bg-muted rounded-full text-muted" />
              <div>
                {{ formatTime(post?.createdAt.toString() || '01/03/2000') }}
              </div>
            </div>
          </div>
        </div>
        <PostParsedContent :content="post?.content ?? ''" />

        <PostInteractions
          v-if="post"
          :post-id="post?.id"
          @reply-click="handleReplyClick"
        />

        <PostReplies />
      </div>
    </div>
  </div>
</template>
