<script setup lang="ts">
import mg from '~/utils/tailwind'

interface Props {
  content: string
  class?: string
}

const props = withDefaults(defineProps<Props>(), {
  class: ''
})

const posts = usePostsStore()
const parsedContent = computed(() => {
  return posts.parseContent(props.content)
})

const renderContent = (content: string) => {
  return content
    .replace(
      /#(\w+)/g,
      '<span class="text-blue-400 font-semibold cursor-pointer hover:underline">#$1</span>'
    )
    .replace(
      /\$(\w+)/g,
      '<span class="text-green-400 font-semibold cursor-pointer hover:underline">$$$1</span>'
    )
    .replace(
      /@(\w+)/g,
      '<span class="text-purple-400 font-semibold cursor-pointer hover:underline">@$1</span>'
    )
}
</script>

<template>
  <div
    :class="mg('max-h-48', $props.class!)"
    v-html="renderContent(parsedContent)"
  />
</template>
