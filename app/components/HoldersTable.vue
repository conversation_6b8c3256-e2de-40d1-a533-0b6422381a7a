<script setup lang="ts">
import { useRoute } from 'vue-router'

const props = defineProps<{
  tokenInfo: TokenInfo | null
}>()
const route = useRoute()
const address = route.params.address as string
const { data } = await useFetch(`/api/fin/holders/${address}?limit=10`, {
  cache: 'default',
  watch: [() => props.tokenInfo]
})

// 1k, 1M, 1B, etc. formatting for token amounts
const formatTokenAmount = (amount: number): string => {
  if (amount >= 1e9) {
    return `${(amount / 1e9).toFixed(2)}B`
  } else if (amount >= 1e6) {
    return `${(amount / 1e6).toFixed(2)}M`
  } else if (amount >= 1e3) {
    return `${(amount / 1e3).toFixed(2)}K`
  }
  return amount.toString()
}
</script>

<template>
  <div class="space-y-3">
    <div class="overflow-x-auto">
      <table class="w-full">
        <thead>
          <tr class="border-b border-gray-100/10">
            <th class="text-left py-2 px-2 text-xs font-semibold text-muted">
              Rank
            </th>
            <th class="text-left py-2 px-2 text-xs font-semibold text-muted">
              Holder
            </th>
            <th class="text-left py-2 px-2 text-xs font-semibold text-muted">
              Balance
            </th>
            <th class="text-left py-2 px-2 text-xs font-semibold text-muted">
              Share
            </th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="(holder, index) in data?.holders"
            :key="holder.id"
            class="border-b border-gray-100/5 hover:bg-gray-50/5"
          >
            <td class="py-2 px-2">
              <div
                class="flex items-center justify-center w-5 h-5 rounded-full bg-gray-100/10 text-xs font-bold"
              >
                {{ index + 1 }}
              </div>
            </td>
            <td class="py-2 px-2">
              <div class="flex items-center gap-2">
                <div
                  class="w-5 h-5 rounded-full bg-secondary-bg flex items-center justify-center"
                >
                  <span class="text-white font-bold text-xs">{{
                    holder.user.name.charAt(0)
                  }}</span>
                </div>
                <div>
                  <div class="font-semibold text-xs">
                    {{ holder.user.name }}
                  </div>
                  <div class="text-xs text-muted">
                    {{ holder.user.address.slice(0, 6) }}...
                    {{ holder.user.address.slice(-4) }}
                  </div>
                </div>
              </div>
            </td>
            <td class="py-2 px-2 font-semibold text-xs">
              {{ formatTokenAmount(parseFloat(holder.balance)) }}
            </td>
            <td class="py-2 px-2">
              <div class="flex items-center gap-2">
                <span class="text-xs font-semibold">{{
                  holder.percentage
                }}</span>
                <div class="flex-1 h-1.5 bg-gray-100/10 rounded-full max-w-12">
                  <div
                    class="h-full bg-blue-500 rounded-full"
                    :style="{ width: holder.percentage }"
                  />
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- <div class="text-center pt-2">
      <button class="text-xs text-blue-400 hover:text-blue-300 font-semibold">
        View all
      </button>
    </div> -->
  </div>
</template>
