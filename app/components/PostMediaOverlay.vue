<script setup lang="ts">
const posts = usePostsStore()
const curPost = computed(() => posts.currentSelectedPost)
const selectedImageIndex = computed(() => posts.selectedImageIndex)

onMounted(() => {
  document.body.classList.add('overflow-hidden')
})

onUnmounted(() => {
  document.body.classList.remove('overflow-hidden')
})

const handleClose = () => {
  posts.postOverlayVisible = false
}

const nextImage = () => {
  if (
    curPost.value?.media &&
    posts.selectedImageIndex < curPost.value.media.length - 1
  ) {
    posts.selectedImageIndex += 1
  }
}

const prevImage = () => {
  if (posts.selectedImageIndex > 0) {
    posts.selectedImageIndex -= 1
  }
}
</script>

<template>
  <div
    class="fixed top-0 left-0 z-[999] h-screen w-screen bg-black/50 backdrop-blur-lg"
    @click.stop="handleClose"
  />

  <button
    class="fixed z-[9999] top-0 left-0 flex items-center justify-center w-7 h-7 rounded-full hover:bg-secondary-bg m-2"
    @click="handleClose"
  >
    <Icon name="lucide:x" size="20" />
  </button>

  <div
    class="fixed top-1/2 left-1/2 -translate-x-1/2 lg:left-[calc((100vw-500px)/2)] lg:-translate-x-1/2 z-[9999] min-w-[350px] h-fit max-w-[550px] -translate-y-1/2"
  >
    <img
      :key="selectedImageIndex"
      draggable="false"
      :src="curPost?.media![selectedImageIndex]?.url"
      alt=""
    />
    <!-- Navigation buttons -->
    <button
      v-if="curPost?.media && selectedImageIndex > 0"
      class="absolute flex left-2 top-1/2 -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full"
      @click="prevImage"
    >
      <Icon name="lucide:chevron-left" size="24" />
    </button>
    <button
      v-if="curPost?.media && selectedImageIndex < curPost.media.length - 1"
      class="absolute flex right-2 top-1/2 -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full"
      @click="nextImage"
    >
      <Icon name="lucide:chevron-right" size="24" />
    </button>
  </div>

  <PostOverlayContent :post="curPost" />
</template>
