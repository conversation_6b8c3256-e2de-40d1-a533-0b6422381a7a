<script setup lang="ts">
import { NavButton } from '~/components/button'
import CreateCoinPopup from './popups/CreateCoinPopup.vue'

const layout = useLayout()
const { loggedIn, user, fetch: refreshSession } = useUserSession()
const notificationsStore = useNotificationsStore()
const logout = async () => {
  if (loggedIn.value) {
    await $fetch('/api/auth/logout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    })

    refreshSession()
  }
}
</script>

<template>
  <aside
    class="h-screen sticky top-0 border-gray-100/5 flex-shrink-0 border-r p-4 hidden md:flex md:flex-col"
    :class="{
      'max-w-xs w-full': layout.isFullLeftSidebar,
      'max-w-16 w-16 items-center': !layout.isFullLeftSidebar
    }"
  >
    <Header />
    <!-- Main Nav -->
    <ul class="pt-7 space-y-2">
      <li>
        <CreateCoinPopup />
      </li>
      <li>
        <hr class="border-primary-bg/20" />
      </li>
      <li>
        <NavButton href="/" class="flex items-center gap-2">
          <template #icon="{ size }">
            <Icon name="lucide:home" :size="size" />
          </template>
          <template #text>
            <span>Community</span>
          </template>
        </NavButton>
      </li>
      <li>
        <NavButton href="/realtime" class="flex items-center gap-2">
          <template #icon="{ size }">
            <Icon name="lucide:flame" :size="size" />
          </template>
          <template #text>
            <span>Realtime</span>
          </template>
        </NavButton>
      </li>
      <li v-if="loggedIn">
        <NavButton
          href="/messaging"
          class="flex items-center gap-2"
          :count="notificationsStore.unreadCount"
        >
          <template #icon="{ size }">
            <Icon name="mdi:envelope-outline" :size="size" />
          </template>
          <template #text>
            <span>Messaging</span>
          </template>
        </NavButton>
      </li>
      <li v-if="loggedIn">
        <NavButton
          href="/notifications"
          class="flex items-center gap-2"
          :count="notificationsStore.unreadCount"
        >
          <template #icon="{ size }">
            <Icon name="lucide:bell" :size="size" />
          </template>
          <template #text>
            <span>Notifications</span>
          </template>
        </NavButton>
      </li>
      <li>
        <NavButton :href="`/${user?.name}`" class="flex items-center gap-2">
          <template #icon="{ size }">
            <Icon name="lucide:user-circle" :size="size" />
          </template>
          <template #text>
            <span>Profile</span>
          </template>
        </NavButton>
      </li>
      <li v-if="loggedIn">
        <ClientOnly>
          <NavButton
            class="flex items-center gap-2 text-red-400 p-2 hover:bg-gray-50/5 w-full font-semibold text-left rounded-xs cursor-pointer"
            @click="logout"
          >
            <template #icon="{ size }">
              <Icon name="lucide:log-out" :size="size" />
            </template>
            <template #text>
              <span>Logout</span>
            </template>
          </NavButton>
        </ClientOnly>
      </li>
    </ul>
  </aside>
</template>
