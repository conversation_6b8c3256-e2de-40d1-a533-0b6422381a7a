<script setup lang="ts">
import {
  ToastAction,
  ToastDescription,
  ToastProvider,
  ToastRoot,
  ToastTitle,
  ToastViewport
} from 'reka-ui'

interface ToastProps {
  open?: boolean
  title?: string
  description?: string
  type?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

const props = withDefaults(defineProps<ToastProps>(), {
  open: false,
  title: '',
  description: '',
  type: 'info',
  duration: 5000
})

const emit = defineEmits<{
  close: []
}>()

const isOpen = ref(props.open)

// Watch for prop changes
watch(
  () => props.open,
  newValue => {
    isOpen.value = newValue
  }
)

// Auto-close after duration (only if duration > 0)
watch(isOpen, newValue => {
  if (newValue && props.duration && props.duration > 0) {
    setTimeout(() => {
      handleClose()
    }, props.duration)
  }
})

const handleClose = () => {
  isOpen.value = false
  emit('close')
}

const handleActionClick = () => {
  if (props.action?.onClick) {
    props.action.onClick()
  }
  handleClose()
}

// Get icon for toast type
const getTypeIcon = (type: string) => {
  switch (type) {
    case 'success':
      return 'lucide:check'
    case 'error':
      return 'lucide:x'
    case 'warning':
      return 'lucide:alert-triangle'
    case 'info':
    default:
      return 'lucide:info'
  }
}
</script>

<template>
  <ClientOnly>
    <ToastProvider>
      <ToastRoot
        v-model:open="isOpen"
        :class="[
          'bg-primary-bg rounded-lg shadow-xl border p-4',
          'flex gap-y-2 items-start flex-col',
          'data-[state=open]:animate-reka-slideIn data-[state=closed]:animate-reka-slideOut',
          'data-[swipe=move]:translate-x-[var(--reka-toast-swipe-move-x)]',
          'data-[swipe=cancel]:translate-x-0 data-[swipe=cancel]:transition-[transform_200ms_ease-out]',
          'data-[swipe=end]:animate-reka-swipeOut',
          'min-w-[350px] max-w-[100%]',
          'border-gray-100/5'
        ]"
      >
        <ToastTitle
          :class="[
            'flex gap-2 items-baseline font-semibold text-sm leading-5 flex-shrink-0 m-0',
            {
              'text-green-600': props.type === 'success',
              'text-red-600': props.type === 'error',
              'text-yellow-600': props.type === 'warning',
              'text-blue-600': props.type === 'info'
            }
          ]"
        >
          <div
            :class="[
              '[grid-area:_icon] flex items-center justify-center w-5 h-5 rounded-full text-sm font-bold mt-0.5'
            ]"
          >
            <Icon :name="getTypeIcon(props.type)" />
          </div>
          {{ props.title }}
        </ToastTitle>

        <ToastDescription
          v-if="props.description"
          :class="[
            '[grid-area:_description] text-xs leading-4 m-0',
            {
              'text-green-700': props.type === 'success',
              'text-red-700': props.type === 'error',
              'text-yellow-700': props.type === 'warning',
              'text-blue-700': props.type === 'info'
            }
          ]"
        >
          {{ props.description }}
        </ToastDescription>

        <ToastAction v-if="props.action" class="" as-child alt-text="action">
          <button
            :class="[
              'px-3 py-1 text-xs font-medium rounded border-none cursor-pointer transition-colors',
              {
                'bg-green-100 hover:bg-green-200 text-green-700 hover:text-green-900':
                  props.type === 'success',
                'bg-red-100 hover:bg-red-200 text-red-700 hover:text-red-900':
                  props.type === 'error',
                'bg-yellow-100 hover:bg-yellow-200 text-yellow-700 hover:text-yellow-900':
                  props.type === 'warning',
                'bg-blue-100 hover:bg-blue-200 text-blue-700 hover:text-blue-900':
                  props.type === 'info'
              }
            ]"
            @click="handleActionClick"
          >
            {{ props.action.label }}
          </button>
        </ToastAction>

        <ToastAction class="absolute -right-2 -top-2" as-child alt-text="close">
          <button
            class="flex items-center justify-center w-5 h-5 rounded text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors border-none cursor-pointer text-xs leading-none"
            @click="handleClose"
          >
            ✕
          </button>
        </ToastAction>
      </ToastRoot>

      <ToastViewport
        class="[--viewport-padding:_25px] fixed bottom-0 right-0 flex flex-col p-[var(--viewport-padding)] gap-[10px] w-[390px] max-w-[100vw] m-0 list-none z-[**********] outline-none"
      />
    </ToastProvider>
  </ClientOnly>
</template>
