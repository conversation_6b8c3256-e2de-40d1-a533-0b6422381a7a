<script setup lang="ts">
import { ref, computed } from 'vue'
import mg from '~/utils/tailwind'

const props = defineProps<{
  placeholder: string
  error?: string | null | boolean
  type?: string
  id?: string
  class?: string
  disabled?: boolean
}>()

const model = defineModel<string>()
const placeholder = ref(props.placeholder)

const error = toRef(props, 'error')

const inputClass = computed(() => [
  mg(
    'border rounded p-2 outline-none ',
    error.value
      ? 'border-red-500'
      : 'border-muted focus:border-fg-light focus:outline-1 focus:outline-fg-light',
    props.class ?? ''
  )
])
</script>

<template>
  <input
    :id="id"
    v-model="model"
    :type="type ?? 'text'"
    :placeholder="placeholder"
    :class="inputClass"
    :disabled="disabled"
  />
  <p v-if="error" class="text-red-500 text-xs mt-px">{{ error }}</p>
</template>
