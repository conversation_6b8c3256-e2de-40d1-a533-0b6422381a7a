<script setup lang="ts">
interface Props {
  id?: string
  modelValue?: File | null
  placeholder?: string
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: File | null): void
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: 'Token image'
})

const emit = defineEmits<Emits>()

const fileInput = ref<HTMLInputElement>()
const imagePreview = ref<string>('')

const { addToast } = useToast()

// Handle file upload
const handleFileChange = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      addToast({
        title: 'Invalid file type',
        description: 'Please select an image file',
        type: 'error'
      })
      return
    }

    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      addToast({
        title: 'File too large',
        description: 'Image must be smaller than 5MB',
        type: 'error'
      })
      return
    }

    const reader = new FileReader()
    reader.onload = e => {
      imagePreview.value = e.target?.result as string
    }
    reader.readAsDataURL(file)

    emit('update:modelValue', file)
  }
}

const removeImage = () => {
  imagePreview.value = ''
  emit('update:modelValue', null)
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const triggerFileInput = () => {
  if (!props.disabled) {
    fileInput.value?.click()
  }
}

// Watch for external changes to modelValue
watch(
  () => props.modelValue,
  newValue => {
    if (!newValue) {
      imagePreview.value = ''
      if (fileInput.value) {
        fileInput.value.value = ''
      }
    }
  }
)
</script>

<template>
  <div class="space-y-2">
    <div class="relative">
      <!-- Image preview or upload area -->
      <div
        v-if="imagePreview"
        class="w-full h-32 rounded-lg overflow-hidden border border-gray-100/10"
      >
        <img
          :src="imagePreview"
          alt="Preview"
          class="w-full h-full object-contain"
        />
      </div>
      <div
        v-else
        class="w-full h-32 border-muted !text-muted rounded-sm border p-2 border-dashed flex flex-col items-center justify-center cursor-pointer hover:border-gray-400 transition-colors"
        :class="{ 'opacity-50 cursor-not-allowed': disabled }"
        @click="triggerFileInput"
      >
        <div class="w-fit mx-auto">
          <Icon name="circum:image-on" size="65" />
        </div>
        <div class="text-center text-sm">
          <span class="font-semibold text-sm">Upload Image</span>
          <br />
          <span class="text-xs">PNG, JPEG, WEBP</span>
        </div>
      </div>

      <!-- Action buttons overlay -->
      <div v-if="imagePreview" class="absolute top-2 right-2 flex gap-2">
        <button
          type="button"
          :disabled="disabled"
          class="bg-gray-900/80 hover:bg-gray-900 text-white p-2 h-7 w-7 flex items-center rounded-full transition-colors disabled:opacity-50"
          @click="triggerFileInput"
        >
          <Icon name="lucide:camera" />
        </button>
        <button
          type="button"
          :disabled="disabled"
          class="bg-red-600/80 hover:bg-red-600 text-white p-2 h-7 w-7 flex items-center rounded-full transition-colors disabled:opacity-50"
          @click="removeImage"
        >
          <Icon name="lucide:trash-2" />
        </button>
      </div>
    </div>

    <!-- Hidden file input -->
    <input
      ref="fileInput"
      type="file"
      accept="image/png,image/jpeg,image/webp"
      class="hidden"
      :disabled="disabled"
      @change="handleFileChange"
    />
  </div>
</template>
