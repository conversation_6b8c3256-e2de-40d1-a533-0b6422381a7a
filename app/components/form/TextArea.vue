<script setup lang="ts">
import { ref, computed } from 'vue'
import mg from '~/utils/tailwind'

type ResizeMode = 'none' | 'both' | 'horizontal' | 'vertical'

const props = withDefaults(
  defineProps<{
    placeholder: string
    error?: string | null | boolean
    type?: string
    id?: string
    class?: string
    disabled?: boolean
    resizeMode?: ResizeMode
  }>(),
  {
    resizeMode: 'none'
  }
)

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
}>()
const model = defineModel<string>()
const placeholder = ref(props.placeholder)

const error = toRef(props, 'error')

const inputClass = computed(() => [
  mg(
    'border rounded p-2 outline-none ',
    error.value
      ? 'border-red-500'
      : 'border-muted focus:border-fg-light focus:outline-1 focus:outline-fg-light',
    props.class ?? ''
  )
])
</script>

<template>
  <textarea
    :id="id"
    v-model="model"
    :placeholder="placeholder"
    :class="inputClass"
    :disabled="disabled"
    rows="3"
    :style="{ resize: props.resizeMode || 'both' }"
  />
  <p v-if="error" class="text-red-500 text-xs mt-px">{{ error }}</p>
</template>
