<script setup lang="ts">
import AuthPopup from './popups/AuthPopup.vue'
import FollowButton from './button/FollowButton.vue'

const { loggedIn } = useUserSession()
const shouldShowAuthPopup = computed(() => !loggedIn.value)

// Initialize posts store to ensure hashtags are loaded for proper resolution
const posts = usePostsStore()
onMounted(() => {
  posts.initializeStore()
})

// Fetch trending hashtags from API
const { data: trendingHashtags, pending: hashtagsPending } =
  await useLazyFetch<{
    hashtags: Array<{
      tag: string
      posts: number
    }>
  }>('/api/trending/hashtags?limit=5', {
    server: false
  })

// Fetch trending tokens from API
const { data: trendingTokens, pending: tokensPending } = await useLazyFetch<{
  tokens: Array<{
    id: number
    symbol: string
    name: string
    address: string
    mcap: string
    change: string
    interactions: number
  }>
}>('/api/trending/tokens?limit=5', {
  server: false
})

// Fetch recommended profiles
const { data: recommendedProfiles, pending: profilesPending } =
  await useLazyFetch<{
    profiles: Array<{
      id: number
      name: string
      address: string
      profileImage: string
      followers: number
    }>
  }>('/api/recommendations/profiles?limit=3', {
    server: false
  })

const suggestedUsers = computed(() => recommendedProfiles.value?.profiles || [])
const trendingTopics = computed(() => trendingHashtags.value?.hashtags || [])
const trendingCoins = computed(() => trendingTokens.value?.tokens || [])
</script>

<template>
  <aside class="max-w-xs w-full h-full bg-gray-50/5 p-2 space-y-2">
    <div class="flex gap-2">
      <ClientOnly>
        <AuthPopup v-if="shouldShowAuthPopup" />
      </ClientOnly>
    </div>

    <!-- Who to follow section -->
    <div class="bg-primary-bg p-4 rounded">
      <h4 class="font-bold text-sm mb-3">Who to follow</h4>

      <div v-if="profilesPending" class="space-y-3">
        <!-- Loading skeletons -->
        <div v-for="i in 3" :key="i" class="flex items-center gap-3">
          <div class="w-8 h-8 rounded-full bg-gray-100/10 animate-pulse" />
          <div class="flex-1">
            <div class="h-4 w-20 bg-gray-100/10 rounded animate-pulse mb-1" />
            <div class="h-3 w-16 bg-gray-100/10 rounded animate-pulse" />
          </div>
          <div class="h-6 w-12 bg-gray-100/10 rounded animate-pulse" />
        </div>
      </div>

      <div v-else-if="suggestedUsers.length > 0" class="space-y-3">
        <div
          v-for="user in suggestedUsers"
          :key="user.id"
          class="flex items-center gap-3"
        >
          <NuxtLink
            :to="`/${user.address}`"
            class="flex items-center gap-3 flex-1 min-w-0"
          >
            <div
              class="w-8 h-8 rounded-full bg-secondary-bg flex items-center justify-center overflow-hidden"
            >
              <img
                v-if="user.profileImage"
                :src="user.profileImage"
                :alt="user.name"
                class="w-full h-full object-cover"
              />
              <span v-else class="text-white font-bold text-sm">{{
                user.name.charAt(0).toUpperCase()
              }}</span>
            </div>
            <div class="flex-1 min-w-0">
              <div class="font-semibold text-sm truncate">{{ user.name }}</div>
              <div class="text-xs text-muted">
                {{ user.followers.toLocaleString() }} followers
              </div>
            </div>
          </NuxtLink>
          <FollowButton
            v-if="loggedIn"
            :user-id="user.id"
            :is-following="false"
            variant="small"
          />
          <button
            v-else
            class="text-xs bg-gray-100/10 hover:bg-gray-100/20 px-2 py-1 rounded font-semibold"
          >
            Follow
          </button>
        </div>
      </div>

      <div v-else class="text-center text-muted py-4">
        <p class="text-xs">No recommendations available</p>
      </div>
    </div>

    <div class="top-0 space-y-2 sticky">
      <!-- Trending topics section -->
      <div class="bg-primary-bg p-4 rounded">
        <h4 class="font-bold text-sm mb-3">Trending topics</h4>

        <div v-if="hashtagsPending" class="space-y-2">
          <!-- Loading skeletons -->
          <div v-for="i in 5" :key="i" class="p-2">
            <div class="h-4 w-16 bg-gray-100/10 rounded animate-pulse mb-1" />
            <div class="h-3 w-12 bg-gray-100/10 rounded animate-pulse" />
          </div>
        </div>

        <div v-else-if="trendingTopics.length > 0" class="space-y-1">
          <div
            v-for="topic in trendingTopics"
            :key="topic.tag"
            class="p-2 hover:bg-gray-50/5 rounded cursor-pointer"
          >
            <div class="font-semibold text-sm">#{{ topic.tag }}</div>
            <div class="text-xs text-muted">
              {{ topic.posts.toLocaleString() }} posts
            </div>
          </div>
        </div>

        <div v-else class="text-center text-muted py-4">
          <p class="text-xs">No trending topics</p>
        </div>
      </div>

      <!-- Trending coins section -->
      <div class="bg-primary-bg p-4 rounded">
        <h4 class="font-bold text-sm mb-3">Trending coins</h4>

        <div v-if="tokensPending" class="space-y-2">
          <!-- Loading skeletons -->
          <div v-for="i in 5" :key="i" class="p-2">
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <div
                  class="w-6 h-6 rounded-full bg-gray-100/10 animate-pulse"
                />
                <div>
                  <div
                    class="h-4 w-8 bg-gray-100/10 rounded animate-pulse mb-1"
                  />
                  <div class="h-3 w-12 bg-gray-100/10 rounded animate-pulse" />
                </div>
              </div>
              <div class="text-right">
                <div
                  class="h-3 w-8 bg-gray-100/10 rounded animate-pulse mb-1"
                />
                <div class="h-3 w-10 bg-gray-100/10 rounded animate-pulse" />
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="trendingCoins.length > 0" class="space-y-2">
          <div
            v-for="coin in trendingCoins"
            :key="coin.symbol"
            class="p-2 hover:bg-gray-50/5 rounded cursor-pointer"
          >
            <div class="flex items-center justify-between mb-1">
              <div class="flex items-center gap-2">
                <div
                  class="w-6 h-6 rounded-full bg-secondary-bg flex items-center justify-center"
                >
                  <span class="text-white font-bold text-xs">{{
                    coin.symbol.charAt(0)
                  }}</span>
                </div>
                <div>
                  <div class="font-semibold text-sm">${{ coin.symbol }}</div>
                  <div class="text-xs text-muted truncate">{{ coin.name }}</div>
                </div>
              </div>
              <div class="text-right">
                <div class="text-xs text-muted">{{ coin.mcap }}</div>
                <div class="text-xs text-green-400 font-semibold">
                  {{ coin.change }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="text-center text-muted py-4">
          <p class="text-xs">No trending coins</p>
        </div>
      </div>

      <!-- Footer -->
      <div class="text-xs text-muted text-center pt-4">
        <div class="font-semibold mb-1">
          All rights reserved © 2025 Mazenut
        </div>
        <div>
          <a href="#" class="hover:underline">Terms of Service</a>
          <span class="mx-1">|</span>
          <a href="#" class="hover:underline">Privacy Policy</a>
        </div>
      </div>
    </div>
  </aside>
</template>
