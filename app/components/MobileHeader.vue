<script setup lang="ts">
import MobileAuthPopup from './auth/MobileAuthPopup.vue'

const { user } = useUserSession()
</script>

<template>
  <header
    class="fixed top-0 left-0 right-0 z-50 bg-black/90 backdrop-blur-sm border-b border-gray-100/5 px-4 py-3 md:hidden"
  >
    <!-- Account for iOS status bar and notch -->
    <div class="pt-safe-top">
      <div class="flex items-center justify-between">
        <!-- Profile Image -->
        <div class="flex items-center">
          <div
            class="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center overflow-hidden"
          >
            <span v-if="user?.name" class="text-white font-bold text-xs">
              {{ user.name.charAt(0).toUpperCase() }}
            </span>
            <Icon
              v-else
              name="lucide:user-circle"
              size="20"
              class="text-gray-400"
            />
          </div>
        </div>

        <!-- Logo centered -->
        <div
          class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
        >
          <Logo class="size-8" />
        </div>

        <div>
          <MobileAuthPopup v-if="!user" />
          <div v-else class="flex items-center gap-2">
            <span class="text-white text-xs">{{
              user.name || user.email
            }}</span>
          </div>
        </div>
      </div>
    </div>
  </header>
</template>

<style scoped>
/* iOS safe area support */
.pt-safe-top {
  padding-top: env(safe-area-inset-top);
}
</style>
