<script setup lang="ts">
import { TooltipContent, TooltipRoot, TooltipTrigger } from 'reka-ui'
import type { TokenInfo } from '~/server/services/trading'

const TARGET_SOL_RESERVE = 122 // Target SOL reserves for migration
const INITIAL_VIRTUAL_SOL_RESERVE = 28 // Initial virtual reserve (not shown to users)

// Development preview toggle - set to true to see migration state
const PREVIEW_MIGRATION = false

const props = defineProps<{
  tokenData: TokenInfo | null
}>()

// Computed properties for display - using explicit reactive dependencies

// Current market cap calculation
const currentMarketCap = computed(() => {
  if (
    !props.tokenData?.currentPrice?.priceUSD ||
    !props.tokenData?.totalSupply
  ) {
    return 0
  }
  return props.tokenData.currentPrice.priceUSD * props.tokenData.totalSupply
})

// formatted market cap in usd
const formattedMarketCap = computed(() => {
  if (PREVIEW_MIGRATION) return '$89.5K' // Preview market cap at migration

  if (currentMarketCap.value === 0) return '$0.00'

  if (currentMarketCap.value >= 1_000_000) {
    return `$${(currentMarketCap.value / 1_000_000).toFixed(2)}M`
  } else if (currentMarketCap.value >= 1_000) {
    return `$${(currentMarketCap.value / 1_000).toFixed(2)}K`
  } else {
    return `$${currentMarketCap.value.toFixed(2)}`
  }
})

const progressToTarget = computed(() => {
  if (PREVIEW_MIGRATION) return 98.5 // Preview at 98.5% for migration demo

  if (!props.tokenData?.solReserves) return 0

  // Convert lamports to SOL (assuming solReserves is in lamports)
  const currentSolReserves = props.tokenData.solReserves / 1e9

  // Subtract the initial virtual reserve (28 SOL) since it doesn't count toward migration
  const effectiveSolReserves = Math.max(
    0,
    currentSolReserves - INITIAL_VIRTUAL_SOL_RESERVE
  )

  // Progress is based on how much SOL above the initial virtual reserve we have toward the target
  // We need to reach 122 SOL total to trigger migration
  const targetAboveInitial = TARGET_SOL_RESERVE - INITIAL_VIRTUAL_SOL_RESERVE
  const progress = (effectiveSolReserves / targetAboveInitial) * 100

  // Cap at 100%
  const finalProgress = Math.min(progress, 100)

  return finalProgress
})

const targetMarketCap = computed(() => {
  return (TARGET_SOL_RESERVE - INITIAL_VIRTUAL_SOL_RESERVE).toString() + ' SOL'
})

const nearMigration = computed(() => {
  return progressToTarget.value >= 95 // Near migration when 95% or more
})

// Current SOL reserves formatted
const currentSolReserves = computed(() => {
  if (!props.tokenData?.solReserves) return 0
  return props.tokenData.solReserves / 1e9 // Convert lamports to SOL
})

const formattedSolReserves = computed(() => {
  if (PREVIEW_MIGRATION) return '121.94 SOL' // Preview reserves at migration

  if (currentSolReserves.value === 0) return '0 SOL'

  return `${(currentSolReserves.value - INITIAL_VIRTUAL_SOL_RESERVE).toFixed(2)} SOL`
})

// Calculate target market cap at migration using bonding curve formula
const targetMigrationMarketCap = computed(() => {
  if (!props.tokenData?.tokenReserves || !props.tokenData?.solReserves) {
    return 0
  }

  // At migration, we'll have 122 SOL total reserves
  const migrationSolReservesLamports = TARGET_SOL_RESERVE * 1e9 // Convert to lamports

  // Calculate what token reserves will be at migration using constant product formula
  // k = current_sol_reserves * current_token_reserves (constant)
  const k = props.tokenData.solReserves * props.tokenData.tokenReserves

  // At migration: migration_sol_reserves * migration_token_reserves = k
  // So: migration_token_reserves = k / migration_sol_reserves
  const estimatedTokenReservesAtMigration = k / migrationSolReservesLamports

  // Calculate price per token at migration using constant product formula
  // Price = SOL reserves / token reserves (price per smallest token unit)
  const pricePerTokenUnitAtMigration =
    migrationSolReservesLamports / estimatedTokenReservesAtMigration

  // Calculate circulating supply at migration
  const totalSupply = 1_000_000_000 * 1e9 // 1B tokens with 9 decimals
  const circulatingSupplyAtMigration =
    totalSupply - estimatedTokenReservesAtMigration

  // Market cap in lamports = circulating supply * price per token unit
  const marketCapLamports =
    circulatingSupplyAtMigration * pricePerTokenUnitAtMigration

  // Convert to SOL
  const marketCapSol = marketCapLamports / 1e9

  // Convert to USD if we have current price data
  if (props.tokenData.currentPrice?.priceUSD) {
    if (
      props.tokenData.currentPrice.priceLamports &&
      props.tokenData.currentPrice.priceUSD > 0
    ) {
      // SOL price = (token price USD / token price lamports) * 1e9
      const solPriceUSD =
        (props.tokenData.currentPrice.priceUSD /
          props.tokenData.currentPrice.priceLamports) *
        1e9
      return marketCapSol * solPriceUSD
    }
  }

  return 0
})

function truncate (num: number, decimals: number) {
  const factor = 10 ** decimals
  return Math.trunc(num * factor) / factor
}

const formattedTargetMarketCap = computed(() => {
  if (PREVIEW_MIGRATION) return '$91K' // Preview target at migration

  const mcapInUsd = targetMigrationMarketCap.value
  if (mcapInUsd >= 1_000_000) {
    return `$${truncate(mcapInUsd / 1_000_000, 2)}M`
  } else if (mcapInUsd >= 1_000) {
    return `$${truncate(mcapInUsd / 1_000, 2)}K`
  } else {
    return `$${truncate(mcapInUsd, 2)}`
  }
})
</script>

<template>
  <!-- Market Cap Display -->
  <div
    class="market-cap-progress w-full space-y-2"
    :class="{ 'border-red-500': nearMigration }"
  >
    <!-- Market Cap Value -->
    <div class="flex justify-between items-center text-sm">
      <div>Market Cap</div>
      <div class="flex items-center gap-2">
        <span class="font-semibold text-white">{{ formattedMarketCap }}</span>
        <span v-if="nearMigration" class="text-orange-600 text-xs font-bold">
          MIGRATING
        </span>
      </div>
    </div>

    <!-- Progress Bar with Tooltip -->
    <TooltipRoot>
      <TooltipTrigger as-child>
        <div class="relative">
          <!-- Progress Background -->
          <div class="w-full h-2 bg-gray-700 rounded-full overflow-hidden">
            <!-- Progress Fill - changes to red when near migration -->
            <div
              class="h-full rounded-full transition-all duration-300 ease-out"
              :class="
                nearMigration
                  ? 'bg-gradient-to-r from-red-500 to-orange-500'
                  : 'bg-gradient-to-r from-green-500 to-blue-500'
              "
              :style="{ width: `${progressToTarget}%` }"
            />
          </div>

          <!-- Progress Indicator Dot -->
          <div
            class="absolute top-1/2 transform -translate-y-1/2 w-3 h-3 rounded-full border-2 border-gray-800 shadow-lg transition-all duration-300 ease-out"
            :class="nearMigration ? 'bg-red-400' : 'bg-white'"
            :style="{ left: `calc(${progressToTarget}% - 6px)` }"
          />
        </div>
      </TooltipTrigger>

      <TooltipContent
        side="top"
        class="bg-gray-900 border border-gray-700 text-white px-3 py-2 rounded text-xs space-y-1 max-w-xs"
      >
        <div class="font-semibold">
          {{ progressToTarget.toFixed(1) }}% to {{ targetMarketCap }}
        </div>
        <div class="text-gray-300">
          Current Reserves: {{ formattedSolReserves }}
        </div>
        <div class="text-gray-300">Market Cap: {{ formattedMarketCap }}</div>
        <div class="text-blue-400 border-t border-gray-600 pt-1">
          Will migrate at {{ formattedTargetMarketCap }} market cap
        </div>

        <div
          v-if="nearMigration"
          class="text-red-400 font-bold border-t border-gray-600 pt-1"
        >
          Ready for Migration!
        </div>
      </TooltipContent>
    </TooltipRoot>

    <!-- Progress Labels -->
    <div class="flex justify-between text-xs text-gray-500">
      <span>0 SOL</span>
      <span>{{ targetMarketCap }}</span>
    </div>
  </div>
</template>

<style scoped>
@reference 'tailwindcss';
.market-cap-progress {
  @apply border border-gray-700/50 rounded-lg p-3 bg-gray-800/30;
}
</style>
