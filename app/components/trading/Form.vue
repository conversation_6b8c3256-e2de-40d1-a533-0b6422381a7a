<script setup lang="ts">
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from 'reka-ui'
const toast = useToast()
const props = defineProps<{
  tokenData: TokenInfo | null
}>()

const emit = defineEmits<{
  buy: [number]
  sell: [number]
}>()

const currentBuyOrSellTab = ref('buy')
const finance = useFinance()
const wallet = useWallet()
const priceFormatter = usePriceFormatter()

const addr = inject<string>('currentTokenAddress')

// WebSocket endpoint for trading notifications
const priceWsEndpoint = computed(() => {
  if (import.meta.client) {
    const wss = window.location.protocol === 'https:' ? 'wss://' : 'ws://'
    return `${wss}${window.location.host}/api/ws/trading/${addr || 'error'}`
  }
})

const buyAmount = ref(0)
const isLoading = ref(false)
const errorMessage = ref('')
const successMessage = ref('')
const estimatedTokens = ref<number>(0)
const quickAmounts = [0.1, 0.25, 1, 5]
const quickSellPercentages = [10, 25, 50, 75, 100]
const sellAmount = ref<number>(0)
const isToastOpen = ref(false)
const userTokenBalance = ref<number>(0)
const estimatedSolAmount = ref<number>(0)

// Computed property for button text
const buttonText = computed(() => {
  if (isLoading.value) return 'Processing Transaction...'
  if (!wallet.connected) return 'Connect Wallet to Buy'
  return 'Buy Token'
})

// Computed property for button disabled state
const buttonDisabled = computed(() => {
  return isLoading.value || !buyAmount.value || buyAmount.value <= 0
})

// Computed property for sell button text
const sellButtonText = computed(() => {
  if (isLoading.value) return 'Processing Transaction...'
  if (!wallet.connected) return 'Connect Wallet to Sell'
  if (sellAmount.value > userTokenBalance.value) return 'Insufficient Balance'
  return 'Sell Tokens'
})

// Computed property for sell button disabled state
const sellButtonDisabled = computed(() => {
  return (
    isLoading.value ||
    !sellAmount.value ||
    sellAmount.value <= 0 ||
    sellAmount.value > userTokenBalance.value
  )
})

// Function to set sell percentage
const setSellPercentage = (percentage: number) => {
  // For now, set a fixed amount - in production you'd calculate from user's holdings
  const amount = getPercentageAmount(percentage)
  sellAmount.value = amount
  errorMessage.value = ''
  successMessage.value = ''
}

// Helper function to calculate percentage amount
const getPercentageAmount = (percentage: number) => {
  // Use actual user token balance
  const balance = userTokenBalance.value || 0
  return (balance * percentage) / 100
}

const setQuickAmount = (amount: number) => {
  buyAmount.value = amount
  errorMessage.value = '' // Clear any existing errors
  successMessage.value = '' // Clear any existing success messages
  // Note: updatePriceEstimation() will be called automatically by the watcher
}

const buyToken = async () => {
  isToastOpen.value = true
  if (!addr) {
    errorMessage.value = 'No token address provided'
    return
  }

  // Check wallet connection and try to reconnect if needed
  const walletReady = await wallet.ensureWalletConnected()
  if (!walletReady) {
    errorMessage.value = 'Please connect your wallet first'
    return
  }

  const amount = buyAmount.value
  if (!amount || amount <= 0) {
    errorMessage.value = 'Please enter a valid amount'
    return
  }

  isLoading.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    const { tokensAmount } = await finance.buyCurrentToken(
      addr,
      amount,
      estimatedTokensFromBuy.value // Convert to token base units
    )

    emit('buy', tokensAmount ?? 0)

    toast.success('Successfully bought tokens!')
    buyAmount.value = 0 // Clear the input
    userTokenBalance.value += tokensAmount ?? 0
  } catch (error: any) {
    console.error('Error buying token:', error)
    errorMessage.value =
      error?.message || 'Failed to buy token. Please try again.'
  } finally {
    isLoading.value = false
  }
}

const estimateCostUSD = computed(() => props.tokenData?.currentPrice?.priceUSD)

// Bonding curve simulation for selling tokens using tokenData
const estimatedSolFromSell = computed(() => {
  if (!sellAmount.value || sellAmount.value <= 0) {
    return 0
  }

  // Check if we have reserves data in tokenData
  if (props.tokenData?.solReserves && props.tokenData?.tokenReserves) {
    // Following exactly what pricing.rs does in calculate_sell_amount
    // const solReserves = props.tokenData.solReserves
    // const tokenReserves = props.tokenData.tokenReserves

    // Convert sell amount to base units (tokens have 9 decimals)
    const tokenAmountToSell = sellAmount.value * 1e9

    // Bonding curve formula: x * y = k (constant product)
    // where x = SOL reserves (lamports), y = token reserves (base units)
    const k = props.tokenData.solReserves * props.tokenData.tokenReserves

    // Calculate new token reserves after selling
    const newTokenReserves = props.tokenData.tokenReserves + tokenAmountToSell

    // Calculate new SOL reserves: new_sol_reserves = k / new_token_reserves
    const newSolReserves = k / newTokenReserves

    // SOL amount user receives = old_sol_reserves - new_sol_reserves (in lamports)
    const solAmountBeforeFee = props.tokenData.solReserves - newSolReserves

    // Apply 0.6% trading fee (matching Rust program)
    const tradingFee = solAmountBeforeFee * 0.006 // 0.6%
    const solAmountAfterFee = solAmountBeforeFee - tradingFee

    // Convert lamports to SOL for display
    return Math.max(0, solAmountAfterFee / 1e9)
  }

  // Fallback: simple calculation using current price if reserves data not available
  if (props.tokenData?.currentPrice?.priceLamports) {
    const priceLamports = props.tokenData.currentPrice.priceLamports
    const solAmountBeforeFee = (sellAmount.value * priceLamports) / 1e9
    const tradingFee = solAmountBeforeFee * 0.006
    const solAmountAfterFee = solAmountBeforeFee - tradingFee
    return Math.max(0, solAmountAfterFee)
  }

  return 0
})

// Bonding curve simulation for buying tokens using tokenData
const estimatedTokensFromBuy = computed(() => {
  if (!buyAmount.value || buyAmount.value <= 0) {
    return 0
  }

  // Check if we have reserves data in tokenData
  if (props.tokenData?.solReserves && props.tokenData?.tokenReserves) {
    // Calculate using the exact algorithm from finance.ts
    const solAmount = buyAmount.value
    const currentPriceLamports =
      props.tokenData.currentPrice?.priceLamports || 0

    // This is how many tokens the user wants to buy based on current price
    // This is the tokensAmount calculated in finance.ts
    const initialTokensAmount = (solAmount * 1e9) / currentPriceLamports

    // Convert to the token base units as done in finance.ts (multiply by 1e9)
    const tokenAmount = initialTokensAmount * 1e9

    // Now, let's use the bonding curve to calculate what this would actually cost
    // Following exactly what pricing.rs does in calculate_buy_cost

    // const solReserves = props.tokenData.solReserves
    // const tokenReserves = props.tokenData.tokenReserves

    // Calculate k (constant product)
    const k = props.tokenData.solReserves * props.tokenData.tokenReserves

    // Calculate new token reserves after purchase
    const newTokenReserves = props.tokenData.tokenReserves - tokenAmount

    // Calculate required SOL amount: new_sol_reserves = k / new_token_reserves
    const newSolReserves = k / newTokenReserves

    // SOL amount (base cost without fee)
    const baseSolAmount = newSolReserves - props.tokenData.solReserves

    // Apply trading fee (0.6% like pump.fun)
    const fee = baseSolAmount * 0.006 // 0.6%
    const totalCost = baseSolAmount + fee

    // Adjust token amount if the calculated cost exceeds the user's input
    // The user specified how much SOL they want to spend, but the bonding curve
    // might require more or less for the requested token amount
    const adjustedTokenAmount =
      solAmount >= totalCost / 1e9
        ? initialTokensAmount
        : initialTokensAmount * (solAmount / (totalCost / 1e9))

    return Math.max(0, adjustedTokenAmount)
  }

  // Fallback: simple calculation using current price if reserves data not available
  if (props.tokenData?.currentPrice?.priceLamports) {
    const priceLamports = props.tokenData.currentPrice.priceLamports

    // Calculate tokens directly using the current price in lamports
    // This matches the logic in the finance store
    const tokenAmount = (buyAmount.value * 1e9) / priceLamports

    return Math.max(0, tokenAmount)
  }

  return 0
})

const solToTokenAmount = (sol: number) => {
  // Use the new bonding curve simulation
  return estimatedTokensFromBuy.value
}

onMounted(async () => {
  // important for Firefox
  await wallet.ensureWalletConnected()
  // initialize the balance
  await finance.refreshTokenBalance(addr ?? '0x00')
  userTokenBalance.value = finance.getCurrentTokenBalance()
})

const sellToken = async () => {
  isToastOpen.value = true
  if (!addr) {
    errorMessage.value = 'No token address provided'
    return
  }

  // Check wallet connection and try to reconnect if needed
  const walletReady = await wallet.ensureWalletConnected()
  if (!walletReady) {
    errorMessage.value = 'Please connect your wallet first'
    return
  }

  const amount = sellAmount.value
  if (!amount || amount <= 0) {
    errorMessage.value = 'Please enter a valid amount'
    return
  }

  isLoading.value = true
  errorMessage.value = ''
  successMessage.value = ''

  try {
    console.log('Selling token', addr, 'for', amount, 'SOL')
    const { tokensAmount } = await finance.sellCurrentToken(
      addr,
      amount,
      estimatedSolFromSell.value // Pass the estimated SOL amount for slippage protection
    )

    emit('sell', tokensAmount ?? 0)
    toast.success('Successfully sold tokens!')
    sellAmount.value = 0 // Clear the input
    userTokenBalance.value -= amount // Update balance
  } catch (error: any) {
    console.error('Error selling token:', error)
    errorMessage.value =
      error?.message || 'Failed to sell token. Please try again.'
  } finally {
    isLoading.value = false
  }
}

watch(
  buyAmount,
  newValue => {
    if (newValue && newValue > 0) {
      estimatedTokens.value = estimatedTokensFromBuy.value
    } else {
      estimatedTokens.value = 0
    }
  },
  { immediate: true }
)
</script>

<template>
  <!-- Trading Form -->
  <div class="bg-primary-bg rounded-lg p-3">
    <!-- Wallet connection status and balance -->
    <div v-if="wallet.connected" class="mb-3 p-2 bg-gray-100/5 rounded-lg">
      <div class="text-xs text-muted">
        Balance:
        <span class="font-semibold text-fg-light tracking-wide">{{
          new Number(userTokenBalance.toFixed(3)).toLocaleString()
        }}</span>
      </div>
    </div>

    <TabsRoot v-model="currentBuyOrSellTab" default-value="buy">
      <TabsList class="flex p-1 bg-gray-100/5 rounded-lg mb-3">
        <TabsTrigger
          class="px-3 py-1.5 flex-1 text-center rounded-md font-semibold text-xs transition cursor-pointer"
          :class="{
            'bg-green-500 text-white': currentBuyOrSellTab === 'buy',
            'text-green-500 hover:bg-green-500/10':
              currentBuyOrSellTab !== 'buy'
          }"
          value="buy"
        >
          Buy
        </TabsTrigger>
        <TabsTrigger
          class="px-3 py-1.5 flex-1 text-center rounded-md font-semibold text-xs transition cursor-pointer"
          :class="{
            'bg-red-500 text-white': currentBuyOrSellTab === 'sell',
            'text-red-500 hover:bg-red-500/10': currentBuyOrSellTab !== 'sell'
          }"
          value="sell"
        >
          Sell
        </TabsTrigger>
      </TabsList>

      <TabsContent value="buy">
        <div class="space-y-3">
          <!-- Quick amount buttons -->
          <div>
            <label class="block text-xs font-semibold text-muted mb-1"
              >Quick Select</label
            >
            <div class="grid grid-cols-4 gap-1.5">
              <button
                v-for="amount in quickAmounts"
                :key="amount"
                class="text-xs font-semibold bg-gray-100/10 hover:bg-gray-100/20 transition py-2 rounded-md"
                :class="{
                  'bg-green-500/20 border border-green-500 text-green-400':
                    buyAmount === amount
                }"
                @click="setQuickAmount(amount)"
              >
                {{ amount }} SOL
              </button>
            </div>
          </div>

          <!-- Amount input -->
          <div>
            <label class="block text-xs font-semibold text-muted mb-1"
              >Amount (SOL)</label
            >
            <input
              v-model="buyAmount"
              type="number"
              step="0.01"
              min="0"
              class="w-full p-2.5 bg-gray-100/10 rounded-lg border border-gray-100/10 focus:border-green-500 focus:outline-none text-sm font-semibold"
              placeholder="0.00"
            />
          </div>

          <!-- Price estimation -->
          <div
            v-if="buyAmount && buyAmount > 0"
            class="p-2 bg-gray-100/5 rounded-lg border border-gray-100/5"
          >
            <div class="space-y-1.5">
              <div class="flex justify-between text-xs">
                <span class="text-muted">You'll receive:</span>
                <span class="font-semibold"
                  >~{{
                    Number(estimatedTokensFromBuy).toLocaleString()
                  }}
                  tokens</span
                >
              </div>
            </div>
          </div>

          <!-- Messages -->
          <div
            v-if="errorMessage"
            class="p-2 bg-red-500/10 border border-red-500/20 rounded-lg"
          >
            <div class="text-red-400 text-xs">{{ errorMessage }}</div>
          </div>
          <div
            v-if="successMessage"
            class="p-2 bg-green-500/10 border border-green-500/20 rounded-lg"
          >
            <div class="text-green-400 text-xs">{{ successMessage }}</div>
          </div>

          <!-- Buy button -->
          <ClientOnly>
            <button
              :disabled="buttonDisabled"
              class="w-full p-2.5 bg-green-500 text-white font-bold rounded-lg hover:bg-green-500/90 transition disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 text-sm"
              @click="wallet.connected ? buyToken() : wallet.connect()"
            >
              <Icon
                v-if="isLoading || wallet.connecting"
                name="lucide:loader"
                size="14"
                class="animate-spin"
              />
              <span>{{ buttonText }}</span>
            </button>
          </ClientOnly>
        </div>
      </TabsContent>

      <TabsContent value="sell">
        <div class="space-y-3">
          <!-- Quick percentage buttons -->
          <div>
            <label class="block text-xs font-semibold text-muted mb-1"
              >Quick Select</label
            >
            <div class="grid grid-cols-5 gap-1.5">
              <button
                v-for="percentage in quickSellPercentages"
                :key="percentage"
                :disabled="userTokenBalance <= 0"
                class="text-xs font-semibold bg-gray-100/10 hover:bg-gray-100/20 transition p-1.5 rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
                :class="{
                  'bg-red-500/20 border border-red-500 text-red-400':
                    sellAmount === getPercentageAmount(percentage)
                }"
                @click="setSellPercentage(percentage)"
              >
                {{ percentage }}%
              </button>
            </div>
          </div>

          <!-- Amount input -->
          <div>
            <label class="block text-xs font-semibold text-muted mb-1"
              >Amount (Tokens)</label
            >
            <input
              v-model="sellAmount"
              type="number"
              step="0.01"
              min="0"
              class="w-full p-2.5 bg-gray-100/10 rounded-lg border border-gray-100/10 focus:border-red-500 focus:outline-none text-sm font-semibold"
              placeholder="0"
            />
          </div>

          <!-- Sell price estimation -->
          <div
            v-if="sellAmount && sellAmount > 0"
            class="p-2 bg-gray-100/5 rounded-lg border border-gray-100/5"
          >
            <div class="space-y-1.5">
              <div class="flex justify-between text-xs">
                <span class="text-muted">You'll receive:</span>
                <span class="font-semibold">
                  ~{{ estimatedSolFromSell.toFixed(6) }}
                  SOL
                </span>
              </div>
            </div>
          </div>

          <!-- Balance warning -->
          <div
            v-if="wallet.connected && userTokenBalance === 0"
            class="p-2 bg-yellow-500/10 border border-yellow-500/20 rounded-lg"
          >
            <div class="text-yellow-400 text-xs text-center">
              You don't own any tokens to sell
            </div>
          </div>

          <!-- Messages -->
          <div
            v-if="errorMessage"
            class="p-2 bg-red-500/10 border border-red-500/20 rounded-lg"
          >
            <div class="text-red-400 text-xs">{{ errorMessage }}</div>
          </div>
          <div
            v-if="successMessage"
            class="p-2 bg-green-500/10 border border-green-500/20 rounded-lg"
          >
            <div class="text-green-400 text-xs">{{ successMessage }}</div>
          </div>

          <!-- Sell button -->
          <ClientOnly>
            <button
              :disabled="sellButtonDisabled"
              class="w-full p-2.5 bg-red-500 text-white font-bold rounded-lg hover:bg-red-500/90 transition disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2 text-sm"
              @click="wallet.connected ? sellToken() : wallet.connect()"
            >
              <Icon
                v-if="isLoading || wallet.connecting"
                name="lucide:loader"
                size="14"
                class="animate-spin"
              />
              <span>{{ sellButtonText }}</span>
            </button>
          </ClientOnly>
        </div>
      </TabsContent>
    </TabsRoot>
  </div>
  <Toast :open="isToastOpen" />
</template>
