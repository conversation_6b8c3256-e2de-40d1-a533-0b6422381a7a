<script setup lang="ts">
defineProps<{
  tokenInfo: TokenInfo | null
}>()
const toast = useToast()
const addr = inject<string>('currentTokenAddress')
const tokensStore = useTokens()

onMounted(() => {
  if (addr) {
    tokensStore.fetchSidebarData(addr)
  }
})

const handleTokenBuy = async (amount: number) => {
  try {
    // Send WebSocket notification about the buy transaction
    // trading.notifyTokenBuy(
    //   amount,
    //   trading.tokenInfo.value?.currentPrice?.priceLamports
    // )
    console.log(`Buy notification sent: ${amount} tokens`)
  } catch (error: any) {
    console.error('Error buying token:', error)
    toast.error('Error buying token')
  }
}

const handleTokenSell = async (amount: number) => {
  try {
    // Send WebSocket notification about the sell transaction
    // trading.notifyTokenSell(
    //   amount,
    //   trading.tokenInfo.value?.currentPrice?.priceLamports
    // )
    console.log(`Sell notification sent: ${amount} tokens`)
  } catch (error: any) {
    console.error('Error selling token:', error)
    toast.error('Error selling token')
  }
}

// Use the reactive ref directly instead of wrapping it in a computed
// const tokenInfo = trading.tokenInfo
</script>

<template>
  <div class="p-3 space-y-3">
    <TradingTokenInfo />
    <TradingForm
      :token-data="tokenInfo"
      @buy="handleTokenBuy"
      @sell="handleTokenSell"
    />
    <TradingMCapProgressIndicator :token-data="tokenInfo" />
  </div>
</template>
