<script setup lang="ts">
const addr = inject<string>('currentTokenAddress')

const tokenInfo = ref<Token | null>(null)

onMounted(async () => {
  tokenInfo.value = await $fetch('/api/tokens/token-display-info', {
    params: {
      address: addr
    }
  })
})
</script>

<template>
  <div class="bg-primary-bg rounded-lg overflow-hidden">
    <div class="w-full h-24 relative">
      <div
        class="absolute font-bold bg-primary-bg/80 px-3 py-1.5 left-1/2 backdrop-blur-sm shadow-sm -translate-x-1/2 rounded-b-lg"
      >
        <div v-if="tokenInfo" class="text-lg">{{ tokenInfo.symbol }}</div>
        <div v-else class="text-lg">Loading...</div>
      </div>
      <img
        class="w-full h-full object-cover"
        src="https://cdn.outsideonline.com/wp-content/uploads/2023/03/Funny_Dog_H.jpg"
        alt=""
      />
    </div>
    <div class="p-3 space-y-3">
      <!-- Token name and basic info -->
      <div v-if="tokenInfo" class="text-center">
        <h3 class="font-bold text-base">{{ tokenInfo.name }}</h3>
        <p class="text-xs text-muted mt-1">{{ tokenInfo.symbol }} Token</p>
      </div>

      <!-- Social buttons -->
      <div class="grid grid-cols-4 gap-2">
        <button
          class="flex items-center justify-center p-2 h-8 rounded bg-gray-100/10 hover:bg-gray-100/20 transition"
        >
          <Icon name="ri:twitter-x-fill" size="14" />
        </button>
        <button
          class="flex items-center justify-center p-2 h-8 rounded bg-gray-100/10 hover:bg-gray-100/20 transition"
        >
          <Icon name="streamline-plump:web" size="14" />
        </button>
        <button
          class="flex items-center justify-center p-2 h-8 rounded bg-gray-100/10 hover:bg-gray-100/20 transition"
        >
          <Icon name="ic:outline-telegram" size="14" />
        </button>
        <button
          class="flex items-center justify-center p-2 h-8 rounded bg-gray-100/10 hover:bg-gray-100/20 transition"
        >
          <Icon name="lucide:more-horizontal" size="14" />
        </button>
      </div>
    </div>
  </div>
</template>
