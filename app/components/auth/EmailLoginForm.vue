<script setup lang="ts">
import TextInput from '../form/TextInput.vue'

const emit = defineEmits(['back'])
const auth = useAuth()

const loading = ref(false)
const email = ref('')
const code = ref('')
const emailError = ref('')
const codeError = ref('')
const isEmailInputEnabled = ref(true)
const currentStep = ref(1)
const { fetch: refreshSession } = useUserSession()

const goToStep = (step: number) => {
  if (step === 2) {
    isEmailInputEnabled.value = false
    loading.value = true
    // Simulate an API call to send the code
    sendLoginCode()
    loading.value = false
    if (emailError.value) {
      // If there is an error, stay on step 1
      currentStep.value = 1
    } else {
      currentStep.value = step
      codeError.value = ''
    }
  } else {
    currentStep.value = step
  }
}

const back = () => {
  if (currentStep.value === 1) {
    // reset everything
    email.value = ''
    code.value = ''
    isEmailInputEnabled.value = true
    currentStep.value = 1
    loading.value = false
    emit('back')
  } else {
    currentStep.value = 1
    isEmailInputEnabled.value = true
  }
}

const sendLoginCode = async () => {
  if (email.value.trim() === '') {
    emailError.value = 'Please enter your email address.'
    return
  }

  try {
    const response = await $fetch('/api/auth/send-login-code', {
      method: 'POST',
      body: { email: email.value }
    })

    return response
  } catch (error) {
    console.error('Error sending login code:', error)
    emailError.value = 'Opps something blew up!'
  }
}

const verifyCode = async () => {
  if (code.value.trim() === '') {
    codeError.value = 'Please enter the code sent to your email.'
    return
  }

  try {
    const response = await $fetch('/api/auth/code-registration', {
      method: 'POST',
      body: { code: code.value }
    })

    await refreshSession()
  } catch (error: any) {
    console.error('Error verifying code:', error)
    codeError.value = error.message
  }
}
</script>

<template>
  <div>
    <button
      v-if="!auth.isLoading"
      class="flex items-center gap-2 text-sm text-muted hover:text-fg-light mt-4"
      @click="back()"
    >
      <Icon name="lucide:arrow-left" />
      back
    </button>
    <div v-else class="flex items-center gap-2 text-sm text-gray-500 mt-4">
      <Icon name="lucide:arrow-left" />
      back
    </div>
    <div v-if="currentStep === 1" class="space-y-4">
      <div class="w-full">
        <TextInput
          v-model="email"
          :disabled="!isEmailInputEnabled"
          placeholder="Enter your email"
          type="email"
          class="mt-4 w-full"
          :error="emailError"
        />
      </div>
      <div>
        <button
          :disabled="loading"
          class="w-full bg-primary-btn-bg flex items-center gap-2 justify-center hover:bg-primary-btn-bg/90 text-fg-light font-semibold py-2 px-4 rounded-md"
          @click="goToStep(currentStep + 1)"
        >
          <Icon
            v-if="loading"
            name="lucide:loader"
            class="animate-spin"
            size="16"
          />
          Continue
        </button>
      </div>
    </div>
    <div v-else-if="currentStep === 2" class="space-y-4">
      <div class="w-full">
        <TextInput
          v-model="code"
          placeholder="Enter the code sent to your email"
          type="text"
          class="mt-4 w-full"
          :error="codeError"
        />
      </div>
      <div>
        <button
          :disabled="loading"
          class="w-full bg-primary-btn-bg flex items-center gap-2 justify-center hover:bg-primary-btn-bg/90 text-fg-light font-semibold py-2 px-4 rounded-md"
          @click="verifyCode()"
        >
          Verify Code
        </button>
      </div>
    </div>
  </div>
</template>
