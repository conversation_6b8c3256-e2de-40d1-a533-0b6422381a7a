<script setup lang="ts">
// emits
const emit = defineEmits<{
  (e: 'emailChosen'): void
  (e: 'walletChosen'): void
}>()
</script>

<template>
  <div class="space-y-3 mt-4">
    <button
      class="p-3 border rounded-md border-muted flex items-center justify-between gap-2 hover:bg-gray-50/5 cursor-pointer w-full"
      @click="emit('emailChosen')"
    >
      <div class="text-sm font-semibold flex items-center gap-2">
        <Icon name="uil:envelope" size="24" />
        <span>Use your email</span>
      </div>
      <Icon name="lucide:chevron-right" size="24" />
    </button>
    <hr class="border-muted" />
    <button
      class="p-3 border rounded-md border-muted flex items-center gap-2 hover:bg-gray-50/5 cursor-pointer w-full"
      @click="emit('walletChosen')"
    >
      <Icon name="token-branded:phantom" size="24" />
      <div class="text-sm font-semibold">Login with Phantom</div>
    </button>
    <button
      class="p-2 mx-auto border text-xs text-muted border-muted rounded-md hover:bg-gray-50/5 hover:text-fg-light"
    >
      ... show all wallets
    </button>
  </div>
</template>
