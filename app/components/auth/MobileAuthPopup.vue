<script setup lang="ts">
import MobileEmailLoginForm from './MobileEmailLoginForm.vue'
import AuthMethods from './AuthMethods.vue'
import {
  DialogClose,
  DialogContent,
  DialogOverlay,
  DialogPortal,
  DialogRoot,
  DialogTrigger
} from 'reka-ui'
import { TransitionPresets, useTransition } from '@vueuse/core'

type AuthMethod = 'email' | 'wallet' | 'none'
const shouldShowMethods = ref(true)
const chosenAuthMethod = shallowRef<AuthMethod>('none')
const prevTabSource = shallowRef(0)
const currentTabSource = shallowRef(120)
const prevTabTransitionFn = useTransition(prevTabSource, {
  duration: 300,
  transition: TransitionPresets.easeInOutCubic
})
const currentTabTransitionFn = useTransition(currentTabSource, {
  duration: 300,
  transition: TransitionPresets.easeInOutCubic,
  onFinished () {
    if (currentTabSource.value === -10) {
      shouldShowMethods.value = false
    }
  }
})

const goBackToMethods = () => {
  shouldShowMethods.value = true
  currentTabSource.value = 120
  prevTabSource.value = 0
  setTimeout(() => {
    chosenAuthMethod.value = 'none'
  }, 300)
}

const changeChosenView = () => {
  prevTabSource.value = 100
  currentTabSource.value = -10
}

const auth = useAuth()

watch(chosenAuthMethod, newMethod => {
  if (newMethod === 'email') {
    changeChosenView()
  } else if (newMethod === 'wallet') {
    // Connect wallet and login
    auth
      .login()
      .then(() => {
        // Reset the auth popup state
        chosenAuthMethod.value = 'none'
        shouldShowMethods.value = true
      })
      .catch(error => {
        console.error('Wallet login failed:', error)
        // Reset state on error
        chosenAuthMethod.value = 'none'
        shouldShowMethods.value = true
      })
  }
})
</script>

<template>
  <DialogRoot>
    <DialogTrigger
      class="bg-fg-light text-gray-600 text-xs font-bold p-1 px-4 rounded-md"
    >
      Login
    </DialogTrigger>
    <DialogPortal>
      <DialogOverlay
        class="backdrop-blur-md bg-gray-50/5 opacity-10 data-[state=open]:animate-overlayShow fixed inset-0 z-30"
      />
      <DialogContent
        class="data-[state=open]:animate-contentShow fixed focus:outline-none z-[100] bg-primary-bg border-0 shadow-md inset-0 rounded-none p-0 max-h-none max-w-none translate-x-0 translate-y-0 top-0 left-0 flex flex-col"
        @interact-outside="
          event => {
            if (auth.isLoading) event.preventDefault()
          }
        "
      >
        <!-- Mobile Header with Back Button -->
        <div
          class="sticky top-0 bg-primary-bg border-b border-gray-100/5 px-4 py-3 flex items-center justify-between z-10"
        >
          <!-- Account for iOS status bar -->
          <div class="w-full pt-safe-top">
            <div class="flex items-center justify-between">
              <DialogClose
                v-if="!auth.isLoading"
                class="flex items-center gap-2 text-gray-300 hover:text-white"
              >
                <Icon name="lucide:chevron-left" size="24" />
                <span class="text-sm font-medium">Login</span>
              </DialogClose>
              <div v-else class="flex items-center gap-2 text-gray-500">
                <Icon name="lucide:chevron-left" size="24" />
                <span class="text-sm font-medium">Login</span>
              </div>

              <div class="w-16" />
              <!-- Spacer for centering -->
            </div>
          </div>
        </div>

        <!-- Content Area -->
        <div class="overflow-hidden flex-1 p-4 pb-safe-bottom">
          <div class="relative min-h-48 w-full">
            <!-- Loading state -->
            <div
              v-if="auth.isLoading"
              class="w-full absolute top-0 left-0 flex flex-col items-center justify-center h-48"
            >
              <div
                class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mb-4"
              />
              <p class="text-sm text-muted">
                Connecting wallet and setting up account...
              </p>
            </div>

            <!-- Auth methods -->
            <AuthMethods
              v-if="shouldShowMethods && !auth.isLoading"
              class="w-full absolute top-0 left-0"
              :style="{
                transform: prevTabTransitionFn
                  ? `translateX(-${prevTabTransitionFn}%)`
                  : 'none',
                marginRight: chosenAuthMethod === 'email' ? '20px' : '0'
              }"
              @email-chosen="chosenAuthMethod = 'email'"
              @wallet-chosen="chosenAuthMethod = 'wallet'"
            />

            <!-- Email form -->
            <MobileEmailLoginForm
              v-show="chosenAuthMethod === 'email' && !auth.isLoading"
              class="w-full absolute left-0 ps-px"
              :style="{
                transform: `translateX(${currentTabTransitionFn}%)`,
                left: '10%'
              }"
              @back="goBackToMethods"
            />
          </div>
        </div>
      </DialogContent>
    </DialogPortal>
  </DialogRoot>
</template>

<style scoped>
/* iOS safe area support */
.pt-safe-top {
  padding-top: env(safe-area-inset-top);
}

.pb-safe-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
</style>
