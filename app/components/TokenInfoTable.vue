<script setup lang="ts">
const addr = inject<string>('currentTokenAddress')

// Mock data for token info
const tokenInfo = ref({
  name: 'MoonShot Token',
  symbol: 'MOON',
  description:
    'A revolutionary meme token built on Solana with community-driven features and fair launch mechanics.',
  totalSupply: '1,000,000,000',
  circulatingSupply: '800,000,000',
  maxSupply: '1,000,000,000',
  decimals: 9,
  contractAddress: addr || '7xKd...J9mP',
  creator: 'DevMarcus',
  createdAt: '2 days ago',
  website: 'https://moonshot.finance',
  twitter: '@MoonShotToken',
  telegram: 't.me/moonshottoken',
  marketCap: '125.6 SOL',
  volume24h: '45.2 SOL',
  holders: '1,234',
  transactions24h: '567'
})
</script>

<template>
  <div class="space-y-4">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Basic Info -->
      <div class="space-y-3">
        <h4 class="font-semibold text-sm border-b border-gray-100/10 pb-2">
          Basic Info
        </h4>

        <div class="space-y-2">
          <div class="flex justify-between text-xs">
            <span class="text-muted">Name:</span>
            <span class="font-semibold">{{ tokenInfo.name }}</span>
          </div>
          <div class="flex justify-between text-xs">
            <span class="text-muted">Symbol:</span>
            <span class="font-semibold">${{ tokenInfo.symbol }}</span>
          </div>
          <div class="flex justify-between text-xs">
            <span class="text-muted">Total Supply:</span>
            <span class="font-semibold">{{ tokenInfo.totalSupply }}</span>
          </div>
          <div class="flex justify-between text-xs">
            <span class="text-muted">Circulating:</span>
            <span class="font-semibold">{{ tokenInfo.circulatingSupply }}</span>
          </div>
          <div class="flex justify-between text-xs">
            <span class="text-muted">Decimals:</span>
            <span class="font-semibold">{{ tokenInfo.decimals }}</span>
          </div>
          <div class="flex justify-between text-xs">
            <span class="text-muted">Creator:</span>
            <span class="font-semibold">{{ tokenInfo.creator }}</span>
          </div>
          <div class="flex justify-between text-xs">
            <span class="text-muted">Created:</span>
            <span class="font-semibold">{{ tokenInfo.createdAt }}</span>
          </div>
        </div>
      </div>

      <!-- Market Data -->
      <div class="space-y-3">
        <h4 class="font-semibold text-sm border-b border-gray-100/10 pb-2">
          Market Data
        </h4>

        <div class="space-y-2">
          <div class="flex justify-between text-xs">
            <span class="text-muted">Market Cap:</span>
            <span class="font-semibold text-green-400">{{
              tokenInfo.marketCap
            }}</span>
          </div>
          <div class="flex justify-between text-xs">
            <span class="text-muted">24h Volume:</span>
            <span class="font-semibold">{{ tokenInfo.volume24h }}</span>
          </div>
          <div class="flex justify-between text-xs">
            <span class="text-muted">Holders:</span>
            <span class="font-semibold">{{ tokenInfo.holders }}</span>
          </div>
          <div class="flex justify-between text-xs">
            <span class="text-muted">24h Transactions:</span>
            <span class="font-semibold">{{ tokenInfo.transactions24h }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Description -->
    <div class="space-y-2">
      <h4 class="font-semibold text-sm border-b border-gray-100/10 pb-2">
        Description
      </h4>
      <p class="text-muted text-xs leading-relaxed">
        {{ tokenInfo.description }}
      </p>
    </div>

    <!-- Contract Address -->
    <div class="space-y-2">
      <h4 class="font-semibold text-sm border-b border-gray-100/10 pb-2">
        Contract
      </h4>
      <div class="flex items-center gap-2 p-2 bg-gray-100/5 rounded-lg">
        <span class="font-mono text-xs flex-1">{{
          tokenInfo.contractAddress
        }}</span>
        <button class="text-blue-400 hover:text-blue-300 p-1">
          <Icon name="lucide:copy" size="12" />
        </button>
        <button class="text-blue-400 hover:text-blue-300 p-1">
          <Icon name="lucide:external-link" size="12" />
        </button>
      </div>
    </div>

    <!-- Social Links -->
    <div class="space-y-2">
      <h4 class="font-semibold text-sm border-b border-gray-100/10 pb-2">
        Links
      </h4>
      <div class="flex gap-2">
        <a
          href="#"
          class="flex items-center gap-1 px-2 py-1 bg-gray-100/10 hover:bg-gray-100/20 rounded-lg text-xs font-semibold transition"
        >
          <Icon name="lucide:globe" size="12" />
          Web
        </a>
        <a
          href="#"
          class="flex items-center gap-1 px-2 py-1 bg-gray-100/10 hover:bg-gray-100/20 rounded-lg text-xs font-semibold transition"
        >
          <Icon name="ri:twitter-x-fill" size="12" />
          X
        </a>
        <a
          href="#"
          class="flex items-center gap-1 px-2 py-1 bg-gray-100/10 hover:bg-gray-100/20 rounded-lg text-xs font-semibold transition"
        >
          <Icon name="ic:outline-telegram" size="12" />
          TG
        </a>
      </div>
    </div>
  </div>
</template>
