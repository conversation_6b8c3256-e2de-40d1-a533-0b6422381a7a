<script setup lang="ts">
interface PostMedia {
  id: number
  url: string
  type: string
  width?: number | null
  height?: number | null
  order: number
}

interface Props {
  media: PostMedia[]
  postId: number
}

const posts = usePostsStore()
const props = defineProps<Props>()

const getImageGridLayout = (imageCount: number) => {
  if (imageCount === 1) {
    return {
      containerClass: 'grid grid-cols-1 gap-1 rounded-lg overflow-hidden mt-3',
      imageClass: 'w-full h-auto max-h-96 object-cover cursor-pointer',
      showOverlay: false
    }
  } else if (imageCount === 2) {
    return {
      containerClass: 'grid grid-cols-2 gap-1 rounded-lg overflow-hidden mt-3',
      imageClass: 'w-full h-48 object-cover cursor-pointer',
      showOverlay: false
    }
  } else if (imageCount === 3) {
    return {
      containerClass:
        'grid grid-cols-2 grid-rows-2 gap-1 rounded-lg overflow-hidden mt-3',
      imageClasses: [
        'w-full h-96 object-cover row-span-2 cursor-pointer',
        'w-full h-47 object-cover cursor-pointer',
        'w-full h-47 object-cover cursor-pointer'
      ],
      showOverlay: false
    }
  } else if (imageCount === 4) {
    return {
      containerClass: 'grid grid-cols-2 gap-1 rounded-lg overflow-hidden mt-3',
      imageClass: 'w-full h-48 object-cover cursor-pointer',
      showOverlay: false
    }
  } else {
    return {
      containerClass:
        'grid grid-cols-2 grid-rows-2 gap-1 rounded-lg overflow-hidden mt-3',
      imageClasses: [
        'w-full h-96 object-cover row-span-2 cursor-pointer hover:opacity-90',
        'w-full h-47 object-cover cursor-pointer hover:opacity-90',
        'w-full h-47 object-cover cursor-pointer hover:opacity-90 relative'
      ],
      showOverlay: true,
      overlayCount: imageCount - 3
    }
  }
}

const layout = computed(() => getImageGridLayout(props.media.length))

const openImageModal = (index: number) => {
  posts.handleOpenPostOverlay(props.postId, index)
}
</script>

<template>
  <div
    v-if="media.length > 0"
    :class="layout.containerClass"
    @click.stop="posts.handleOpenPostOverlay(props.postId, 0)"
  >
    <!-- Single image -->
    <template v-if="media.length === 1">
      <img
        :src="media[0].url"
        :alt="`Post image 1`"
        :class="layout.imageClass"
        @click.stop="openImageModal(0)"
      />
    </template>

    <!-- Two images -->
    <template v-else-if="media.length === 2">
      <img
        v-for="(image, index) in media"
        :key="image.id"
        :src="image.url"
        :alt="`Post image ${index + 1}`"
        :class="layout.imageClass"
        @click.stop="openImageModal(index)"
      />
    </template>

    <!-- Three images -->
    <template v-else-if="media.length === 3">
      <img
        v-for="(image, index) in media"
        :key="image.id"
        :src="image.url"
        :alt="`Post image ${index + 1}`"
        :class="layout.imageClasses![index]"
        @click.stop="openImageModal(index)"
      />
    </template>

    <!-- Four images -->
    <template v-else-if="media.length === 4">
      <img
        v-for="(image, index) in media"
        :key="image.id"
        :src="image.url"
        :alt="`Post image ${index + 1}`"
        :class="layout.imageClass"
        @click.stop="openImageModal(index)"
      />
    </template>

    <!-- Five or more images -->
    <template v-else>
      <!-- First image (large) -->
      <img
        :src="media[0].url"
        :alt="`Post image 1`"
        :class="layout.imageClasses![0]"
        @click.stop="openImageModal(0)"
      />

      <!-- Second image -->
      <img
        :src="media[1].url"
        :alt="`Post image 2`"
        :class="layout.imageClasses![1]"
        @click.stop="openImageModal(1)"
      />

      <!-- Third image with overlay -->
      <div class="relative">
        <img
          :src="media[2].url"
          :alt="`Post image 3`"
          :class="layout.imageClasses![2]"
          @click.stop="openImageModal(2)"
        />
        <div
          class="absolute inset-0 bg-black bg-opacity-60 flex items-center justify-center cursor-pointer hover:bg-opacity-50 transition-colors"
          @click.stop="openImageModal(2)"
        >
          <span class="text-white text-xl font-bold">
            +{{ layout.overlayCount }}
          </span>
        </div>
      </div>
    </template>
  </div>
</template>

<style scoped>
.h-47 {
  height: 11.75rem; /* 188px - roughly half of h-96 minus gap */
}
</style>
