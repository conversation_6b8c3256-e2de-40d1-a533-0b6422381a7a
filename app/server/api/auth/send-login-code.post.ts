import prisma from '@/lib/prisma'

// passwordless login
export default defineEventHandler(async event => {
  const body = await readBody(event)
  const { email } = body

  if (!email) {
    throw createError({
      statusCode: 400,
      message: 'Email is required'
    })
  }

  // validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    throw createError({
      statusCode: 400,
      message: 'Invalid email address'
    })
  }

  let user = await prisma.user.findUnique({
    where: { email }
  })

  // send login code via email
  if (!user) {
    // generate a random name
    const randomName = `user_${Math.random().toString(36).substring(2, 8)}`
    // create a user
    user = await prisma.user.create({
      data: { email, address: '', name: randomName, displayName: 'Unnamed' }
    })
  }

  // send a login code to the new user
  const { sendMail } = useNodeMailer()
  const loginCode = generateCode()

  await prisma.authCode.create({
    data: {
      userId: user.id,
      code: loginCode
    }
  })

  await sendMail({
    to: email,
    subject: 'Your Login Code',
    text: `Your login code is: ${loginCode}`
  })

  return { message: 'Login code sent', user }
})
