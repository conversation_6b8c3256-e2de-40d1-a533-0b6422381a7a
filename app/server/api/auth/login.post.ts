import {
  createOrGetUser,
  createUserStateRecord
} from '~/server/services/database'
import { createUserStateOnChain } from '~/server/services/web3'

export default defineEventHandler(async event => {
  try {
    const body = await readBody(event)
    const { publicKey } = body

    if (!publicKey) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Public key is required'
      })
    }

    // Create or get user with onChainId assignment
    const user = await createOrGetUser(publicKey)

    // If user doesn't have on-chain state yet, create it
    if (!user.onChainState && user.onChainId) {
      try {
        // Create the on-chain user state (authority-only, no user signature)
        const statePda = await createUserStateOnChain(user.onChainId)

        // Create the database record
        const userState = await createUserStateRecord(
          user.id,
          user.onChainId,
          statePda
        )

        // Update the user object with the new state
        user.onChainState = {
          id: userState.id,
          onChainId: userState.onChainId,
          statePda: userState.statePda,
          tokenCount: userState.tokenCount
        }
      } catch (error) {
        console.error('Failed to create on-chain user state:', error)
        // Don't fail the login, just log the error
        // User can try again later or we can retry in the background
      }
    }

    // Set user session using nuxt-auth-utils
    await setUserSession(event, {
      user: {
        id: user.id,
        address: user.address,
        name: user.name,
        email: user.email,
        onChainId: user.onChainId
      },
      loggedInAt: new Date().toISOString()
    })

    return {
      success: true,
      user: {
        id: user.id,
        address: user.address,
        name: user.name,
        email: user.email,
        onChainId: user.onChainId,
        hasOnChainState: !!user.onChainState
      }
    }
  } catch (error) {
    console.error('Login error:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Login failed'
    })
  }
})
