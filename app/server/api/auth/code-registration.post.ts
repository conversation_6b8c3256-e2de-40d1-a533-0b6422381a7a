import prisma from '~/lib/prisma'
import { generateSessionToken } from '../../utils'

export default defineEventHandler(async event => {
  const body = await readBody(event)
  const { code } = body

  if (!code) {
    throw createError({
      statusCode: 400,
      message: 'Code is required'
    })
  }

  // get code from database
  const codeRecord = await prisma.authCode.findUnique({
    where: { code },
    include: { user: true }
  })

  // code should be less that 10mins ago
  if (
    !codeRecord ||
    new Date(codeRecord.createdAt).getTime() < Date.now() - 10 * 60 * 1000
  ) {
    throw createError({
      statusCode: 400,
      message: 'Invalid or expired code'
    })
  }

  // check if code matches
  if (codeRecord.code !== code) {
    throw createError({
      statusCode: 400,
      message: 'Invalid code'
    })
  }

  // create a session and login the user
  await setUserSession(event, {
    user: codeRecord.user
  })

  return {}
})
