import prisma from '~/lib/prisma'

export default defineEventHandler(async event => {
  try {
    const hashtags = await prisma.hashtag.findMany({
      select: {
        id: true,
        name: true,
        postCount: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        postCount: 'desc'
      }
    })

    return hashtags
  } catch (error) {
    console.error('Error fetching hashtags:', error)
    return []
  }
})
