import prisma from '~/lib/prisma'
import { useTradingService } from '~/server/services/trading'

export default defineEventHandler(async event => {
  try {
    const query = getQuery(event)
    const limit = parseInt(query.limit as string) || 5

    // Get trending tokens based on trading volume in the last 24 hours
    const yesterday = new Date()
    yesterday.setHours(yesterday.getHours() - 24)
    const tradingService = useTradingService()

    // Get tokens with most trading activity (transactions) in the last 24 hours
    const trending = await prisma.token.findMany({
      select: {
        id: true,
        name: true,
        symbol: true,
        address: true,
        createdAt: true,
        transactions: {
          where: {
            createdAt: {
              gte: yesterday
            }
          },
          select: {
            id: true,
            tokenAmount: true,
            amountLamports: true,
            type: true,
            createdAt: true
          }
        },
        _count: {
          select: {
            transactions: {
              where: {
                createdAt: {
                  gte: yesterday
                }
              }
            }
          }
        }
      },
      orderBy: {
        transactions: {
          _count: 'desc'
        }
      },
      take: limit * 2 // Get more to filter properly
    })

    // Calculate trading volume and format results
    const formattedResults = trending
      .map(async token => {
        const curveData = await tradingService.getTokenCurveAccount(
          token.address
        )
        const transactions = token.transactions
        const transactionCount = token._count.transactions

        // Calculate trading volume in SOL for the day
        const totalVolume = transactions.reduce((sum, tx) => {
          return sum + Number(tx.amountLamports || 0)
        }, 0)

        // Only include tokens that have actual trading activity
        if (transactionCount === 0) return null
        const solPrice = getLastCachedPrice() ?? 0

        // Calculate mock market cap based on trading volume and activity
        const totalSupplyCostInSol = 1_000_000_000 * (curveData.lastPrice ?? 0)
        const totalSupplyCostInUsd = totalSupplyCostInSol * solPrice

        const baseMcap = totalSupplyCostInUsd
        console.log(baseMcap)
        const mcap =
          baseMcap > 1000
            ? `$${(baseMcap / 1000).toFixed(2)}k`
            : `$${Math.round(baseMcap)}`

        // Calculate change percentage based on trading activity
        // More trades = higher positive change (trending up)
        const changePercent = Math.min(
          50,
          transactionCount * 2 + Math.random() * 10
        )
        const change = `+${changePercent.toFixed(1)}%`

        return {
          id: token.id,
          symbol: token.symbol,
          name: token.name,
          address: token.address,
          mcap,
          change,
          volume: totalVolume.toFixed(2),
          transactions: transactionCount
        }
      })
      .map(promise => promise.catch(() => null)) // Handle any errors in mapping

    // Wait for all promises to resolve
    const results = await Promise.all(formattedResults)

    // Filter out null results and limit to requested number
    const filteredResults = results
      .filter(Boolean) // Remove null entries
      .slice(0, limit) // Take only the requested limit

    return {
      tokens: filteredResults
    }
  } catch (error) {
    console.error('Error fetching trending tokens:', error)
    return {
      tokens: []
    }
  }
})
