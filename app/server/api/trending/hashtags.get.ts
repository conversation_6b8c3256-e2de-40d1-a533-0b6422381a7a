import prisma from '~/lib/prisma'

export default defineEventHandler(async event => {
  try {
    const query = getQuery(event)
    const limit = parseInt(query.limit as string) || 5

    // Get all posts from the last 24 hours that contain hashtags
    const posts = await prisma.post.findMany({
      where: {
        content: {
          contains: '#'
        },
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
        }
      },
      select: {
        content: true
      }
    })

    // Get all hashtags to resolve IDs to names
    const allHashtags = await prisma.hashtag.findMany({
      select: {
        id: true,
        name: true
      }
    })

    // Create a map for quick hashtag ID to name lookup
    const hashtagMap = new Map<number, string>()
    allHashtags.forEach(hashtag => {
      hashtagMap.set(hashtag.id, hashtag.name)
    })

    // Extract hashtags from post content and count them
    const hashtagCounts = new Map<string, number>()

    posts.forEach(post => {
      // Extract hashtags using regex (both ID format and name format)
      const hashtags = post.content.match(/#[a-zA-Z0-9_]+/g) || []
      hashtags.forEach(hashtag => {
        const tagValue = hashtag.substring(1) // Remove #

        let actualTagName = tagValue.toLowerCase()

        // If it's a number (hashtag ID), resolve it to the actual hashtag name
        if (/^\d+$/.test(tagValue)) {
          const hashtagId = parseInt(tagValue)
          const resolvedName = hashtagMap.get(hashtagId)
          if (resolvedName) {
            actualTagName = resolvedName.toLowerCase()
          }
        }

        hashtagCounts.set(
          actualTagName,
          (hashtagCounts.get(actualTagName) || 0) + 1
        )
      })
    })

    // Sort by count and take the top ones
    const sortedHashtags = Array.from(hashtagCounts.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, limit)
      .map(([tag, count]) => ({
        tag,
        posts: count
      }))

    return {
      hashtags: sortedHashtags
    }
  } catch (error) {
    console.error('Error fetching trending hashtags:', error)

    // Return fallback data if there's an error
    return {
      hashtags: [
        { tag: 'solana', posts: 1234 },
        { tag: 'defi', posts: 892 },
        { tag: 'memecoin', posts: 743 },
        { tag: 'web3', posts: 567 },
        { tag: 'crypto', posts: 421 }
      ]
    }
  }
})
