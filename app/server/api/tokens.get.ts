import prisma from '~/lib/prisma'

export default defineEventHandler(async event => {
  try {
    const tokens = await prisma.token.findMany({
      select: {
        id: true,
        name: true,
        symbol: true,
        address: true,
        userId: true,
        createdAt: true,
        updatedAt: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    return tokens
  } catch (error) {
    console.error('Error fetching tokens:', error)
    return []
  }
})
