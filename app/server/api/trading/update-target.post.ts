import { BN } from 'bn.js'
import { PublicKey } from '@solana/web3.js'
import { initializeProgram } from '~/server/services/web3'

export default defineEventHandler(async event => {
  try {
    const body = await readBody(event)
    const { tokenAddress, newTargetSol, reason } = body

    if (!tokenAddress || !newTargetSol) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Token address and new target are required'
      })
    }

    // CRITICAL: Validate target doesn't exceed smart contract limit
    const MAX_TARGET_SOL = 10000
    if (newTargetSol > MAX_TARGET_SOL) {
      throw createError({
        statusCode: 400,
        statusMessage: `Target market cap cannot exceed ${MAX_TARGET_SOL} SOL (smart contract limit)`
      })
    }

    // PROFIT OPTIMIZATION: Validate minimum target for profitability
    const MIN_TARGET_SOL = 50 // Don't bother with tiny targets
    if (newTargetSol < MIN_TARGET_SOL) {
      throw createError({
        statusCode: 400,
        statusMessage: `Target market cap must be at least ${MIN_TARGET_SOL} SOL for profitability`
      })
    }

    console.log(
      `✅ Updating target market cap for ${tokenAddress} to ${newTargetSol} SOL (within limits). Reason: ${reason}`
    )

    // Initialize the Anchor program
    const program = await initializeProgram()

    // Convert addresses to PublicKey objects
    const mintPubkey = new PublicKey(tokenAddress)

    // Get the curve account PDA
    const [tokenCurveAccount] = PublicKey.findProgramAddressSync(
      [Buffer.from('curve'), mintPubkey.toBuffer()],
      program.programId
    )

    // Convert new target to lamports
    const newTargetLamports = new BN(newTargetSol * 1e9)

    // For now, we'll use a platform authority keypair
    // In production, this should be properly secured
    const platformAuthority = program.provider.wallet

    if (!platformAuthority) {
      throw createError({
        statusCode: 500,
        statusMessage: 'Platform authority not configured'
      })
    }

    try {
      // Call the update_target_sol_reserves instruction
      const tx = await program.methods
        .updateTargetSolReserves(newTargetLamports)
        .accountsStrict({
          platformAuthority: platformAuthority.publicKey,
          mint: mintPubkey,
          tokenCurveAccount: tokenCurveAccount
        })
        .rpc()

      console.log(
        `Target SOL reserves updated successfully. Transaction: ${tx}`
      )

      return {
        success: true,
        transaction: tx,
        newTarget: {
          sol: newTargetSol,
          lamports: newTargetLamports.toString()
        },
        reason
      }
    } catch (updateError: any) {
      console.error('Error updating target SOL reserves on-chain:', updateError)

      // If on-chain update fails, we can still track it off-chain
      // This ensures the dynamic calculation continues working
      return {
        success: false,
        error: updateError.message,
        fallbackMode: true,
        newTarget: {
          sol: newTargetSol,
          lamports: newTargetLamports.toString()
        },
        reason
      }
    }
  } catch (error) {
    console.error('Error in update target endpoint:', error)
    throw createError({
      statusCode: 500,
      statusMessage:
        error instanceof Error ? error.message : 'Internal server error'
    })
  }
})
