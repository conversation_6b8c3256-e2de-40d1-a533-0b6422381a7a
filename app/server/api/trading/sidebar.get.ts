import { BN } from 'bn.js'
import { PublicKey } from '@solana/web3.js'
import { initializeProgram } from '~/server/services/web3'

export default defineEventHandler(async event => {
  try {
    const query = getQuery(event)
    const { address } = query

    if (!address) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Token address is required'
      })
    }

    // Initialize the Anchor program
    const program = await initializeProgram()

    // Convert address to PublicKey
    const mintPubkey = new PublicKey(address as string)

    // Get the curve account PDA
    const [tokenCurveAccount] = PublicKey.findProgramAddressSync(
      [Buffer.from('curve'), mintPubkey.toBuffer()],
      program.programId
    )

    try {
      // Fetch the curve account data
      const curveData =
        await program.account.tokenCurveAccount.fetch(tokenCurveAccount)

      // Calculate circulating supply (tokens sold from the curve)
      const totalSupply = new BN('**********').mul(new BN('**********')) // 1B tokens with 9 decimals
      const initialTokenReserves = new BN('*********').mul(new BN('**********')) // 800M initial tokens
      const currentTokenReserves = new BN(curveData.tokenReserves.toString())
      const circulatingSupply = initialTokenReserves.sub(currentTokenReserves)

      // Calculate current price per token (in lamports) using constant product formula
      const solReserves = new BN(curveData.solReserves.toString())

      // Avoid division by zero
      if (currentTokenReserves.isZero()) {
        throw new Error('Token reserves cannot be zero')
      }

      const priceNumerator = solReserves.mul(new BN('**********')) // Scale for precision
      const currentPricePerTokenLamports =
        priceNumerator.div(currentTokenReserves)

      // Calculate real market cap: circulating supply * current price
      const realMarketCapLamports = circulatingSupply
        .mul(currentPricePerTokenLamports)
        .div(new BN('**********'))

      // Convert to SOL safely using division instead of toNumber()
      const realMarketCapSol =
        parseFloat(realMarketCapLamports.toString()) / 1e9

      // Get target market cap from on-chain data (this can be dynamically adjusted)
      const targetMarketCapLamports = new BN(
        32_000_000_000 // 32 SOL in lamports
      )
      const targetMarketCapSol =
        parseFloat(targetMarketCapLamports.toString()) / 1e9

      // Calculate progress towards target
      const progressToTarget = Math.min(
        (realMarketCapSol / targetMarketCapSol) * 100,
        100
      )

      // Check if near migration (99.5% threshold)
      const isNearMigration = progressToTarget >= 99.5

      // Price velocity calculation (will be enhanced with off-chain data)
      const currentPrice =
        parseFloat(currentPricePerTokenLamports.toString()) / 1e9
      const onChainLastPrice = curveData.lastPrice || 0
      const priceVelocity =
        onChainLastPrice > 0
          ? (currentPrice - onChainLastPrice) / onChainLastPrice
          : 0

      return {
        success: true,
        address: address as string,
        marketCap: {
          real: {
            lamports: realMarketCapLamports.toString(),
            sol: realMarketCapSol,
            formatted: `${realMarketCapSol.toFixed(4)} SOL`
          },
          target: {
            lamports: targetMarketCapLamports.toString(),
            sol: targetMarketCapSol,
            base: 332, // Base target before dynamic adjustments
            dynamic: targetMarketCapSol, // Will be adjusted by off-chain logic
            formatted: `${targetMarketCapSol.toFixed(0)} SOL`
          },
          progress: {
            percentage: progressToTarget,
            isNearMigration
          }
        },
        supply: {
          total: totalSupply.toString(),
          circulating: circulatingSupply.toString(),
          reserves: currentTokenReserves.toString(),
          circulatingPercentage: parseFloat(
            circulatingSupply.mul(new BN(100)).div(totalSupply).toString()
          )
        },
        pricing: {
          currentPricePerToken: parseFloat(
            currentPricePerTokenLamports.toString()
          ),
          currentPricePerTokenSol: currentPrice,
          lastPriceOnChain: onChainLastPrice,
          priceVelocity
        },
        reserves: {
          sol: curveData.solReserves.toString(),
          tokens: curveData.tokenReserves.toString()
        },
        onChain: {
          creator: curveData.creator.toString(),
          lastPurchaseTimestamp:
            curveData.lastPurchaseTimestamp?.toString() || '0',
          purchaseCountWindow: curveData.purchaseCountWindow || 0
        },
        timestamp: Date.now()
      }
    } catch (error) {
      console.error('Error getting sidebar data:', error)
      // If curve account doesn't exist, return default values for new tokens
      console.log('Curve account not found, using default values')

      return {
        success: false,
        address: address as string,
        marketCap: {
          real: {
            lamports: '0',
            sol: 0,
            formatted: '0.0000 SOL'
          },
          target: {
            lamports: '************', // 332 SOL in lamports
            sol: 332,
            base: 332,
            dynamic: 332,
            formatted: '332 SOL'
          },
          progress: {
            percentage: 0,
            isNearMigration: false
          }
        },
        supply: {
          total: '**********000000000', // 1B tokens
          circulating: '0',
          reserves: '*********000000000', // 800M tokens
          circulatingPercentage: 0
        },
        pricing: {
          currentPricePerToken: 0,
          currentPricePerTokenSol: 0,
          lastPriceOnChain: 0,
          priceVelocity: 0
        },
        reserves: {
          sol: '***********', // Default 59 SOL
          tokens: '*********000000000' // Default 800M tokens
        },
        onChain: {
          creator: '',
          lastPurchaseTimestamp: '0',
          purchaseCountWindow: 0
        },
        timestamp: Date.now()
      }
    }
  } catch (error) {
    console.error('Error fetching sidebar data:', error)
    throw createError({
      statusCode: 500,
      statusMessage:
        error instanceof Error ? error.message : 'Internal server error'
    })
  }
})
