import prisma from '~/lib/prisma'

export default defineEventHandler(async event => {
  const session = await requireUserSession(event)
  const userId = session.user?.id

  if (!userId) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized'
    })
  }

  const { notificationId } = await readBody(event)

  if (!notificationId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Notification ID is required'
    })
  }

  try {
    await prisma.notification.update({
      where: {
        id: notificationId,
        targetUserId: userId // Ensure user can only mark their own notifications
      },
      data: {
        read: true
      }
    })

    return { success: true }
  } catch (error) {
    console.error('Failed to mark notification as read:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to mark notification as read'
    })
  }
})
