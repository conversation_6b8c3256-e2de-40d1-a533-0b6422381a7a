import prisma from '~/lib/prisma'

export default defineEventHandler(async event => {
  const session = await requireUserSession(event)
  const userId = session.user?.id

  if (!userId) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized'
    })
  }

  try {
    await prisma.notification.updateMany({
      where: {
        targetUserId: userId,
        read: false
      },
      data: {
        read: true
      }
    })

    return { success: true }
  } catch (error) {
    console.error('Failed to mark all notifications as read:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to mark all notifications as read'
    })
  }
})
