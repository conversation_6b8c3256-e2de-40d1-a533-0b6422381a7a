import prisma from '~/lib/prisma'

export default defineEventHandler(async event => {
  const session = await requireUserSession(event)
  const userId = session.user?.id

  if (!userId) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized'
    })
  }

  const query = getQuery(event)
  const limit = Number(query.limit) || 20
  const offset = Number(query.offset) || 0

  try {
    const notifications = await prisma.notification.findMany({
      where: {
        targetUserId: userId
      },
      include: {
        sourceUser: {
          select: {
            id: true,
            name: true,
            address: true
          }
        },
        post: {
          select: {
            id: true,
            content: true
          }
        },
        token: {
          select: {
            id: true,
            name: true,
            symbol: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset
    })

    // Transform to match frontend interface
    const transformedNotifications = notifications.map(notification => ({
      id: notification.id,
      type: notification.type.toLowerCase(),
      content: notification.content,
      createdAt: notification.createdAt.toISOString(),
      read: notification.read,
      sourceUser: notification.sourceUser
        ? {
            id: notification.sourceUser.id,
            username: notification.sourceUser.name || 'Unknown',
            displayName: notification.sourceUser.name || 'Unknown User'
          }
        : null,
      post: notification.post,
      token: notification.token
    }))

    return transformedNotifications
  } catch (error) {
    console.error('Failed to fetch notifications:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch notifications'
    })
  }
})
