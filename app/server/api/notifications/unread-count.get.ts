import prisma from '~/lib/prisma'

export default defineEventHandler(async event => {
  const session = await requireUserSession(event)
  const userId = session.user?.id

  if (!userId) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized'
    })
  }

  try {
    const count = await prisma.notification.count({
      where: {
        targetUserId: userId,
        read: false
      }
    })

    return { count }
  } catch (error) {
    console.error('Failed to get unread count:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get unread count'
    })
  }
})
