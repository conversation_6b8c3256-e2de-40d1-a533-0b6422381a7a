// this is going to be replaced by another helius based solution

import { <PERSON>Key } from '@solana/web3.js'
import { BN } from 'bn.js'
import { initializeProgram } from '~/server/services/web3'

interface SolanaWsMessage {
  type: 'subscribe' | 'unsubscribe' | 'data' | 'error' | 'ping' | 'pong'
  tokenAddress?: string
  data?: any
}

interface TokenSubscription {
  tokenAddress: string
  curveAccountAddress: string
  subscriptionId?: number
}

// Store active subscriptions for each peer
const peerSubscriptions = new Map<any, Set<TokenSubscription>>()
let solanaWsConnection: WebSocket | null = null
const subscriptionMap = new Map<string, number>() // curve account address -> subscription ID
const subscriptionToAccountMap = new Map<number, string>() // subscription ID -> curve account address
const tokenAddressMap = new Map<string, string>() // curve account address -> token address
const pendingSubscriptions = new Map<number, string>() // request ID -> curve account address
let reconnectTimeout: NodeJS.Timeout | null = null

// Initialize Solana WebSocket connection for on-demand curve account monitoring
const initializeSolanaWsConnection = () => {
  if (solanaWsConnection?.readyState === WebSocket.OPEN) return

  // Use the WebSocket endpoint that matches your RPC endpoint
  // If RPC is http://localhost:8899, WebSocket should be ws://localhost:8900
  // For mainnet: wss://api.mainnet-beta.solana.com/
  // For devnet: wss://api.devnet.solana.com/
  let wsUrl = process.env.SOLANA_WS_URL

  if (!wsUrl) {
    // Derive WebSocket URL from RPC URL
    const rpcUrl = process.env.WEB3_PROVIDER_URL || 'http://localhost:8899'
    if (rpcUrl.includes('localhost:8899')) {
      wsUrl = 'ws://localhost:8900'
    } else if (rpcUrl.includes('mainnet')) {
      wsUrl = 'wss://api.mainnet-beta.solana.com/'
    } else if (rpcUrl.includes('devnet')) {
      wsUrl = 'wss://api.devnet.solana.com/'
    } else {
      wsUrl = 'ws://localhost:8900' // fallback
    }
  }

  console.log(
    `🔗 Initializing Solana WebSocket connection for on-demand monitoring to: ${wsUrl}`
  )
  console.log(
    `📡 Derived from RPC URL: ${process.env.WEB3_PROVIDER_URL || 'http://localhost:8899'}`
  )

  solanaWsConnection = new WebSocket(wsUrl)

  solanaWsConnection.addEventListener('open', () => {
    console.log(
      '🟢 Solana WebSocket connection established for on-demand curve monitoring'
    )

    // Test the connection with a simple request
    const testRequest = {
      jsonrpc: '2.0',
      id: 1, // Use integer instead of string
      method: 'getVersion',
      params: []
    }

    console.log(
      '🧪 Sending test request to verify Solana connection:',
      JSON.stringify(testRequest)
    )
    if (solanaWsConnection) {
      solanaWsConnection.send(JSON.stringify(testRequest))
    }

    if (reconnectTimeout) {
      clearTimeout(reconnectTimeout)
      reconnectTimeout = null
    }
  })

  solanaWsConnection.addEventListener('message', event => {
    try {
      const data = JSON.parse(event.data)

      // Handle errors specifically
      if (data.error) {
        console.error('Solana WebSocket returned an error:', data.error)
        return
      }

      handleSolanaAccountUpdate(data)
    } catch (error) {
      console.error('❌ Error parsing Solana WebSocket message:', error)
      console.log('Raw message data:', event.data)
    }
  })
  solanaWsConnection.addEventListener('close', event => {
    console.log('Solana WebSocket connection closed:', event.code, event.reason)
    solanaWsConnection = null

    // Clear all subscriptions since connection is lost
    subscriptionMap.clear()
    subscriptionToAccountMap.clear()
    pendingSubscriptions.clear()

    // Reconnect after a delay with exponential backoff
    if (!reconnectTimeout) {
      const delay = Math.min(5000 * Math.pow(2, 0), 30000) // Start with 5s, max 30s
      reconnectTimeout = setTimeout(() => {
        initializeSolanaWsConnection()
      }, delay)
    }
  })

  solanaWsConnection.addEventListener('error', error => {
    console.error('Solana WebSocket error:', error)
  })
}

// Handle Solana account updates and broadcast to relevant peers
const handleSolanaAccountUpdate = async (data: any) => {
  try {
    // Handle subscription confirmations
    if (data.result && typeof data.result === 'number' && data.id) {
      const subscriptionId = data.result
      const requestId = data.id

      // Check if this was a pending subscription and store the mapping
      const curveAccountAddress = pendingSubscriptions.get(requestId)
      if (curveAccountAddress) {
        subscriptionMap.set(curveAccountAddress, subscriptionId)
        subscriptionToAccountMap.set(subscriptionId, curveAccountAddress) // Add reverse mapping
        pendingSubscriptions.delete(requestId)
      }

      return
    }

    // Handle account change notifications
    if (data.method === 'accountNotification' && data.params?.result?.value) {
      const { value } = data.params.result
      const subscriptionId = data.params.subscription

      // Get curve account address from subscription ID
      const curveAccountAddress = subscriptionToAccountMap.get(subscriptionId)
      const tokenAddress = tokenAddressMap.get(curveAccountAddress || '')
      if (!tokenAddress || !curveAccountAddress) {
        return // Not a curve account we're monitoring
      }

      // Process the curve account update and notify relevant peers
      await processCurveAccountUpdate(tokenAddress, curveAccountAddress, value)
    } else {
      // Log unexpected messages for debugging
      console.log('Received unexpected Solana message:', {
        method: data.method,
        hasParams: !!data.params,
        hasResult: !!data.result,
        type: typeof data.result
      })
    }
  } catch (error) {
    console.error('Error handling Solana account update:', error)
  }
}

// Process curve account updates and broadcast to subscribed peers
const processCurveAccountUpdate = async (
  tokenAddress: string,
  curveAccountAddress: string,
  accountData: any
) => {
  try {
    const program = await initializeProgram()

    // Fetch the fresh curve account data from the blockchain
    const mintPubkey = new PublicKey(tokenAddress)
    const [tokenCurveAccount] = PublicKey.findProgramAddressSync(
      [Buffer.from('curve'), mintPubkey.toBuffer()],
      program.programId
    )

    const curveData =
      await program.account.tokenCurveAccount.fetch(tokenCurveAccount)

    // Calculate current price using the same logic as price.get.ts
    const solReserves = new BN(curveData.solReserves.toString())
    const tokenReserves = new BN(curveData.tokenReserves.toString())

    // Calculate current price (SOL per token)
    const priceNumerator = solReserves.mul(new BN('**********')) // Scale up for precision
    const currentPricePerTokenBN = priceNumerator.div(tokenReserves)
    const currentPricePerToken = currentPricePerTokenBN.toNumber() / 1e9

    // Prepare curve update data
    const curveUpdate = {
      tokenAddress,
      price: currentPricePerToken,
      solReserves: curveData.solReserves.toString(),
      tokenReserves: curveData.tokenReserves.toString(),
      currentPriceLamports: currentPricePerToken * 1e9,
      actualLastPrice: curveData.lastPrice,
      actualLastPriceLamports: curveData.lastPrice * 1e9,
      timestamp: new Date().toISOString(),
      source: 'solana_ws_curve_update'
    }

    // Broadcast to all peers subscribed to this token
    broadcastToTokenSubscribers(tokenAddress, curveUpdate)
  } catch (error) {
    console.error(
      `Error processing curve account update for ${tokenAddress}:`,
      error
    )
  }
}

// Subscribe to a specific token's curve account
const subscribeToTokenCurve = async (tokenAddress: string) => {
  if (!solanaWsConnection || solanaWsConnection.readyState !== WebSocket.OPEN) {
    console.log(
      '⚠️ Solana WebSocket not connected, cannot subscribe to token curve'
    )
    return null
  }

  try {
    const program = await initializeProgram()
    const mintPubkey = new PublicKey(tokenAddress)

    const [tokenCurveAccount] = PublicKey.findProgramAddressSync(
      [Buffer.from('curve'), mintPubkey.toBuffer()],
      program.programId
    )

    const curveAccountAddress = tokenCurveAccount.toBase58()

    // Check if already subscribed
    if (subscriptionMap.has(curveAccountAddress)) {
      return curveAccountAddress
    }

    // Store mapping for later use
    tokenAddressMap.set(curveAccountAddress, tokenAddress)

    // Subscribe to account changes
    const requestId = Math.floor(Date.now() + Math.random() * 1000) // Ensure integer
    const subscriptionRequest = {
      jsonrpc: '2.0',
      id: requestId,
      method: 'accountSubscribe',
      params: [
        curveAccountAddress,
        {
          encoding: 'jsonParsed',
          commitment: 'confirmed'
        }
      ]
    }

    // Store the pending subscription for confirmation tracking
    pendingSubscriptions.set(requestId, curveAccountAddress)

    solanaWsConnection.send(JSON.stringify(subscriptionRequest))
    return curveAccountAddress
  } catch (error) {
    console.error(
      `❌ Error subscribing to curve account for token ${tokenAddress}:`,
      error
    )
    throw error
  }
}

// Unsubscribe from a specific token's curve account
const unsubscribeFromTokenCurve = async (tokenAddress: string) => {
  if (!solanaWsConnection || solanaWsConnection.readyState !== WebSocket.OPEN) {
    return
  }

  try {
    const program = await initializeProgram()
    const mintPubkey = new PublicKey(tokenAddress)
    const [tokenCurveAccount] = PublicKey.findProgramAddressSync(
      [Buffer.from('curve'), mintPubkey.toBuffer()],
      program.programId
    )

    const curveAccountAddress = tokenCurveAccount.toBase58()
    const subscriptionId = subscriptionMap.get(curveAccountAddress)

    if (subscriptionId) {
      const unsubscribeRequest = {
        jsonrpc: '2.0',
        id: Math.floor(Date.now() + Math.random() * 1000), // Ensure integer
        method: 'accountUnsubscribe',
        params: [subscriptionId]
      }

      solanaWsConnection.send(JSON.stringify(unsubscribeRequest))
      subscriptionMap.delete(curveAccountAddress)
      subscriptionToAccountMap.delete(subscriptionId) // Clean up reverse mapping
      tokenAddressMap.delete(curveAccountAddress)
    }
  } catch (error) {
    console.error(
      `Error unsubscribing from curve account for token ${tokenAddress}:`,
      error
    )
  }
}

// Broadcast updates to peers subscribed to a specific token
const broadcastToTokenSubscribers = (tokenAddress: string, data: any) => {
  for (const [peer, subscriptions] of peerSubscriptions) {
    for (const subscription of subscriptions) {
      if (subscription.tokenAddress === tokenAddress) {
        try {
          peer.send(
            JSON.stringify({
              type: 'data',
              tokenAddress,
              data
            })
          )
        } catch (error) {
          console.error('Error broadcasting to peer:', error)
          // Remove dead peer
          peerSubscriptions.delete(peer)
        }
      }
    }
  }
}

// Initialize connection when module loads
// initializeSolanaWsConnection()

export default defineWebSocketHandler({
  open (peer) {
    return
    console.log('[sol-ws] Peer connected', peer.id)
    peerSubscriptions.set(peer, new Set())

    // Send welcome message
    peer.send(
      JSON.stringify({
        type: 'data',
        data: {
          status: 'connected',
          peerId: peer.id,
          solanaWsStatus:
            solanaWsConnection?.readyState === WebSocket.OPEN
              ? 'connected'
              : 'disconnected'
        }
      })
    )
  },

  async message (peer, message) {
    return
    try {
      const msg: SolanaWsMessage = JSON.parse(message.text())

      switch (msg.type) {
        case 'ping':
          peer.send(JSON.stringify({ type: 'pong' }))
          break

        case 'subscribe':
          await handleTokenSubscribe(peer, msg)
          break

        case 'unsubscribe':
          await handleTokenUnsubscribe(peer, msg)
          break

        default:
          console.log('[sol-ws] ❓ Unknown message type:', msg.type)
      }
    } catch (error) {
      console.error('[sol-ws] ❌ Error processing message:', error)
      peer.send(
        JSON.stringify({
          type: 'error',
          data: 'Failed to process message'
        })
      )
    }
  },

  close (peer, event) {
    return
    // Clean up subscriptions for this peer
    const subscriptions = peerSubscriptions.get(peer)
    if (subscriptions) {
      // Check if any other peers are still subscribed to the same tokens
      for (const subscription of subscriptions) {
        let hasOtherSubscribers = false
        for (const [otherPeer, otherSubs] of peerSubscriptions) {
          if (otherPeer !== peer) {
            for (const otherSub of otherSubs) {
              if (otherSub.tokenAddress === subscription.tokenAddress) {
                hasOtherSubscribers = true
                break
              }
            }
          }
          if (hasOtherSubscribers) break
        }

        // If no other subscribers, unsubscribe from Solana
        if (!hasOtherSubscribers) {
          unsubscribeFromTokenCurve(subscription.tokenAddress)
        }
      }
    }

    peerSubscriptions.delete(peer)
  },

  error (peer, error) {
    console.log('[sol-ws] Peer error', peer.id, error)
    peerSubscriptions.delete(peer)
  }
})

async function handleTokenSubscribe (peer: any, msg: SolanaWsMessage) {
  if (!msg.tokenAddress) {
    peer.send(
      JSON.stringify({
        type: 'error',
        data: 'Token address is required for subscription'
      })
    )
    return
  }

  const subscriptions = peerSubscriptions.get(peer)
  if (!subscriptions) {
    return
  }

  try {
    // Subscribe to the token's curve account on Solana
    const curveAccountAddress = await subscribeToTokenCurve(msg.tokenAddress)

    if (curveAccountAddress) {
      // Add subscription for this peer
      const subscription: TokenSubscription = {
        tokenAddress: msg.tokenAddress,
        curveAccountAddress
      }
      subscriptions.add(subscription)

      peer.send(
        JSON.stringify({
          type: 'data',
          tokenAddress: msg.tokenAddress,
          data: {
            status: 'subscribed',
            curveAccountAddress
          }
        })
      )
    } else {
      throw new Error('Failed to subscribe to curve account')
    }
  } catch (error: any) {
    console.error(
      `❌ Error subscribing peer to token ${msg.tokenAddress}:`,
      error
    )
    peer.send(
      JSON.stringify({
        type: 'error',
        tokenAddress: msg.tokenAddress,
        data: `Failed to subscribe to token: ${error.message}`
      })
    )
  }
}

async function handleTokenUnsubscribe (peer: any, msg: SolanaWsMessage) {
  if (!msg.tokenAddress) {
    peer.send(
      JSON.stringify({
        type: 'error',
        data: 'Token address is required for unsubscription'
      })
    )
    return
  }

  const subscriptions = peerSubscriptions.get(peer)
  if (!subscriptions) return

  // Remove subscription for this peer
  for (const subscription of subscriptions) {
    if (subscription.tokenAddress === msg.tokenAddress) {
      subscriptions.delete(subscription)
      break
    }
  }

  // Check if any other peers are still subscribed to this token
  let hasOtherSubscribers = false
  for (const [otherPeer, otherSubs] of peerSubscriptions) {
    if (otherPeer !== peer) {
      for (const otherSub of otherSubs) {
        if (otherSub.tokenAddress === msg.tokenAddress) {
          hasOtherSubscribers = true
          break
        }
      }
    }
    if (hasOtherSubscribers) break
  }

  // If no other subscribers, unsubscribe from Solana
  if (!hasOtherSubscribers) {
    await unsubscribeFromTokenCurve(msg.tokenAddress)
  }

  peer.send(
    JSON.stringify({
      type: 'data',
      tokenAddress: msg.tokenAddress,
      data: { status: 'unsubscribed' }
    })
  )
}
