import type { <PERSON>eer } from 'crossws'
import prisma from '~/lib/prisma'

// user id => WebSocket connection
export const connections = new Map<string, Peer>()

export default defineWebSocketHandler({
  async upgrade (request) {
    await requireUserSession(request)
  },
  async open (peer) {
    const session = await requireUserSession(peer)
    const userId = session.user?.id

    if (userId) {
      // Store connection with user ID
      connections.set(userId.toString(), peer)

      // Send unread notifications count
      const unreadCount = await getUnreadNotificationsCount(userId)
      peer.send(
        JSON.stringify({
          type: 'unread_count',
          count: unreadCount
        })
      )
    }
  },
  close (peer) {
    // Remove connection when user disconnects
    for (const [userId, connection] of connections.entries()) {
      if (connection === peer) {
        connections.delete(userId)
        break
      }
    }
  },
  async message (peer, message) {
    const session = await requireUserSession(peer)
    const userId = session.user?.id

    if (!userId) return

    try {
      const data = JSON.parse(message.toString())

      switch (data.type) {
        case 'mark_read':
          await markNotificationAsRead(data.notificationId, userId)
          break
        case 'mark_all_read':
          await markAllNotificationsAsRead(userId)
          break
        case 'get_notifications':
          {
            const notifications = await getUserNotifications(
              userId,
              data.limit,
              data.offset
            )
            peer.send(
              JSON.stringify({
                type: 'notifications',
                data: notifications
              })
            )
          }
          break
      }
    } catch (error) {
      console.error('Error handling notification message:', error)
    }
  }
})

// Get unread notifications count for a user
async function getUnreadNotificationsCount (userId: number): Promise<number> {
  try {
    const count = await prisma.notification.count({
      where: {
        targetUserId: userId,
        read: false
      }
    })
    return count
  } catch (error) {
    console.error('Error getting unread notifications count:', error)
    return 0
  }
}

// Get notifications for a user
async function getUserNotifications (userId: number, limit = 20, offset = 0) {
  try {
    const notifications = await prisma.notification.findMany({
      where: {
        targetUserId: userId
      },
      include: {
        sourceUser: {
          select: {
            id: true,
            name: true,
            address: true
          }
        },
        post: {
          select: {
            id: true,
            content: true
          }
        },
        token: {
          select: {
            id: true,
            name: true,
            symbol: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset
    })

    return notifications.map(notification => ({
      id: notification.id,
      type: notification.type.toLowerCase(),
      content: notification.content,
      createdAt: notification.createdAt.toISOString(),
      read: notification.read,
      sourceUser: notification.sourceUser
        ? {
            id: notification.sourceUser.id,
            username: notification.sourceUser.name || 'Unknown',
            displayName: notification.sourceUser.name || 'Unknown User'
          }
        : null,
      post: notification.post,
      token: notification.token
    }))
  } catch (error) {
    console.error('Error getting user notifications:', error)
    return []
  }
}

// Mark a notification as read
async function markNotificationAsRead (notificationId: number, userId: number) {
  try {
    await prisma.notification.update({
      where: {
        id: notificationId,
        targetUserId: userId // Ensure user can only mark their own notifications
      },
      data: {
        read: true
      }
    })

    // Send updated unread count
    const connection = connections.get(userId.toString())
    if (connection) {
      const unreadCount = await getUnreadNotificationsCount(userId)
      connection.send(
        JSON.stringify({
          type: 'unread_count',
          count: unreadCount
        })
      )
    }
  } catch (error) {
    console.error('Error marking notification as read:', error)
  }
}

// Mark all notifications as read for a user
async function markAllNotificationsAsRead (userId: number) {
  try {
    await prisma.notification.updateMany({
      where: {
        targetUserId: userId,
        read: false
      },
      data: {
        read: true
      }
    })

    // Send updated unread count
    const connection = connections.get(userId.toString())
    if (connection) {
      connection.send(
        JSON.stringify({
          type: 'unread_count',
          count: 0
        })
      )
    }
  } catch (error) {
    console.error('Error marking all notifications as read:', error)
  }
}

// Create and send a notification in real-time
export const sendNotification = async (notificationData: {
  sourceUserId?: number
  targetUserId: number
  type: 'COMMENT' | 'LIKE' | 'FOLLOW' | 'MENTION' | 'SHARE' | 'TOKEN'
  content: string
  postId?: number
  tokenId?: number
}) => {
  try {
    // Create notification in database
    const notification = await prisma.notification.create({
      data: notificationData,
      include: {
        sourceUser: {
          select: {
            id: true,
            name: true,
            address: true
          }
        },
        post: {
          select: {
            id: true,
            content: true
          }
        },
        token: {
          select: {
            id: true,
            name: true,
            symbol: true
          }
        }
      }
    })

    // Send real-time notification to connected user
    const connection = connections.get(notificationData.targetUserId.toString())
    if (connection) {
      connection.send(
        JSON.stringify({
          type: 'new_notification',
          data: {
            id: notification.id,
            type: notification.type.toLowerCase(),
            content: notification.content,
            createdAt: notification.createdAt.toISOString(),
            read: notification.read,
            sourceUser: notification.sourceUser
              ? {
                  id: notification.sourceUser.id,
                  username: notification.sourceUser.name || 'Unknown',
                  displayName: notification.sourceUser.name || 'Unknown User'
                }
              : null,
            post: notification.post,
            token: notification.token
          }
        })
      )

      // Also send updated unread count
      const unreadCount = await getUnreadNotificationsCount(
        notificationData.targetUserId
      )
      connection.send(
        JSON.stringify({
          type: 'unread_count',
          count: unreadCount
        })
      )
    }

    return notification
  } catch (error) {
    console.error('Error sending notification:', error)
    return null
  }
}
