import { PrismaClient } from '@prisma/client'

interface WebSocketMessage {
  type:
    | 'subscribe'
    | 'unsubscribe'
    | 'data'
    | 'error'
    | 'ping'
    | 'pong'
    | 'refresh'
  context?: string
  data?: any
  filters?: Record<string, any>
}

interface SubscriptionInfo {
  context: string
  filters?: Record<string, any>
}

interface SolanaAccountChange {
  jsonrpc: string
  method: string
  params: {
    result: {
      context: { slot: number }
      value: {
        account: {
          data: [string, string] // [base64_data, encoding]
          executable: boolean
          lamports: number
          owner: string
          rentEpoch: number
        }
        pubkey: string
      }
    }
    subscription: number
  }
}

// Store active subscriptions for each peer
const peerSubscriptions = new Map<any, Set<SubscriptionInfo>>()
let prisma: PrismaClient | null = null

// Initialize Prisma client
const getPrisma = () => {
  if (!prisma) {
    prisma = new PrismaClient()
  }
  return prisma
}

export default defineWebSocketHandler({
  open (peer) {
    console.log('[ws] Peer connected', peer.id)
    peerSubscriptions.set(peer, new Set())

    // Send welcome message
    peer.send(
      JSON.stringify({
        type: 'data',
        context: 'connection',
        data: { status: 'connected', peerId: peer.id }
      })
    )
  },

  async message (peer, message) {
    try {
      const msg: WebSocketMessage = JSON.parse(message.text())
      switch (msg.type) {
        case 'ping':
          peer.send(JSON.stringify({ type: 'pong' }))
          break

        case 'subscribe':
          await handleSubscribe(peer, msg)
          break

        case 'unsubscribe':
          handleUnsubscribe(peer, msg)
          break

        case 'refresh':
          await handleRefresh(peer, msg)
          break
      }
    } catch (error) {
      console.error('[ws] Error processing message:', error)
      peer.send(
        JSON.stringify({
          type: 'error',
          data: 'Failed to process message'
        })
      )
    }
  },

  close (peer, event) {
    peerSubscriptions.delete(peer)
  },

  error (peer, error) {
    console.log('[ws] Peer error', peer.id, error)
    peerSubscriptions.delete(peer)
  }
})

async function handleSubscribe (peer: any, msg: WebSocketMessage) {
  if (!msg.context) {
    peer.send(
      JSON.stringify({
        type: 'error',
        data: 'Context is required for subscription'
      })
    )
    return
  }

  const subscriptions = peerSubscriptions.get(peer)
  if (!subscriptions) return

  // Add subscription
  const subscription: SubscriptionInfo = {
    context: msg.context,
    filters: msg.filters
  }
  subscriptions.add(subscription)

  try {
    // Send initial data based on context
    const data = await getContextData(msg.context, msg.filters)
    peer.send(
      JSON.stringify({
        type: 'data',
        context: msg.context,
        data
      })
    )
  } catch (error) {
    console.error(`[ws] Error fetching ${msg.context} data:`, error)
    peer.send(
      JSON.stringify({
        type: 'error',
        context: msg.context,
        data: `Failed to fetch ${msg.context} data`
      })
    )
  }
}

function handleUnsubscribe (peer: any, msg: WebSocketMessage) {
  const subscriptions = peerSubscriptions.get(peer)
  if (!subscriptions || !msg.context) return

  // Remove subscription
  for (const sub of subscriptions) {
    if (sub.context === msg.context) {
      subscriptions.delete(sub)
      break
    }
  }
}

async function handleRefresh (peer: any, msg: WebSocketMessage) {
  if (!msg.context) {
    peer.send(
      JSON.stringify({
        type: 'error',
        data: 'Context is required for refresh'
      })
    )
    return
  }

  try {
    // Fetch fresh data for the requested context
    const data = await getContextData(msg.context, msg.filters)
    peer.send(
      JSON.stringify({
        type: 'data',
        context: msg.context,
        data
      })
    )
  } catch (error) {
    console.error(`[ws] Error refreshing ${msg.context} data:`, error)
    peer.send(
      JSON.stringify({
        type: 'error',
        context: msg.context,
        data: `Failed to refresh ${msg.context} data`
      })
    )
  }
}

async function getContextData (context: string, filters?: Record<string, any>) {
  const db = getPrisma()

  switch (context) {
    case 'transactions':
      return await getTransactionsData(db, filters)

    case 'holders':
      return await getHoldersData(db, filters)

    case 'marketcap':
      return await getMarketCapData(db, filters)

    case 'price':
      return await getPriceData(db, filters)

    case 'posts':
      return await getPostsData(db, filters)

    case 'tokens':
      return await getTokensData(db, filters)

    case 'solana': {
      // Solana WebSocket functionality moved to /api/ws/sol-ws
      return {
        message: 'Use /api/ws/sol-ws endpoint for Solana real-time updates',
        status: 'redirect'
      }
    }

    default:
      throw new Error(`Unknown context: ${context}`)
  }
}

async function getTransactionsData (
  db: PrismaClient,
  filters?: Record<string, any>
) {
  const where: any = {}

  if (filters?.tokenAddress) {
    where.token = { address: filters.tokenAddress }
  }
  if (filters?.userId) {
    where.userId = filters.userId
  }
  if (filters?.type) {
    where.type = filters.type
  }

  const transactions = await db.transaction.findMany({
    where,
    include: {
      user: {
        select: {
          id: true,
          name: true,
          address: true
        }
      },
      token: {
        select: {
          id: true,
          name: true,
          symbol: true,
          address: true
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    },
    take: 50 // Limit to recent 50 transactions
  })

  return transactions.map(tx => ({
    id: tx.id,
    userId: tx.userId,
    tokenId: tx.tokenId,
    amount: tx.amount,
    solAmount: tx.solAmount,
    transactionHash: tx.transactionHash,
    type: tx.type,
    isOwner: tx.isOwner,
    isBot: tx.isBot,
    createdAt: tx.createdAt.toISOString(),
    user: tx.user,
    token: tx.token
  }))
}

async function getHoldersData (db: PrismaClient, filters?: Record<string, any>) {
  // This would require a custom query to calculate holder balances
  // For now, return mock data structure
  // In a real implementation, you'd aggregate transactions to calculate balances

  if (!filters?.tokenAddress) {
    return []
  }

  // Mock implementation - in real app, you'd calculate from transactions
  const transactions = await db.transaction.findMany({
    where: {
      token: { address: filters.tokenAddress }
    },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          address: true
        }
      }
    }
  })

  // Group by user and calculate balances (simplified)
  const balances = new Map()
  transactions.forEach(tx => {
    const userId = tx.userId
    const current = balances.get(userId) || { balance: 0, user: tx.user }

    if (tx.type === 'BUY') {
      current.balance += tx.amount
    } else if (tx.type === 'SELL') {
      current.balance -= tx.amount
    }

    balances.set(userId, current)
  })

  const totalSupply = Array.from(balances.values()).reduce(
    (sum, holder) => sum + Math.max(0, holder.balance),
    0
  )

  return Array.from(balances.entries())
    .map(([userId, data]) => ({
      userId,
      tokenId: filters.tokenId || 0,
      balance: Math.max(0, data.balance),
      percentage:
        totalSupply > 0 ? (Math.max(0, data.balance) / totalSupply) * 100 : 0,
      user: data.user
    }))
    .filter(holder => holder.balance > 0)
    .sort((a, b) => b.balance - a.balance)
    .slice(0, 100) // Top 100 holders
}

async function getTokensData (db: PrismaClient, filters?: Record<string, any>) {
  const tokens = await db.token.findMany({
    where: filters?.tokenAddress ? { address: filters.tokenAddress } : {},
    select: {
      id: true,
      name: true,
      symbol: true,
      address: true,
      createdAt: true
    },
    orderBy: {
      createdAt: filters?.sortBy === 'newest' ? 'desc' : 'asc'
    }
  })

  return tokens
}

async function getMarketCapData (
  db: PrismaClient,
  filters?: Record<string, any>
) {
  // Mock market cap data - in real app, you'd fetch from on-chain or calculate
  const tokens = await db.token.findMany({
    where: filters?.tokenAddress ? { address: filters.tokenAddress } : {},
    take: 10
  })

  return tokens.map(token => ({
    tokenId: token.id,
    marketCap: '0x' + Math.floor(Math.random() * 1000000).toString(16), // Mock hex value
    percentage: Math.random() * 100,
    targetMarketCapSol: 332,
    isNearMigration: Math.random() > 0.8,
    tokenAddress: token.address
  }))
}

async function getPriceData (db: PrismaClient, filters?: Record<string, any>) {
  const where: any = {}

  if (filters?.tokenAddress) {
    where.token = { address: filters.tokenAddress }
  }

  // Get latest price logs with transactions for velocity calculation
  const prices = await db.priceLog.findMany({
    where,
    include: {
      token: {
        select: {
          id: true,
          address: true,
          name: true,
          symbol: true
        }
      }
    },
    orderBy: {
      timestamp: 'desc'
    },
    take: filters?.tokenAddress ? 10 : 20 // More historical data for velocity calculation
  })

  // For each token, calculate velocity and enhanced market data
  const enhancedPriceData = await Promise.all(
    prices.map(async price => {
      // Get recent transactions for volume calculation
      const recentTransactions = await db.transaction.findMany({
        where: {
          tokenId: price.tokenId,
          createdAt: {
            gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        take: 50
      })

      // Calculate 24h volume
      const volume24h = recentTransactions.reduce((sum, tx) => {
        return sum + (tx.solAmount || 0)
      }, 0)

      // Calculate price velocity (change over time)
      const priceHistory = prices
        .filter(p => p.tokenId === price.tokenId)
        .slice(0, 5)
      let priceVelocity = 0
      if (priceHistory.length >= 2) {
        const timeSpan =
          priceHistory[0].timestamp.getTime() -
          priceHistory[priceHistory.length - 1].timestamp.getTime()
        const priceChange =
          priceHistory[0].price - priceHistory[priceHistory.length - 1].price
        priceVelocity = timeSpan > 0 ? (priceChange / timeSpan) * 60000 : 0 // per minute
      }

      // Try to get on-chain data for market cap calculation
      let marketCapData = null
      try {
        const sidebarResponse = await $fetch(
          `/api/trading/sidebar?address=${price.token.address}`
        )
        marketCapData = sidebarResponse
      } catch (error) {
        console.log(
          `Could not fetch market cap for ${price.token.address}:`,
          error
        )
      }

      return {
        tokenId: price.tokenId,
        price: price.price,
        priceVelocity, // SOL per minute
        volume24h,
        timestamp: price.timestamp.toISOString(),
        tokenAddress: price.token.address,
        tokenSymbol: price.token.symbol,
        tokenName: price.token.name,
        marketCap: marketCapData
          ? {
              real: marketCapData.marketCap.real,
              target: marketCapData.marketCap.target,
              progress: marketCapData.marketCap.progress,
              // Include full sidebar data for frontend dynamic calculations
              sidebarData: marketCapData
            }
          : null,
        recentTransactionCount: recentTransactions.length,
        // Additional metadata for dynamic target calculation
        metadata: {
          hasRecentActivity: recentTransactions.length > 0,
          avgTransactionSize:
            recentTransactions.length > 0
              ? recentTransactions.reduce(
                  (sum, tx) => sum + (tx.solAmount || 0),
                  0
                ) / recentTransactions.length
              : 0,
          lastTransactionTime:
            recentTransactions.length > 0
              ? recentTransactions[0].createdAt.toISOString()
              : null,
          // Price change indicators
          priceChangeIndicators: {
            priceVelocityPerMinute: priceVelocity,
            transactionFrequency: recentTransactions.length / 24, // per hour
            volumeVelocity: volume24h / 24 // SOL per hour
          }
        }
      }
    })
  )

  return enhancedPriceData
}

async function getPostsData (db: PrismaClient, filters?: Record<string, any>) {
  const where: any = {}

  if (filters?.tokenSymbol) {
    where.mentions = {
      some: {
        token: {
          symbol: filters.tokenSymbol
        }
      }
    }
  }

  const posts = await db.post.findMany({
    where,
    include: {
      user: {
        select: {
          id: true,
          name: true,
          address: true
        }
      },
      mentions: {
        include: {
          token: {
            select: {
              id: true,
              name: true,
              symbol: true,
              address: true
            }
          },
          hashtag: {
            select: {
              id: true,
              name: true
            }
          },
          user: {
            select: {
              id: true,
              name: true,
              address: true
            }
          }
        }
      }
    },
    orderBy: {
      createdAt: 'desc'
    },
    take: 50
  })

  return posts.map(post => ({
    id: post.id,
    content: post.content,
    userId: post.userId,
    likeCount: post.likeCount,
    createdAt: post.createdAt.toISOString(),
    user: post.user,
    mentions: post.mentions
  }))
}

// Broadcast updates to subscribed peers
export async function broadcastToSubscribers (
  context: string,
  data: any,
  filters?: Record<string, any>
) {
  console.log(`[ws] Broadcasting ${context} update to subscribers`)

  for (const [peer, subscriptions] of peerSubscriptions) {
    for (const subscription of subscriptions) {
      if (subscription.context === context) {
        // Check if filters match (simple implementation)
        const shouldBroadcast =
          !filters ||
          !subscription.filters ||
          Object.keys(filters).every(
            key =>
              subscription.filters?.[key] === undefined ||
              subscription.filters[key] === filters[key]
          )

        if (shouldBroadcast) {
          try {
            peer.send(
              JSON.stringify({
                type: 'data',
                context,
                data
              })
            )
          } catch (error) {
            console.error('[ws] Error broadcasting to peer:', error)
          }
        }
      }
    }
  }
}
