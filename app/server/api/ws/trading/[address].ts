import { useTradingService } from '~/server/services/trading'

const tradingService = useTradingService()

export default defineWebSocketHandler({
  upgrade () {
    console.log('WebSocket upgrade initiated')
  },
  async open (peer) {
    const url = new URL(peer.websocket.url || '')
    const address = url.pathname.split('/').pop() || ''
    if (!address) {
      throw new Error('No address provided in WebSocket URL')
    }

    // await requireUserSession(peer)
    tradingService.onConnect(peer, address)
  },
  close (peer) {
    tradingService.onDisconnect(peer)
  },
  message (peer, message) {
    // ping
    if (message.text() === '{"type":"ping"}') {
      peer.send('{"type":"pong"}')
      return
    }

    tradingService.onMessage(peer, JSON.parse(message.text()))
  },
  error (peer, error) {
    console.error('WebSocket error:', error)
    // Handle error if needed
  }
})
