import prisma from '~/lib/prisma'

export default defineEventHandler(async event => {
  try {
    const tokens = await prisma.token.findMany({
      orderBy: [{ name: 'asc' }],
      take: 100 // Limit to 100 tokens
    })

    return tokens
  } catch (error) {
    console.error('Failed to fetch tokens:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch tokens'
    })
  }
})
