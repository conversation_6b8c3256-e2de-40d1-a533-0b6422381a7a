import prisma from '~/lib/prisma'

export default defineEventHandler(async event => {
  const { address } = getQuery(event)
  if (!address) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Address is required'
    })
  }

  // TODO: image and other stuff
  const token = await prisma.token.findFirst({
    where: {
      address: String(address)
    },
    select: {
      id: true,
      address: true,
      name: true,
      symbol: true
    }
  })

  if (!token) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Token not found'
    })
  }

  return token
})
