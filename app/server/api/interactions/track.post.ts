import prisma from '~/lib/prisma'

export default defineEventHandler(async event => {
  const session = await getUserSession(event)
  const userId = session.user?.id

  if (!userId) {
    // For non-authenticated users, we can still track general analytics
    // but skip database operations
    return { success: true, tracked: false }
  }

  const { type, postId, tokenId } = await readBody(event)

  if (!type) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Interaction type is required'
    })
  }

  if (!postId && !tokenId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Either postId or tokenId is required'
    })
  }

  try {
    // Check if this interaction already exists (for some types we want to avoid duplicates)
    const duplicateTypes = ['LIKE', 'BOOKMARK']
    if (duplicateTypes.includes(type)) {
      const existingInteraction = await prisma.interaction.findFirst({
        where: {
          userId,
          type,
          postId: postId || undefined,
          tokenId: tokenId || undefined
        }
      })

      if (existingInteraction) {
        return { success: true, tracked: false, reason: 'duplicate' }
      }
    }

    // Create the interaction
    await prisma.interaction.create({
      data: {
        userId,
        type,
        postId: postId || undefined,
        tokenId: tokenId || undefined
      }
    })

    return { success: true, tracked: true }
  } catch (error) {
    console.error('Failed to track interaction:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to track interaction'
    })
  }
})
