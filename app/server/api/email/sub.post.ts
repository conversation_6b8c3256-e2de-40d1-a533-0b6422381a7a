import prisma from '~/lib/prisma'

export default defineEventHandler(async event => {
  const body = await readBody(event)
  const { email } = body

  if (!email) {
    throw createError({ statusCode: 400, statusMessage: 'Email is required' })
  }

  // verify email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  if (!emailRegex.test(email)) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid email format'
    })
  }

  const subscription = await prisma.emailSubscription.create({
    data: {
      email,
      type: 'LAUNCH'
    }
  })
  if (!subscription) {
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create subscription'
    })
  }

  return {
    success: true,
    message: 'Subscription created successfully',
    data: subscription
  }
})
