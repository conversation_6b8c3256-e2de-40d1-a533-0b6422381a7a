export default defineEventHandler(async event => {
  const session = await requireUserSession(event)

  try {
    const query = getQuery(event)
    const { address } = query

    if (!address) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Token address is required'
      })
    }

    // Get user address from session
    const userAddress =
      (session.user as any)?.address ||
      (session as any).address ||
      (session as any).user?.address

    if (!userAddress) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User address not found in session'
      })
    }

    // Dynamic imports to avoid bundling jayson
    const { PublicKey } = await import('@solana/web3.js')
    const { getAssociatedTokenAddress } = await import('@solana/spl-token')
    const { initializeProgram } = await import('~/server/services/web3')

    // Use Anchor program connection instead of direct Solana connection
    const program = await initializeProgram()
    const connection = program.provider.connection

    const userPubkey = new PublicKey(userAddress)
    const tokenMint = new PublicKey(address as string)

    try {
      // Get user's associated token account
      const userTokenAccount = await getAssociatedTokenAddress(
        tokenMint,
        userPubkey
      )

      // Get token balance
      const balance = await connection.getTokenAccountBalance(userTokenAccount)
      const tokenBalance = balance.value.uiAmount || 0

      return {
        tokenAddress: address,
        userAddress,
        balance: tokenBalance,
        balanceRaw: balance.value.amount
      }
    } catch (error) {
      // User has no token account or balance
      return {
        tokenAddress: address,
        userAddress,
        balance: 0,
        balanceRaw: '0'
      }
    }
  } catch (error) {
    console.error('Error fetching user token balance:', error)
    if (error instanceof Error && (error as any).statusCode) {
      throw error
    }
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
