import prisma from '~/lib/prisma'

export default defineEventHandler(async event => {
  const address = event.context.params?.address as string

  if (!address) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Address is required'
    })
  }

  const token = await prisma.token.findUnique({
    where: {
      address
    }
  })

  if (!token) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Token not found'
    })
  }

  return token
})
