import { createToken } from '~/server/services/web3'
import {
  getUserByAddress,
  incrementTokenCount
} from '~/server/services/database'
import { broadcastToSubscribers } from '~/server/api/ws/index'
import prisma from '~/lib/prisma'
import { PublicKey } from '@solana/web3.js'
import {
  generateFileName,
  validateImageFile,
  deleteStorageFile
} from '~/server/utils/storage'

export default defineEventHandler(async event => {
  const session = await requireUserSession(event)

  // Initialize storage
  const storage = useStorage('uploads')
  let imageUrl = ''

  try {
    console.log('Session object:', JSON.stringify(session, null, 2))

    // Read multipart form data
    const body = await readMultipartFormData(event)
    if (!body) {
      throw createError({
        statusCode: 400,
        statusMessage: 'No form data provided'
      })
    }

    const formFields: { [key: string]: string | File } = {}
    const imageFiles: { [key: string]: File } = {}

    // Parse form data
    for (const part of body) {
      if (part.name) {
        if (part.filename && part.data) {
          // This is a file
          const file = new File([Buffer.from(part.data)], part.filename, {
            type: part.type || 'application/octet-stream'
          })
          formFields[part.name] = file
          imageFiles[part.name] = file
        } else if (part.data) {
          // This is a text field
          formFields[part.name] = part.data.toString()
        }
      }
    }

    const name = formFields.name as string
    const symbol = formFields.symbol as string
    const description = formFields.description as string
    const twitter = formFields.twitter as string
    const telegram = formFields.telegram as string
    const website = formFields.website as string
    const instagram = formFields.instagram as string
    const discord = formFields.discord as string

    // Validate input
    if (!name || !symbol) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Token name and symbol are required'
      })
    }

    // Type assertion to access the address property
    const userAddress =
      (session.user as any)?.address ||
      (session as any).address ||
      (session as any).user?.address

    if (!userAddress) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User address not found in session'
      })
    }

    console.log('Using address:', userAddress)

    // Get user from database with onChainId and state
    const user = await getUserByAddress(userAddress)

    if (!user || !user.onChainState) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User does not have on-chain state. Please login again.'
      })
    }

    // Handle image upload if provided
    if (imageFiles.image) {
      const file = imageFiles.image

      // Validate file using utility
      validateImageFile(file, 5 * 1024 * 1024) // 5MB max

      const fileExtension = file.name.split('.').pop() || 'jpg'
      const fileName = generateFileName(user.id, 'token', fileExtension)

      const buffer = Buffer.from(await file.arrayBuffer())
      await storage.setItemRaw(fileName, buffer)
      imageUrl = `/uploads/${fileName}`
    }

    // Create token on-chain using user input data
    const [mintPda, newTokenCount] = await createToken(
      {
        name: name.trim(),
        symbol: symbol.trim().toUpperCase(),
        description: description?.trim() || `${name} token`,
        image: imageUrl // Use uploaded image URL
      },
      user.onChainId,
      user.onChainState.tokenCount
    )

    // Save token to database
    const savedToken = await prisma.token.create({
      data: {
        name: name.trim(),
        symbol: symbol.trim().toUpperCase(),
        description: description?.trim() || `${name.trim()} token`,
        image: imageUrl || null,
        twitter: twitter?.trim() || null,
        telegram: telegram?.trim() || null,
        website: website?.trim() || null,
        instagram: instagram?.trim() || null,
        discord: discord?.trim() || null,
        address: mintPda.toString(),
        userId: user.id
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            address: true
          }
        }
      }
    })

    // Update token count in database
    await incrementTokenCount(user.onChainId)

    // NOTE: Real-time curve monitoring is now handled by the separate
    // Solana WebSocket endpoint (/api/ws/sol-ws) with on-demand subscriptions
    // Users will subscribe to specific tokens from the frontend when viewing token pages

    // Broadcast the new token to all WebSocket subscribers
    const tokenForBroadcast = {
      id: savedToken.id,
      name: savedToken.name,
      symbol: savedToken.symbol,
      address: savedToken.address,
      userId: savedToken.userId,
      createdAt: savedToken.createdAt.toISOString()
    }

    // Broadcast to tokens context subscribers
    await broadcastToSubscribers('tokens', tokenForBroadcast)

    // Send notifications to followers about new token creation
    const followers = await prisma.follow.findMany({
      where: {
        followedId: user.id
      },
      select: {
        followerId: true
      }
    })

    if (followers.length > 0) {
      const { sendNotification } = await import('~/server/api/ws/notifications')

      for (const follower of followers) {
        await sendNotification({
          sourceUserId: user.id,
          targetUserId: follower.followerId,
          type: 'TOKEN',
          content: `created a new token: ${savedToken.symbol}`,
          tokenId: savedToken.id
        })
      }
    }

    return {
      success: true,
      token: {
        id: savedToken.id,
        name: savedToken.name,
        symbol: savedToken.symbol,
        mintAddress: mintPda,
        tokenCount: newTokenCount,
        creator: {
          id: user.id,
          name: user.name,
          address: user.address
        },
        onChainId: user.onChainId,
        createdAt: savedToken.createdAt.toISOString()
      }
    }
  } catch (error: any) {
    console.error('Error creating token:', error)

    // Cleanup uploaded image if token creation failed
    if (imageUrl) {
      try {
        await deleteStorageFile(imageUrl)
      } catch (cleanupError) {
        console.error('Failed to cleanup uploaded image:', cleanupError)
      }
    }

    throw createError({
      statusCode: 500,
      message: 'Failed to create token',
      data: error.message
    })
  }
})
