import prisma from '~/lib/prisma'
import type { User } from '~/types/database'
import { useTradingService } from '~/server/services/trading'

export default defineEventHandler(async event => {
  const address = event.context.params?.address as string

  if (!address) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Address is required'
    })
  }

  const token = await prisma.token.findUnique({
    where: {
      address
    },
    include: {
      transactions: {
        include: {
          user: {
            select: {
              id: true,
              address: true,
              name: true,
              displayName: true,
              profilePicture: true
            }
          }
        }
      }
    }
  })

  console.log('Token found:', {
    name: token?.name,
    address: token?.address,
    curveAddress: token?.curveAddress,
    transactionCount: token?.transactions?.length
  })

  if (!token) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Token not found'
    })
  }

  // reduce holders from the transactions
  const holders = token.transactions.reduce(
    (acc, transaction) => {
      const { user, tokenAmount, userId, type } = transaction
      if (!tokenAmount) return acc

      // Handle both registered and non-registered users
      const userKey = user?.address || `unregistered_${userId || 'unknown'}`
      // Convert from smallest unit to actual token amount (divide by 10^9 for Solana tokens)
      const amount = Number(tokenAmount) / 1e9

      if (!acc[userKey]) {
        acc[userKey] = {
          user: user || null, // null for non-registered users
          totalAmount: 0
        }
      }

      // Add for BUY transactions, subtract for SELL transactions
      if (type === 'BUY') {
        acc[userKey].totalAmount += amount
      } else if (type === 'SELL') {
        acc[userKey].totalAmount -= amount
      }

      return acc
    },
    {} as Record<string, { user: Partial<User> | null; totalAmount: number }>
  )

  // Total supply is always 1B tokens (1,000,000,000)
  const TOTAL_SUPPLY = 1_000_000_000

  // Calculate user holdings (sum of all positive holdings)
  const userHoldings = Object.values(holders).reduce((sum, holder) => {
    return sum + Math.max(0, holder.totalAmount) // Only count positive holdings
  }, 0)

  // Fetch curve data to get remaining token reserves
  let curveTokenReserves = 0
  const tradingService = useTradingService()
  let curveData = null
  console.log('Token curve address:', token.curveAddress)

  try {
    curveData = await tradingService.getTokenCurveAccount(address)
    console.log('Curve data:', curveData)

    if (curveData) {
      // Convert token reserves from raw units to readable format (divide by 10^9)
      curveTokenReserves = Number(curveData.tokenReserves) / 1e9
      console.log('Curve token reserves:', curveTokenReserves)
    }
  } catch (error) {
    console.error('Error fetching curve data:', error)
  }

  // Add curve account to holders if it exists
  const holdersArray = Object.entries(holders)
    .filter(([_, holder]) => holder.totalAmount > 0) // Only include holders with positive balances
    .map(([userKey, holder]) => {
      const percentage =
        TOTAL_SUPPLY > 0 ? (holder.totalAmount / TOTAL_SUPPLY) * 100 : 0

      return {
        id: userKey,
        user: holder.user
          ? {
              name: holder.user.name || 'Unknown',
              address: userKey.startsWith('unregistered_')
                ? 'Unregistered User'
                : holder.user.address || userKey,
              displayName: holder.user.displayName,
              profilePicture: holder.user.profilePicture
            }
          : {
              name: 'Unknown User',
              address: userKey.startsWith('unregistered_')
                ? 'Unregistered User'
                : userKey
            },
        balance: holder.totalAmount.toString(),
        percentage: `${percentage.toFixed(2)}%`,
        value: '0 SOL', // Placeholder value calculation
        totalAmount: holder.totalAmount
      }
    })

  const curvePercentage =
    TOTAL_SUPPLY > 0 ? (curveTokenReserves / TOTAL_SUPPLY) * 100 : 0

  holdersArray.push({
    id: `curve_${curveData?.curveAddress}`,
    user: {
      name: 'Bonding Curve',
      address: curveData?.curveAddress || 'None',
      displayName: 'Bonding Curve Reserves',
      profilePicture: null
    },
    balance: curveTokenReserves.toString(),
    percentage: `${curvePercentage.toFixed(2)}%`,
    value: '0 SOL',
    totalAmount: curveTokenReserves
  })

  // Sort by holding percentage (descending)
  holdersArray.sort((a, b) => b.totalAmount - a.totalAmount)

  return {
    holders: holdersArray,
    totalSupply: TOTAL_SUPPLY,
    curveAddress: token.curveAddress
  }
})
