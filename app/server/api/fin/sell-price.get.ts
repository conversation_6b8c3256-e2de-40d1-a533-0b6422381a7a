import prisma from '~/lib/prisma'
import { initializeProgram } from '~/server/services/web3'
import { PublicKey } from '@solana/web3.js'
import { BN } from 'bn.js'

export default defineEventHandler(async event => {
  try {
    const query = getQuery(event)
    const { address, tokenAmount } = query

    if (!address || !tokenAmount) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Token address and token amount are required'
      })
    }

    const amount = parseFloat(tokenAmount as string)
    if (amount <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Token amount must be greater than 0'
      })
    }

    // Fetch token from database
    const token = await prisma.token.findUnique({
      where: { address: address as string }
    })

    if (!token) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Token not found'
      })
    }

    // Initialize program and get actual curve data
    const program = await initializeProgram()
    const mintPubkey = new PublicKey(address as string)

    // Get the curve account PDA
    const [tokenCurveAccount] = PublicKey.findProgramAddressSync(
      [Buffer.from('curve'), mintPubkey.toBuffer()],
      program.programId
    )

    try {
      // Fetch current curve state from blockchain
      const curveState =
        await program.account.tokenCurveAccount.fetch(tokenCurveAccount)

      // Convert amount to lamports (9 decimals)
      //   const tokenAmountLamports = new BN(amount).mul(new BN(10).pow(new BN(9)))

      // Get current reserves from curve state
      const solReserves = new BN(curveState.solReserves.toString())
      const tokenReserves = new BN(curveState.tokenReserves.toString())

      // Convert amount to lamports (9 decimals)
      const tokenAmountLamports = new BN(amount).mul(new BN(10).pow(new BN(9)))

      // Constant product formula: k = x * y
      // After selling: (x - sol_out) * (y + tokens_in) = k
      // Solving for sol_out: sol_out = x - k / (y + tokens_in)

      const k = solReserves.mul(tokenReserves)
      const newTokenReserves = tokenReserves.add(tokenAmountLamports)
      const newSolReserves = k.div(newTokenReserves)
      const solAmount = solReserves.sub(newSolReserves)

      // Apply trading fee (0.6% like pump.fun)
      const fee = solAmount.mul(new BN(6)).div(new BN(1000)) // 0.6%
      const finalSellAmount = solAmount.sub(fee)

      // Convert back to SOL (from lamports)
      const estimatedSolReceived = finalSellAmount.toNumber() / 1e9
      const minSolAmount = estimatedSolReceived * 0.98 // 2% slippage tolerance

      // Calculate current price based on constant product (using BN arithmetic to avoid overflow)
      const priceNumerator = solReserves.mul(new BN('1000000000')) // Scale up for precision
      const currentPriceBN = priceNumerator.div(tokenReserves)
      const currentPrice = currentPriceBN.toNumber() / 1e9

      return {
        tokenAmount: amount,
        estimatedSolReceived,
        minSolAmount,
        currentPrice,
        slippageTolerance: 2, // 2% slippage tolerance
        tokenReserves: curveState.tokenReserves.toString(),
        solReserves: curveState.solReserves.toString()
      }
    } catch (fetchError) {
      console.warn(
        'Could not fetch live curve data, using fallback calculation:',
        fetchError
      )

      // Fallback to simple calculation if curve fetch fails
      const basePrice = 0.001 // Base price per token in SOL
      const estimatedSolReceived = amount * basePrice
      const minSolAmount = estimatedSolReceived * 0.98 // 2% slippage tolerance

      return {
        tokenAmount: amount,
        estimatedSolReceived,
        minSolAmount,
        currentPrice: basePrice,
        slippageTolerance: 2 // 2% slippage tolerance
      }
    }
  } catch (error) {
    console.error('Error in sell price estimation:', error)
    if (error instanceof Error && (error as any).statusCode) {
      throw error
    }
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
