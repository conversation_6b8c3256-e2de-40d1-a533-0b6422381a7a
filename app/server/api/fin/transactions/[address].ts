import prisma from '~/lib/prisma'

export default defineEventHandler(async event => {
  const address = event.context.params?.address as string

  if (!address) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Address is required'
    })
  }

  const token = await prisma.token.findUnique({
    where: {
      address
    },
    include: {
      transactions: {
        orderBy: {
          createdAt: 'desc'
        },
        include: {
          user: {
            select: {
              id: true,
              address: true,
              name: true,
              displayName: true,
              profilePicture: true
            }
          }
        }
      }
    }
  })

  if (!token) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Token not found'
    })
  }

  return token.transactions.map(transaction => ({
    ...transaction,
    amountLamports: transaction.amountLamports?.toString(),
    tokenAmount: transaction.tokenAmount?.toString(),
    solResAfter: transaction.solResAfter?.toString(),
    solResBefore: transaction.solResBefore?.toString()
  }))
})
