import { <PERSON>Key } from '@solana/web3.js'
import prisma from '~/lib/prisma'
import { getUserByAddress } from '~/server/services/database'
import { initializeProgram } from '~/server/services/web3'
import { broadcastToSubscribers } from '~/server/api/ws'
import { updateTargetMarketCapInternal } from '~/server/utils/updateTargetMarketCap'

export default defineEventHandler(async event => {
  const session = await requireUserSession(event)
  try {
    const { transactionSignature, tokenAddress, tokenAmount, solAmount } =
      await readBody(event)

    // Validate input
    if (!transactionSignature || !tokenAddress || !tokenAmount || !solAmount) {
      throw createError({
        statusCode: 400,
        statusMessage:
          'Transaction signature, token address, token amount, and SOL amount are required'
      })
    }

    // Get user address from session
    const userAddress =
      (session.user as any)?.address ||
      (session as any).address ||
      (session as any).user?.address

    if (!userAddress) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User address not found in session'
      })
    }

    // Get user from database
    const user = await getUserByAddress(userAddress)
    if (!user) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found'
      })
    }

    // Get token from database
    const token = await prisma.token.findUnique({
      where: { address: tokenAddress }
    })

    if (!token) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Token not found'
      })
    }

    // Initialize program to verify transaction
    const program = await initializeProgram()

    try {
      // Verify the transaction exists and was successful
      const transaction = await program.provider.connection.getTransaction(
        transactionSignature,
        {
          commitment: 'confirmed'
        }
      )

      if (!transaction) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Transaction not found or not confirmed'
        })
      }

      if (transaction.meta?.err) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Transaction failed'
        })
      }

      // Check if this transaction has already been recorded
      const existingTransaction = await prisma.transaction.findUnique({
        where: { transactionHash: transactionSignature }
      })

      if (existingTransaction) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Transaction already recorded'
        })
      }

      // Record the transaction in the database
      const dbTransaction = await prisma.transaction.create({
        data: {
          userId: user.id,
          tokenId: token.id,
          tokenAmount: Math.floor(tokenAmount * 1e9),
          amountLamports: Math.floor(solAmount * 1e9), // Convert SOL to lamports
          transactionHash: transactionSignature,
          type: 'BUY',
          isOwner: false,
          isBot: false
        }
      })

      // Create price log for velocity tracking
      const currentPrice = parseFloat(solAmount) / parseFloat(tokenAmount) // SOL per token
      await prisma.priceLog.create({
        data: {
          tokenId: token.id,
          price: currentPrice,
          volume: parseFloat(solAmount),
          timestamp: new Date()
        }
      })

      console.log('Recorded buy transaction:', dbTransaction)

      // NOTE: Real-time curve monitoring is now handled by the separate
      // Solana WebSocket endpoint (/api/ws/sol-ws) with on-demand subscriptions
      // Users will subscribe to specific tokens from the frontend when viewing token pages

      // Broadcast the new transaction to WebSocket subscribers
      try {
        await broadcastToSubscribers('transactions', dbTransaction)

        // Also broadcast updated price data including sidebar information
        const updatedToken = await prisma.token.findUnique({
          where: { id: token.id }
        })

        if (updatedToken) {
          await broadcastToSubscribers('tokens', [updatedToken])
        }

        // Broadcast updated price data with sidebar information for real-time updates
        const sidebarResponse = await $fetch(
          `/api/trading/sidebar?address=${token.address}`
        ).catch(() => null)

        const priceUpdate = {
          tokenId: token.id,
          price: currentPrice,
          priceVelocity: 0, // Will be calculated by velocity tracking
          volume24h: parseFloat(solAmount),
          timestamp: new Date().toISOString(),
          tokenAddress: token.address,
          tokenSymbol: token.symbol,
          tokenName: token.name,
          token: {
            id: token.id,
            address: token.address,
            name: token.name,
            symbol: token.symbol
          },
          // Match the WebSocket data structure exactly
          marketCap: sidebarResponse
            ? {
                real: sidebarResponse.marketCap.real,
                target: sidebarResponse.marketCap.target,
                progress: sidebarResponse.marketCap.progress,
                // Include full sidebar data for frontend dynamic calculations
                sidebarData: sidebarResponse
              }
            : null
        }

        await broadcastToSubscribers('price', priceUpdate)
      } catch (broadcastError) {
        console.error('Error broadcasting buy transaction:', broadcastError)
        // Don't fail the transaction confirmation for broadcast errors
      }

      return {
        success: true,
        transactionId: dbTransaction.id,
        tokenAmount,
        solAmount
      }
    } catch (error: any) {
      if (error.statusCode) {
        throw error
      }

      console.error('Error verifying transaction:', error)
      throw createError({
        statusCode: 500,
        statusMessage: 'Failed to verify transaction'
      })
    }
  } catch (error) {
    console.error('Error in buy confirm handler:', error)
    if (error instanceof Error && (error as any).statusCode) {
      throw error
    }
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
