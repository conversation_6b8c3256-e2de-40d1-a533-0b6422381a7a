import { BN } from 'bn.js'
import { <PERSON>Key, Transaction, SystemProgram } from '@solana/web3.js'
import {
  getAssociatedTokenAddress,
  TOKEN_PROGRAM_ID,
  ASSOCIATED_TOKEN_PROGRAM_ID
} from '@solana/spl-token'
import prisma from '~/lib/prisma'
import { getUserByAddress } from '~/server/services/database'
import { initializeProgram } from '~/server/services/web3'

export default defineEventHandler(async event => {
  const session = await requireUserSession(event)
  try {
    const { address, amount } = await readBody(event)

    // Validate input
    if (!address) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Token address is required'
      })
    }

    if (!amount || amount <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Valid token amount is required'
      })
    }

    // Get user address from session
    const userAddress =
      (session.user as any)?.address ||
      (session as any).address ||
      (session as any).user?.address

    if (!userAddress) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User address not found in session'
      })
    }

    console.log(
      'User selling with address:',
      userAddress,
      'Token amount:',
      amount
    )

    // Get user from database with onChainId and state
    const user = await getUserByAddress(userAddress)

    if (!user || !user.onChainState) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User does not have on-chain state. Please login again.'
      })
    }

    // Fetch token from database
    const token = await prisma.token.findUnique({
      where: { address }
    })

    if (!token) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Token not found'
      })
    }

    // Initialize the Anchor program
    const program = await initializeProgram()

    // Convert addresses to PublicKey objects
    const sellerPubkey = new PublicKey(userAddress)
    const mintPubkey = new PublicKey(address)

    // Get the curve account PDA
    const [tokenCurveAccount] = PublicKey.findProgramAddressSync(
      [Buffer.from('curve'), mintPubkey.toBuffer()],
      program.programId
    )

    // Get seller's associated token account
    const sellerTokenAccount = await getAssociatedTokenAddress(
      mintPubkey,
      sellerPubkey
    )

    // Get curve's associated token account
    const curveTokenAccount = await getAssociatedTokenAddress(
      mintPubkey,
      tokenCurveAccount,
      true // allowOwnerOffCurve
    )

    // Check if user has enough tokens
    try {
      const tokenAccountInfo =
        await program.provider.connection.getTokenAccountBalance(
          sellerTokenAccount
        )
      const userTokenBalance = new BN(tokenAccountInfo.value.amount)
      const sellAmountLamports = new BN(amount).mul(new BN(10).pow(new BN(9))) // Convert to smallest units

      if (userTokenBalance.lt(sellAmountLamports)) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Insufficient token balance'
        })
      }

      console.log('User token balance:', userTokenBalance.toString())
      console.log('Sell amount (lamports):', sellAmountLamports.toString())
    } catch (error: any) {
      if (error.statusCode) throw error
      throw createError({
        statusCode: 400,
        statusMessage: 'Error checking token balance or user has no tokens'
      })
    }

    // Convert token amount to lamports (considering 9 decimals)
    const tokenAmountLamports = new BN(amount).mul(new BN(10).pow(new BN(9)))

    // Fetch current pricing from the curve for accurate sell price
    const priceData = await $fetch(
      `/api/fin/sell-price?address=${address}&tokenAmount=${amount}`
    )

    const estimatedSolAmount = new BN(priceData.estimatedSolReceived * 1e9) // Convert to lamports
    const minSolAmount = new BN(priceData.minSolAmount * 1e9) // Convert to lamports

    console.log('Transaction details:', {
      tokenAmount: amount,
      tokenAmountLamports: tokenAmountLamports.toString(),
      estimatedSolAmount: estimatedSolAmount.toString(),
      minSolAmount: minSolAmount.toString(),
      currentPrice: priceData.currentPrice,
      slippage: '2%'
    })

    // Build the transaction
    const transaction = new Transaction()

    // Add the sell_token instruction
    const sellInstruction = await program.methods
      .sellToken(tokenAmountLamports, minSolAmount)
      .accounts({
        seller: sellerPubkey,
        mint: mintPubkey
      })
      .instruction()

    transaction.add(sellInstruction)

    // Get recent blockhash
    const { blockhash } = await program.provider.connection.getLatestBlockhash()
    transaction.recentBlockhash = blockhash
    transaction.feePayer = sellerPubkey

    // Serialize the transaction
    const serializedTransaction = transaction.serialize({
      requireAllSignatures: false,
      verifySignatures: false
    })

    // Calculate estimated values for response
    const estimatedSolReceived = estimatedSolAmount.toNumber() / 1e9 // Convert back to SOL

    // Return the transaction for frontend signing
    return {
      transaction: Array.from(serializedTransaction),
      tokenAmount: tokenAmountLamports.toString(),
      solAmount: estimatedSolReceived,
      price: priceData.currentPrice.toString(),
      owned: Math.max(0, user.onChainState.tokenCount - amount) // Estimated new balance
    }
  } catch (error) {
    console.error('Error in sell.post handler:', error)
    throw createError({
      statusCode: 500,
      statusMessage:
        error instanceof Error ? error.message : 'Internal server error'
    })
  }
})
