import { BN } from 'bn.js'
import {
  PublicKey,
  Transaction,
  SystemProgram,
  TransactionInstruction
} from '@solana/web3.js'
import {
  getAssociatedTokenAddress,
  TOKEN_PROGRAM_ID,
  ASSOCIATED_TOKEN_PROGRAM_ID
} from '@solana/spl-token'
import prisma from '~/lib/prisma'
import { getUserByAddress } from '~/server/services/database'
import { initializeProgram } from '~/server/services/web3'

export default defineEventHandler(async event => {
  const session = await requireUserSession(event)
  try {
    const { address, solAmount, tokensAmount } = await readBody(event)

    if (!tokensAmount || tokensAmount <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Token amount must be greater than 0'
      })
    }

    // Validate input
    if (!address) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Token address is required'
      })
    }

    if (!solAmount || solAmount <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Valid SOL amount is required'
      })
    }

    // Type assertion to access the address property
    const userAddress =
      (session.user as any)?.address ||
      (session as any).address ||
      (session as any).user?.address

    if (!userAddress) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User address not found in session'
      })
    }

    console.log(
      'User buying with address:',
      userAddress,
      'SOL amount:',
      solAmount
    )

    // Get user from database with onChainId and state
    const user = await getUserByAddress(userAddress)

    if (!user || !user.onChainState) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User does not have on-chain state. Please login again.'
      })
    }

    // Fetch token from database
    const token = await prisma.token.findUnique({
      where: { address }
    })

    if (!token) {
      throw createError({
        statusCode: 404,
        statusMessage: 'Token not found'
      })
    }

    // Initialize the Anchor program
    const program = await initializeProgram()

    // Convert addresses to PublicKey objects
    const buyerPubkey = new PublicKey(userAddress)
    const mintPubkey = new PublicKey(address)

    // Get the curve account PDA
    const [tokenCurveAccount] = PublicKey.findProgramAddressSync(
      [Buffer.from('curve'), mintPubkey.toBuffer()],
      program.programId
    )

    // Get buyer's associated token account
    const buyerTokenAccount = await getAssociatedTokenAddress(
      mintPubkey,
      buyerPubkey
    )

    // Get curve's associated token account
    const curveTokenAccount = await getAssociatedTokenAddress(
      mintPubkey,
      tokenCurveAccount,
      true // allowOwnerOffCurve
    )

    // Convert SOL amount to lamports and calculate estimated token amount
    const solAmountLamports = new BN(solAmount * 1e9) // Convert SOL to lamports

    // Fetch current pricing from the curve
    // const priceData = await $fetch(
    //   `/api/fin/price?address=${address}&solAmount=${solAmount}`
    // )

    // const estimatedTokenAmount = new BN(priceData.estimatedTokens)

    // Use 2% slippage tolerance
    // const maxSolCost = new BN(priceData.maxSolCost)

    // console.log('Transaction details:', {
    //   solAmount,
    //   solAmountLamports: solAmountLamports.toString(),
    //   estimatedTokenAmount: estimatedTokenAmount.toString(),
    //   maxSolCost: maxSolCost.toString(),
    //   currentPrice: priceData.currentPrice,
    //   slippage: '2%',
    // })

    // Build the transaction
    const transaction = new Transaction()

    // Add a memo instruction to make each transaction unique
    const timestamp = Date.now()
    const randomSuffix = Math.floor(Math.random() * 10000)
    const memo = `buy-${address.slice(0, 8)}-${timestamp}-${randomSuffix}`

    // memo
    const memoInstruction = new TransactionInstruction({
      keys: [],
      programId: new PublicKey('MemoSq4gqABAXKb96qnH8TysNcWxMyWCqXgDLGmfcHr'), // Memo program
      data: Buffer.from(memo, 'utf8')
    })

    // Add the buy_token instruction
    const buyInstruction = await program.methods
      .buyToken(new BN(tokensAmount), new BN(0))
      .accounts({
        buyer: buyerPubkey,
        mint: mintPubkey
      })
      .instruction()

    transaction.add(buyInstruction)
    transaction.add(memoInstruction)

    // Get recent blockhash
    const { blockhash } = await program.provider.connection.getLatestBlockhash()
    transaction.recentBlockhash = blockhash
    transaction.feePayer = buyerPubkey

    // Serialize the transaction
    const serializedTransaction = transaction.serialize({
      requireAllSignatures: false,
      verifySignatures: false
    })

    // Calculate current price for tracking
    let currentPrice = 0
    try {
      const curveData =
        await program.account.tokenCurveAccount.fetch(tokenCurveAccount)
      const solReserves = new BN(curveData.solReserves.toString())
      const tokenReserves = new BN(curveData.tokenReserves.toString())
      const priceNumerator = solReserves.mul(new BN('**********'))
      const currentPricePerTokenBN = priceNumerator.div(tokenReserves)
      currentPrice = currentPricePerTokenBN.toNumber() / 1e9
    } catch (priceError) {
      console.log('Could not fetch current price, using default')
      currentPrice = 0.********
    }

    // Return the transaction for frontend signing
    return {
      transaction: Array.from(serializedTransaction),
      // tokenAmount: estimatedTokenAmount.toString(),
      solCost: solAmountLamports.toString(),
      // maxSolCost: maxSolCost.toString(),
      owned: user.onChainState.tokenCount,
      price: currentPrice.toString()
    }
  } catch (error) {
    console.error('Error in buy.post handler:', error)
    throw createError({
      statusCode: 500,
      statusMessage:
        error instanceof Error ? error.message : 'Internal server error'
    })
  }
})
