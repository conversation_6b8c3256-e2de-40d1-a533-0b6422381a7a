import { BN } from 'bn.js'
import { PublicKey } from '@solana/web3.js'
import { initializeProgram } from '~/server/services/web3'

export default defineEventHandler(async event => {
  try {
    const query = getQuery(event)
    const { address, solAmount } = query

    if (!address || !solAmount) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Token address and SOL amount are required'
      })
    }

    const solAmountNum = parseFloat(solAmount as string)
    if (solAmountNum <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid SOL amount'
      })
    }

    // Initialize the Anchor program
    const program = await initializeProgram()

    // Convert addresses to PublicKey objects
    const mintPubkey = new PublicKey(address as string)

    // Get the curve account PDA
    const [tokenCurveAccount] = PublicKey.findProgramAddressSync(
      [Buffer.from('curve'), mintPubkey.toBuffer()],
      program.programId
    )

    try {
      // Fetch the curve account data
      const curveData =
        await program.account.tokenCurveAccount.fetch(tokenCurveAccount)

      // Get current reserves from curve state
      const solReserves = new BN(curveData.solReserves.toString())
      const tokenReserves = new BN(curveData.tokenReserves.toString())

      // Convert SOL amount to lamports
      const solAmountLamports = new BN(Math.floor(solAmountNum * 1e9))

      // Constant product formula: k = x * y
      // After buying: (x + sol_in) * (y - tokens_out) = k
      // Solving for tokens_out: tokens_out = y - k / (x + sol_in)

      const k = solReserves.mul(tokenReserves)

      // Apply trading fee (0.6% like pump.fun) to the SOL input
      const feeAdjustedSolInput = solAmountLamports
        .mul(new BN(994))
        .div(new BN(1000)) // Remove 0.6% fee
      const newSolReserves = solReserves.add(feeAdjustedSolInput)
      const newTokenReserves = k.div(newSolReserves)
      const tokensOut = tokenReserves.sub(newTokenReserves)

      // Calculate current price based on constant product (SOL per token)
      // Use BN arithmetic to avoid precision loss
      const priceNumerator = solReserves.mul(new BN('**********')) // Scale up for precision
      const currentPricePerTokenBN = priceNumerator.div(tokenReserves)
      const currentPricePerToken = currentPricePerTokenBN.toNumber() / 1e9

      // Apply 2% slippage for max cost
      const maxSolCost = solAmountLamports.mul(new BN(102)).div(new BN(100))

      return {
        success: true,
        estimatedTokens: tokensOut.toString(),
        estimatedTokensFormatted: tokensOut
          .div(new BN('**********'))
          .toString(),
        currentPrice: currentPricePerToken * 1e9, // Current price in lamports per token
        pricePerToken: currentPricePerToken, // Price in SOL
        solAmount: solAmountLamports.toString(),
        maxSolCost: maxSolCost.toString(),
        slippage: 2,
        tokenReserves: curveData.tokenReserves.toString(),
        solReserves: curveData.solReserves.toString(),
        actualLastPrice: curveData.lastPrice,
        actualLastPriceLamports: curveData.lastPrice * 1e9
      }
    } catch (fetchError) {
      // If curve account doesn't exist, use default values
      console.log('Curve account not found, using default pricing')

      // Default initial values for new tokens
      const defaultSolReserves = new BN('***********') // 59 SOL initial reserves
      const defaultTokenReserves = new BN('**********').mul(
        new BN('**********')
      ) // 1B tokens (100% of supply)

      const solAmountLamports = new BN(Math.floor(solAmountNum * 1e9))

      // Apply fee to input
      const feeAdjustedSolInput = solAmountLamports
        .mul(new BN(994))
        .div(new BN(1000))

      // Constant product calculation with defaults
      const k = defaultSolReserves.mul(defaultTokenReserves)
      const newSolReserves = defaultSolReserves.add(feeAdjustedSolInput)
      const newTokenReserves = k.div(newSolReserves)
      const tokensOut = defaultTokenReserves.sub(newTokenReserves)

      // Calculate current price with BN arithmetic
      const priceNumerator = defaultSolReserves.mul(new BN('**********')) // Scale up for precision
      const currentPricePerTokenBN = priceNumerator.div(defaultTokenReserves)
      const currentPricePerToken = currentPricePerTokenBN.toNumber() / 1e9

      // Apply 2% slippage
      const maxSolCost = solAmountLamports.mul(new BN(102)).div(new BN(100))

      return {
        success: true,
        estimatedTokens: tokensOut.toString(),
        estimatedTokensFormatted: tokensOut
          .div(new BN('**********'))
          .toString(),
        currentPrice: currentPricePerToken * 1e9,
        pricePerToken: currentPricePerToken,
        solAmount: solAmountLamports.toString(),
        maxSolCost: maxSolCost.toString(),
        slippage: 2,
        tokenReserves: defaultTokenReserves.toString(),
        solReserves: defaultSolReserves.toString()
      }
    }
  } catch (error) {
    console.error('Error calculating token price:', error)
    throw createError({
      statusCode: 500,
      statusMessage:
        error instanceof Error ? error.message : 'Internal server error'
    })
  }
})
