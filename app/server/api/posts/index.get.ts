import prisma from '~/lib/prisma'

export default defineEventHandler(async () => {
  try {
    const posts = await prisma.post.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            address: true,
            displayName: true,
            profilePicture: true
          }
        },
        media: {
          orderBy: { order: 'asc' }
        },
        mentions: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                address: true
              }
            },
            hashtag: {
              select: {
                id: true,
                name: true
              }
            },
            token: {
              select: {
                id: true,
                name: true,
                symbol: true,
                address: true
              }
            }
          }
        },
        _count: {
          select: {
            likes: true,
            replies: true,
            shares: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      where: {
        replyTo: null // Only fetch top-level posts for now
      },
      take: 50 // Limit to 50 most recent posts
    })

    // Transform the data to match the frontend interface
    const transformedPosts = posts.map(post => ({
      id: post.id,
      content: post.content,
      authorId: post.userId,
      createdAt: post.createdAt.toISOString(),
      user: {
        id: post.user.id,
        name: post.user.name,
        address: post.user.address,
        displayName: post.user.displayName,
        profilePicture: post.user.profilePicture
      },
      // TODO: remove this
      author: {
        id: post.user.id,
        name: post.user.name,
        address: post.user.address,
        displayName: post.user.displayName,
        profilePicture: post.user.profilePicture
      },
      media: post.media,
      likeCount: post._count.likes,
      replyCount: post._count.replies,
      shareCount: post._count.shares,
      mentions: post.mentions.map(mention => ({
        id: mention.id,
        user: mention.user,
        hashtag: mention.hashtag,
        token: mention.token
      }))
    }))

    return transformedPosts
  } catch (error) {
    console.error('Failed to fetch posts:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch posts'
    })
  }
})
