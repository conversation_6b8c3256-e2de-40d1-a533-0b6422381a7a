import prisma from '~/lib/prisma'
import type { Hashtag } from '~/types/database'
import {
  uploadPostMedia,
  savePostMediaToDatabase
} from '~/server/utils/post-media'
import { sendNotification } from '~/server/api/ws/notifications'

interface Token {
  id: number
  name: string
  symbol: string
  address: string
}

const extractHashtags = (content: string): string[] => {
  const hashtagRegex = /#(\w+)/g
  const hashtags: string[] = []
  let match

  while ((match = hashtagRegex.exec(content)) !== null) {
    hashtags.push(match[1])
  }

  return hashtags
}

// we get raw post content from the client so we need to extract the mentions too
// ex: $SOL is going places @user
// no id is supplied
const extractMentions = (content: string) => {
  const userMentionRegex = /@(\w+)/g
  const tokenMentionRegex = /\$(\w+)/g
  const mentions = {
    users: [] as string[],
    tokens: [] as string[]
  }
  let match

  while ((match = userMentionRegex.exec(content)) !== null) {
    mentions.users.push(match[1])
  }

  while ((match = tokenMentionRegex.exec(content)) !== null) {
    mentions.tokens.push(match[1])
  }

  return mentions
}

const replaceHashtagsWithIds = (
  content: string,
  hashtags: Hashtag[]
): string => {
  return content.replace(/#(\w+)/g, (match, p1) => {
    const hashtag = hashtags.find(h => h.name === p1)
    return hashtag ? `#${hashtag.id}` : match
  })
}

const replaceTokensWithIds = (content: string, tokens: Token[]): string => {
  return content.replace(/\$(\w+)/g, (match, p1) => {
    const token = tokens.find(t => t.symbol === p1 || t.name === p1)
    return token ? `$${token.id}` : match
  })
}

export default defineEventHandler(async event => {
  const session = await requireUserSession(event)

  // Check if this is a multipart request (with files) or JSON request
  const contentType = getHeader(event, 'content-type') || ''

  let content = ''
  const imageFiles: File[] = []

  if (contentType.includes('multipart/form-data')) {
    // Handle multipart form data (with files)
    const body = await readMultipartFormData(event)
    if (!body) {
      throw createError({
        statusCode: 400,
        statusMessage: 'No form data provided'
      })
    }

    // Parse form data
    for (const part of body) {
      if (part.name === 'content' && part.data) {
        content = part.data.toString()
      } else if (part.name === 'images' && part.filename && part.data) {
        // This is an image file
        const file = new File([part.data], part.filename, {
          type: part.type || 'image/jpeg'
        })
        imageFiles.push(file)
      }
    }
  } else {
    // Handle JSON request (legacy support)
    const body = await readBody(event)
    content = body.content
  }

  if ((!content || content.trim() === '') && !imageFiles.length) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Content is required'
    })
  }

  // Validate image count (max 4 images like Twitter)
  if (imageFiles.length > 4) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Maximum 4 images allowed per post'
    })
  }

  // extract hashtags
  const hashtags = extractHashtags(content)
  // extract mentions
  const mentions = extractMentions(content)

  // create hashtags in the database if they don't exist
  const existingHashtags = await prisma.hashtag.findMany({
    where: {
      name: {
        in: hashtags
      }
    }
  })

  const newHashtags = hashtags.filter(
    tag => !existingHashtags.some(h => h.name === tag)
  )

  const createdHashtags = await prisma.hashtag.createManyAndReturn({
    data: newHashtags.map(name => ({ name, postCount: 1 }))
  })

  // Update post count for existing hashtags
  if (existingHashtags.length > 0) {
    await prisma.hashtag.updateMany({
      where: {
        id: {
          in: existingHashtags.map(h => h.id)
        }
      },
      data: {
        postCount: {
          increment: 1
        }
      }
    })
  }

  // Combine existing and newly created hashtags
  const allHashtags = [...existingHashtags, ...createdHashtags]
  const contentWithHashtagIds = replaceHashtagsWithIds(content, allHashtags)

  // Find mentioned users and tokens
  const mentionedUsers =
    mentions.users.length > 0
      ? await prisma.user.findMany({
          where: {
            name: {
              in: mentions.users
            }
          }
        })
      : []

  const mentionedTokens =
    mentions.tokens.length > 0
      ? await prisma.token.findMany({
          where: {
            OR: [
              { name: { in: mentions.tokens } },
              { symbol: { in: mentions.tokens } }
            ]
          }
        })
      : []

  // Replace token symbols with IDs in content
  const finalContent = replaceTokensWithIds(
    contentWithHashtagIds,
    mentionedTokens
  )

  try {
    const post = await prisma.post.create({
      data: {
        content: finalContent.trim(),
        userId: session.user.id
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            address: true,
            displayName: true,
            profilePicture: true
          }
        }
      }
    })

    // Upload and save media if any
    if (imageFiles.length > 0) {
      const mediaResults = await uploadPostMedia(
        imageFiles,
        session.user.id,
        post.id
      )
      await savePostMediaToDatabase(post.id, mediaResults)
    }

    // Create mention records for hashtags
    const hashtagMentions = allHashtags.map(hashtag => ({
      postId: post.id,
      hashtagId: hashtag.id,
      userId: null,
      tokenId: null
    }))

    // Create mention records for users
    const userMentions = mentionedUsers.map(user => ({
      postId: post.id,
      userId: user.id,
      hashtagId: null,
      tokenId: null
    }))

    // Create mention records for tokens
    const tokenMentions = mentionedTokens.map(token => ({
      postId: post.id,
      tokenId: token.id,
      userId: null,
      hashtagId: null
    }))

    // Save all mentions to database
    const allMentions = [...hashtagMentions, ...userMentions, ...tokenMentions]
    if (allMentions.length > 0) {
      await prisma.mention.createMany({
        data: allMentions
      })
    }

    // Send notifications for user mentions
    if (mentionedUsers.length > 0) {
      const { sendNotification } = await import('~/server/api/ws/notifications')

      for (const mentionedUser of mentionedUsers) {
        if (mentionedUser.id !== session.user.id) {
          // Don't notify self
          await sendNotification({
            sourceUserId: session.user.id,
            targetUserId: mentionedUser.id,
            type: 'MENTION',
            content: 'mentioned you in a post',
            postId: post.id
          })
        }
      }
    }

    // Fetch the complete post with media for return
    const completePost = await prisma.post.findUnique({
      where: { id: post.id },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            address: true,
            displayName: true,
            profilePicture: true
          }
        },
        media: {
          orderBy: { order: 'asc' }
        },
        _count: {
          select: {
            likes: true,
            replies: true,
            shares: true
          }
        }
      }
    })

    // Transform to match frontend interface
    const transformedPost = {
      id: completePost!.id,
      content: completePost!.content,
      authorId: completePost!.userId,
      createdAt: completePost!.createdAt.toISOString(),
      author: {
        id: completePost!.user.id,
        name: completePost!.user.name,
        address: completePost!.user.address,
        displayName: completePost!.user.displayName,
        profilePicture: completePost!.user.profilePicture
      },
      media: completePost!.media,
      likeCount: completePost!._count.likes,
      replyCount: completePost!._count.replies,
      shareCount: completePost!._count.shares
    }

    return transformedPost
  } catch (error) {
    console.error('Failed to create post:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to create post'
    })
  }
})
