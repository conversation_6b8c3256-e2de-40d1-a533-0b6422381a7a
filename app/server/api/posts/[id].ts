import {
  getPostByIdExpanded,
  getPostRepliesByPostId
} from '~/server/utils/database'
import type { PostWithRelation } from '~/types/database'

const transformReply = (reply: PostWithRelation) => ({
  ...reply,
  authorId: reply.user.id,
  author: {
    id: reply.user.id,
    name: reply.user.name,
    address: reply.user.address,
    displayName: reply.user.displayName,
    profilePicture: reply.user.profilePicture
  },
  media: reply.media || []
})

export default defineEventHandler(async event => {
  // TODO: do not load replies if the user is not logged in
  requireUserSession(event)
  const { id } = getRouterParams(event)

  if (!id) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Post ID is required'
    })
  }

  const post = await getPostByIdExpanded(Number(id))
  if (!post) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Post not found'
    })
  }

  const replies = await getPostRepliesByPostId(Number(id))

  const tranformedPost = {
    ...post,
    // compat
    authorId: post.user.id,
    user: {
      id: post.user.id,
      name: post.user.name,
      address: post.user.address,
      displayName: post.user.displayName,
      profilePicture: post.user.profilePicture
    },
    author: {
      id: post.user.id,
      name: post.user.name,
      address: post.user.address,
      displayName: post.user.displayName,
      profilePicture: post.user.profilePicture
    },
    media: post.media || []
  }

  const transformedReplies = replies.map(reply => transformReply(reply))

  // Transform the post to include replies
  const postWithReplies = {
    ...tranformedPost,
    replies: transformedReplies
  }

  return {
    success: true,
    post: postWithReplies
  }
})
