import { postInteractionsService } from '~/server/services/post-interactions'

export default defineEventHandler(async event => {
  try {
    const body = await readBody(event)
    const { postIds } = body

    if (!Array.isArray(postIds) || postIds.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'postIds array is required'
      })
    }

    // Validate that all postIds are numbers
    const validPostIds = postIds.filter(id => Number.isInteger(id) && id > 0)

    if (validPostIds.length === 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'No valid post IDs provided'
      })
    }

    // Try to get authenticated user session, but don't require it
    let userId: number | null = null
    try {
      const session = await getUserSession(event)
      userId = session?.user?.id || null
    } catch (error) {
      // User is not authenticated, that's okay
      console.log(
        'User not authenticated, showing public interaction data only'
      )
    }

    const batchStatus = await postInteractionsService.getBatchInteractionStatus(
      validPostIds,
      userId
    )

    return {
      success: true,
      data: batchStatus
    }
  } catch (error) {
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }

    console.error('Error in batch interactions endpoint:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
