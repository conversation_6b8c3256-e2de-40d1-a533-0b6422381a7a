import { postInteractionsService } from '~/server/services/post-interactions'

export default defineEventHandler(async event => {
  try {
    const postId = parseInt(getRouterParam(event, 'id') || '0')

    if (!postId || postId <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid post ID'
      })
    }

    // Try to get authenticated user session, but don't require it
    let userId: number | null = null
    try {
      const session = await getUserSession(event)
      userId = session?.user?.id || null
    } catch (error) {
      // User is not authenticated, that's okay
      console.log(
        'User not authenticated, showing public interaction data only'
      )
    }

    const status = await postInteractionsService.getInteractionStatus(
      postId,
      userId
    )

    return {
      success: true,
      data: status
    }
  } catch (error) {
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }

    console.error('Error in interactions endpoint:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
