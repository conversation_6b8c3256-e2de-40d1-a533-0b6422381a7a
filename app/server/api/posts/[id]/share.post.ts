import { postInteractionsService } from '~/server/services/post-interactions'

export default defineEventHandler(async event => {
  try {
    const postId = parseInt(getRouterParam(event, 'id') || '0')

    if (!postId || postId <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid post ID'
      })
    }

    // Get authenticated user session
    const session = await requireUserSession(event)
    const userId = session.user.id

    const result = await postInteractionsService.toggleShare(postId, userId)

    if (!result.success) {
      throw createError({
        statusCode: 400,
        statusMessage: result.message,
        data: result.error
      })
    }

    return result
  } catch (error) {
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }

    console.error('Error in share endpoint:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
