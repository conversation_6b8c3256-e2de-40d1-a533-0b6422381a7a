import { postInteractionsService } from '~/server/services/post-interactions'

export default defineEventHandler(async event => {
  try {
    // TODO: add reply with images
    const postId = parseInt(getRouterParam(event, 'id') || '0')
    const body = await readBody(event)

    if (!postId || postId <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid post ID'
      })
    }

    if (
      !body.content ||
      typeof body.content !== 'string' ||
      body.content.trim().length === 0
    ) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Reply content is required'
      })
    }

    // Get authenticated user session
    const session = await requireUserSession(event)
    const userId = session.user.id

    const result = await postInteractionsService.addReply(
      postId,
      userId,
      body.content
    )

    if (!result.success) {
      throw createError({
        statusCode: 400,
        statusMessage: result.message,
        data: result.error
      })
    }

    return result
  } catch (error) {
    if (error && typeof error === 'object' && 'statusCode' in error) {
      throw error
    }

    console.error('Error in reply endpoint:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
