import { postInteractionsService } from '~/server/services/post-interactions'

export default defineEventHandler(async event => {
  try {
    const postId = parseInt(getRouterParam(event, 'id') || '0')
    const body = await readBody(event)

    if (!postId || postId <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid post ID'
      })
    }

    if (
      !body.content ||
      typeof body.content !== 'string' ||
      body.content.trim().length === 0
    ) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Reply content is required'
      })
    }

    // TODO: Get userId from authentication middleware
    // For now, using a placeholder - replace with actual auth
    const userId = 1 // Replace with actual user ID from auth

    const result = await postInteractionsService.addReply(
      postId,
      userId,
      body.content
    )

    if (!result.success) {
      throw createError({
        statusCode: 400,
        statusMessage: result.message,
        data: result.error
      })
    }

    return result
  } catch (error: any) {
    if (error.statusCode) {
      throw error
    }

    console.error('Error in reply endpoint:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
