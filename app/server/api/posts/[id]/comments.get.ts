import { postInteractionsService } from '~/server/services/post-interactions'

export default defineEventHandler(async event => {
  try {
    const postId = parseInt(getRouterParam(event, 'id') || '0')
    const query = getQuery(event)

    if (!postId || postId <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid post ID'
      })
    }

    const limit = parseInt(query.limit as string) || 10
    const offset = parseInt(query.offset as string) || 0

    const replies = await postInteractionsService.getReplies(
      postId,
      limit,
      offset
    )

    return {
      success: true,
      data: replies
    }
  } catch (error: any) {
    if (error.statusCode) {
      throw error
    }

    console.error('Error in replies endpoint:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
