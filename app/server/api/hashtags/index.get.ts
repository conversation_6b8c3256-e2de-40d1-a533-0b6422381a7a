import prisma from '~/lib/prisma'

export default defineEventHandler(async event => {
  try {
    const hashtags = await prisma.hashtag.findMany({
      orderBy: [{ postCount: 'desc' }, { name: 'asc' }],
      take: 100 // Limit to most popular 100 hashtags
    })

    return hashtags
  } catch (error) {
    console.error('Failed to fetch hashtags:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch hashtags'
    })
  }
})
