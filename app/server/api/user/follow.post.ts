import prisma from '~/lib/prisma'
import { sendNotification } from '~/server/api/ws/notifications'

export default defineEventHandler(async event => {
  const session = await requireUserSession(event)
  const followerId = session.user?.id

  if (!followerId) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized'
    })
  }

  const { userId: followedId } = await readBody(event)

  if (!followedId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'User ID is required'
    })
  }

  if (followerId === followedId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'You cannot follow yourself'
    })
  }

  try {
    // Check if already following
    const existingFollow = await prisma.follow.findUnique({
      where: {
        followerId_followedId: {
          followerId,
          followedId
        }
      }
    })

    if (existingFollow) {
      // Unfollow
      await prisma.follow.delete({
        where: {
          followerId_followedId: {
            followerId,
            followedId
          }
        }
      })

      return {
        success: true,
        following: false,
        message: 'Unfollowed successfully'
      }
    } else {
      // Follow
      await prisma.follow.create({
        data: {
          followerId,
          followedId
        }
      })

      // Send notification
      // const follower = await prisma.user.findUnique({
      //   where: { id: followerId },
      //   select: { name: true },
      // })

      await sendNotification({
        sourceUserId: followerId,
        targetUserId: followedId,
        type: 'FOLLOW',
        content: 'started following you'
      })

      return {
        success: true,
        following: true,
        message: 'Followed successfully'
      }
    }
  } catch (error) {
    console.error('Failed to toggle follow:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to process follow request'
    })
  }
})
