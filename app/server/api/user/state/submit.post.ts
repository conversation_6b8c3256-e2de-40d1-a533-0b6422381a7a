import { PublicKey } from '@solana/web3.js'

export default defineEventHandler(async event => {
  try {
    const { publicKey, signedTransaction } = await readBody(event)

    if (!publicKey) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Public key is required'
      })
    }

    // Validate the public key
    try {
      new PublicKey(publicKey)
    } catch {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid public key format'
      })
    }

    // In the new hybrid approach, user states are created automatically by the authority
    // during login. Users don't need to sign or submit anything.
    throw createError({
      statusCode: 400,
      statusMessage:
        'This endpoint is deprecated. User states are now created automatically during login. No user signature required.'
    })
  } catch (error: any) {
    console.error('Error in deprecated submit endpoint:', error)

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage:
        'This endpoint is deprecated. Please use the login flow instead.'
    })
  }
})
