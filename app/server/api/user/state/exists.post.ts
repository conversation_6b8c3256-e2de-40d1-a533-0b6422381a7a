import { PublicKey } from '@solana/web3.js'
import {
  initializeProgram,
  getUserStatePDAFromOnChainId
} from '~/server/services/web3'
import { getUserByAddress } from '~/server/services/database'

export default defineEventHandler(async event => {
  try {
    const { publicKey } = await readBody(event)

    if (!publicKey) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Public key is required'
      })
    }

    // Validate the public key
    try {
      new PublicKey(publicKey)
    } catch {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid public key format'
      })
    }

    // Get user from database to find their onChainId
    const user = await getUserByAddress(publicKey)

    if (!user) {
      return { exists: false, reason: 'User not found in database' }
    }

    // Initialize the program
    const program = await initializeProgram()

    // Get the user state PDA using onChainId
    const [userStatePda] = getUserStatePDAFromOnChainId(user.onChainId)

    // Check if the user state account exists on-chain
    const userStateAccount = await program.account.userState.fetchNullable(
      new PublicKey(userStatePda)
    )

    return {
      exists: userStateAccount !== null,
      onChainId: user.onChainId,
      statePda: userStatePda,
      hasDbRecord: !!user.userState
    }
  } catch (error: any) {
    console.error('Error checking user state existence:', error)

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to check user state existence'
    })
  }
})
