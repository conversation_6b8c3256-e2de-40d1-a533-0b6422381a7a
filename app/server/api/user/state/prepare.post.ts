import { PublicKey } from '@solana/web3.js'
import { getUserByAddress } from '~/server/services/database'

export default defineEventHandler(async event => {
  try {
    const { publicKey } = await readBody(event)

    if (!publicKey) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Public key is required'
      })
    }

    // Validate the public key
    try {
      new PublicKey(publicKey)
    } catch {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid public key format'
      })
    }

    // Check if user exists in database
    const user = await getUserByAddress(publicKey)

    if (!user) {
      throw createError({
        statusCode: 400,
        statusMessage:
          'User not found. Please login first to have your state created automatically.'
      })
    }

    if (user.userState) {
      throw createError({
        statusCode: 400,
        statusMessage: 'User state already exists'
      })
    }

    // In the new hybrid approach, user states are created automatically by the authority
    // during login. Users don't need to sign anything.
    throw createError({
      statusCode: 400,
      statusMessage:
        'This endpoint is deprecated. User states are now created automatically during login. No user signature required.'
    })
  } catch (error: any) {
    console.error('Error in deprecated prepare endpoint:', error)

    if (error.statusCode) {
      throw error
    }

    throw createError({
      statusCode: 500,
      statusMessage:
        'This endpoint is deprecated. Please use the login flow instead.'
    })
  }
})
