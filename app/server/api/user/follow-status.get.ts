import prisma from '~/lib/prisma'

export default defineEventHandler(async event => {
  const session = await requireUserSession(event)
  const currentUserId = session.user?.id

  if (!currentUserId) {
    throw createError({
      statusCode: 401,
      statusMessage: 'Unauthorized'
    })
  }

  const { userId } = getQuery(event)

  if (!userId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'User ID is required'
    })
  }

  try {
    const follow = await prisma.follow.findUnique({
      where: {
        followerId_followedId: {
          followerId: currentUserId,
          followedId: parseInt(userId as string)
        }
      }
    })

    return {
      following: !!follow
    }
  } catch (error) {
    console.error('Failed to check follow status:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to check follow status'
    })
  }
})
