import prisma from '~/lib/prisma'

export default defineEventHandler(async event => {
  const username = getRouterParam(event, 'username')
  const session = await getUserSession(event)
  const currentUserId = session.user?.id

  if (!username) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Username is required'
    })
  }

  try {
    // Find user by email (which in this app acts as username)
    // Based on the login flow, users are identified by their wallet address
    // But we need to check the schema to see how usernames are stored
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { address: username }, // If username is actually the wallet address
          { name: username }, // If username is the display name
          { email: { contains: username } } // If username part of email
        ]
      },
      select: {
        id: true,
        name: true,
        displayName: true,
        bio: true,
        profilePicture: true,
        coverImage: true,
        address: true,
        createdAt: true,
        _count: {
          select: {
            followers: true,
            followed: true,
            posts: true,
            tokens: true
          }
        }
      }
    })

    if (!user) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found'
      })
    }

    // Check if current user is following this user
    let isFollowing = false
    if (currentUserId && currentUserId !== user.id) {
      const followRelation = await prisma.follow.findUnique({
        where: {
          followerId_followedId: {
            followerId: currentUserId,
            followedId: user.id
          }
        }
      })
      isFollowing = !!followRelation
    }

    // Format the response
    return {
      id: user.id,
      name: user.displayName || 'Unnamed',
      username: user.name,
      bio: user.bio || '',
      profileImage: user.profilePicture || '',
      coverImage: user.coverImage || '',
      followers: user._count.followed,
      following: user._count.followers,
      postsCount: user._count.posts,
      tokensCount: user._count.tokens,
      joinDate: new Date(user.createdAt).toLocaleDateString('en-US', {
        month: 'short',
        year: 'numeric'
      }),
      address: user.address,
      isFollowing
    }
  } catch (error: unknown) {
    const err = error as { statusCode?: number }
    if (typeof err?.statusCode === 'number') {
      throw error
    }

    console.error('Error fetching user profile:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch user profile'
    })
  }
})
