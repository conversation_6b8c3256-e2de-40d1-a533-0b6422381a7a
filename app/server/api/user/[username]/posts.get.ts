import type { NuxtError } from 'nuxt/app'
import prisma from '~/lib/prisma'

export default defineEventHandler(async event => {
  const username = getRouterParam(event, 'username')
  const query = getQuery(event)
  const limit = parseInt(query.limit as string) || 20
  const offset = parseInt(query.offset as string) || 0

  if (!username) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Username is required'
    })
  }

  try {
    // First find the user
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { address: username },
          { name: username },
          { email: { contains: username } }
        ]
      },
      select: { id: true }
    })

    if (!user) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found'
      })
    }

    // Fetch user's posts
    const posts = await prisma.post.findMany({
      where: {
        userId: user.id,
        replyToId: null // Only get original posts, not replies
      },
      include: {
        // TODO: return only necessary fields
        user: true,
        _count: {
          select: {
            likes: true,
            replies: true,
            shares: true
          }
        },
        media: {
          select: {
            id: true,
            url: true,
            type: true,
            width: true,
            height: true,
            order: true
          },
          orderBy: {
            order: 'asc'
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset
    })

    return {
      posts: posts,
      hasMore: posts.length === limit
    }
  } catch (error: unknown) {
    if ((error as NuxtError).statusCode) {
      throw error
    }

    console.error('Error fetching user posts:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch user posts'
    })
  }
})
