import type { NuxtError } from 'nuxt/app'
import prisma from '~/lib/prisma'

export default defineEventHandler(async event => {
  const username = getRouterParam(event, 'username')
  const query = getQuery(event)
  const limit = parseInt(query.limit as string) || 20
  const offset = parseInt(query.offset as string) || 0

  if (!username) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Username is required'
    })
  }

  try {
    // First find the user
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { address: username },
          { name: username },
          { email: { contains: username } }
        ]
      },
      select: { id: true }
    })

    if (!user) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found'
      })
    }

    // Fetch user's replies (posts that have replyToId)
    const replies = await prisma.post.findMany({
      where: {
        userId: user.id,
        replyToId: { not: null } // Only get replies
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            displayName: true,
            address: true,
            profilePicture: true
          }
        },
        replyTo: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                displayName: true,
                address: true,
                profilePicture: true
              }
            }
          }
        },
        _count: {
          select: {
            likes: true,
            replies: true,
            shares: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      },
      take: limit,
      skip: offset
    })

    return {
      replies: replies,
      hasMore: replies.length === limit
    }
  } catch (error: unknown) {
    if (error as NuxtError) {
      throw error
    }

    console.error('Error fetching user replies:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to fetch user replies'
    })
  }
})
