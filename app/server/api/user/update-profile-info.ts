import prisma from '~/lib/prisma'
import {
  deleteStorageFile,
  generateFileName,
  validateImageFile
} from '~/server/utils/storage'

export default defineEventHandler(async event => {
  const session = await requireUserSession(event)

  if (event.method !== 'POST') {
    throw createError({
      statusCode: 405,
      statusMessage: 'Method not allowed'
    })
  }

  const body = await readMultipartFormData(event)
  if (!body) {
    throw createError({
      statusCode: 400,
      statusMessage: 'No form data provided'
    })
  }

  const formFields: { [key: string]: string | File } = {}
  const imageFiles: { [key: string]: File } = {}

  // Parse form data
  for (const part of body) {
    if (part.name) {
      if (part.filename && part.data) {
        // This is a file
        const file = new File([Buffer.from(part.data)], part.filename, {
          type: part.type || 'application/octet-stream'
        })
        formFields[part.name] = file
        imageFiles[part.name] = file
      } else if (part.data) {
        // This is a text field
        formFields[part.name] = part.data.toString()
      }
    }
  }

  // Validate required fields
  const displayName = formFields.displayName as string
  if (!displayName?.trim()) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Display name is required'
    })
  }

  const bio = (formFields.bio as string) || ''

  // Validate input lengths
  if (displayName.length > 50) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Display name must be 50 characters or less'
    })
  }

  if (bio.length > 160) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Bio must be 160 characters or less'
    })
  }

  // Initialize storage - this can be swapped for S3 by changing the driver
  const storage = useStorage('uploads')

  // Get current user data to cleanup old images if needed
  const currentUser = await prisma.user.findUnique({
    where: { id: session.user.id },
    select: { profilePicture: true, coverImage: true }
  })

  const updateData: {
    displayName: string
    bio: string
    profilePicture?: string
    coverImage?: string
  } = {
    displayName: displayName.trim(),
    bio: bio.trim()
  }

  // Handle profile image upload
  if (imageFiles.profileImage) {
    const file = imageFiles.profileImage

    // Validate file using utility
    validateImageFile(file, 5 * 1024 * 1024) // 5MB max

    const fileExtension = file.name.split('.').pop() || 'jpg'
    const fileName = generateFileName(session.user.id, 'profile', fileExtension)

    try {
      const buffer = Buffer.from(await file.arrayBuffer())
      await storage.setItemRaw(fileName, buffer)
      updateData.profilePicture = `/uploads/${fileName}`

      // Cleanup old profile image
      if (currentUser?.profilePicture) {
        await deleteStorageFile(currentUser.profilePicture)
      }
    } catch (error) {
      console.error('Failed to upload profile image:', error)
      throw createError({
        statusCode: 500,
        statusMessage: 'Failed to upload profile image'
      })
    }
  }

  // Handle cover image upload
  if (imageFiles.coverImage) {
    const file = imageFiles.coverImage

    // Validate file using utility
    validateImageFile(file, 10 * 1024 * 1024) // 10MB max

    const fileExtension = file.name.split('.').pop() || 'jpg'
    const fileName = generateFileName(session.user.id, 'cover', fileExtension)

    try {
      const buffer = Buffer.from(await file.arrayBuffer())
      await storage.setItemRaw(fileName, buffer)
      updateData.coverImage = `/uploads/${fileName}`

      // Cleanup old cover image
      if (currentUser?.coverImage) {
        await deleteStorageFile(currentUser.coverImage)
      }
    } catch (error) {
      console.error('Failed to upload cover image:', error)
      throw createError({
        statusCode: 500,
        statusMessage: 'Failed to upload cover image'
      })
    }
  }

  try {
    // Update user in database
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id },
      data: updateData,
      select: {
        id: true,
        displayName: true,
        bio: true,
        profilePicture: true,
        coverImage: true,
        updatedAt: true
      }
    })

    return {
      success: true,
      message: 'Profile updated successfully',
      user: updatedUser
    }
  } catch (error) {
    console.error('Database error:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to update profile'
    })
  }
})
