import prisma from '~/lib/prisma'
import { recommendationService } from '~/server/utils/recommendation'

export default defineEventHandler(async event => {
  const session = await getUserSession(event)
  const userId = session.user?.id

  // Allow non-authenticated users to get general recommendations
  const query = getQuery(event)
  const limit = parseInt(query.limit as string) || 3

  try {
    if (userId) {
      // Personalized recommendations for logged-in users
      const recommendedProfiles =
        await recommendationService.getRecommendedProfiles(userId, limit)
      return { profiles: recommendedProfiles }
    } else {
      // General popular profiles for non-authenticated users
      const popularProfiles = await prisma.user.findMany({
        select: {
          id: true,
          name: true,
          displayName: true,
          address: true,
          profilePicture: true,
          _count: {
            select: {
              followers: true,
              posts: true
            }
          }
        },
        orderBy: [
          { followers: { _count: 'desc' } },
          { posts: { _count: 'desc' } }
        ],
        take: limit
      })

      const formattedProfiles = popularProfiles.map(user => ({
        id: user.id,
        name: user.displayName || user.name || 'Anonymous',
        address: user.address,
        profileImage: user.profilePicture || '',
        followers: user._count.followers
      }))

      return { profiles: formattedProfiles }
    }
  } catch (error) {
    console.error('Failed to get recommended profiles:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get recommended profiles'
    })
  }
})
