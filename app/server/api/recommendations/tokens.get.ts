import prisma from '~/lib/prisma'
import { recommendationService } from '~/server/utils/recommendation'

export default defineEventHandler(async event => {
  const session = await getUserSession(event)
  const userId = session.user?.id

  const query = getQuery(event)
  const limit = parseInt(query.limit as string) || 5

  try {
    if (userId) {
      // Personalized token recommendations
      const recommendedTokens =
        await recommendationService.getRecommendedTokens(userId, limit)

      return {
        tokens: recommendedTokens.map(token => ({
          id: token.id,
          name: token.name,
          symbol: token.symbol,
          address: token.address,
          creator: {
            name: token.user.displayName || token.user.name || 'Anonymous',
            address: token.user.address
          },
          stats: {
            interactions: token._count.interactions,
            transactions: token._count.transactions
          },
          recentTransactions: token.transactions.slice(0, 3),
          score: token.score
        }))
      }
    } else {
      // General trending tokens for non-authenticated users
      const trendingTokens = await prisma.token.findMany({
        include: {
          user: {
            select: {
              name: true,
              displayName: true,
              address: true
            }
          },
          _count: {
            select: {
              interactions: true,
              transactions: true
            }
          },
          transactions: {
            select: {
              type: true,
              tokenAmount: true,
              amountLamports: true,
              createdAt: true
            },
            orderBy: { createdAt: 'desc' },
            take: 3
          }
        },
        orderBy: [
          { transactions: { _count: 'desc' } },
          { interactions: { _count: 'desc' } }
        ],
        take: limit
      })

      return {
        tokens: trendingTokens.map(token => ({
          id: token.id,
          name: token.name,
          symbol: token.symbol,
          address: token.address,
          creator: {
            name: token.user.displayName || token.user.name || 'Anonymous',
            address: token.user.address
          },
          stats: {
            interactions: token._count.interactions,
            transactions: token._count.transactions
          },
          recentTransactions: token.transactions
        }))
      }
    }
  } catch (error) {
    console.error('Failed to get recommended tokens:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get recommended tokens'
    })
  }
})
