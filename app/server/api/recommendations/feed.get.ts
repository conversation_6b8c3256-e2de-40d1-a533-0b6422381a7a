import { recommendationService } from '~/server/utils/recommendation'

export default defineEventHandler(async event => {
  const session = await getUserSession(event)
  const userId = session.user?.id

  const query = getQuery(event)
  const limit = parseInt(query.limit as string) || 20
  const type = (query.type as string) || 'mixed' // 'posts', 'replies', 'mixed'

  try {
    let recommendedContent: any[] = []

    if (userId) {
      // Personalized recommendations for authenticated users
      switch (type) {
        case 'posts':
          recommendedContent = await recommendationService.getRecommendedPosts(
            userId,
            limit
          )
          break

        case 'replies':
          recommendedContent =
            await recommendationService.getRecommendedReplies(userId, limit)
          break

        case 'mixed':
          {
            // Get mix of posts and replies
            const posts = await recommendationService.getRecommendedPosts(
              userId,
              Math.ceil(limit * 0.7)
            )
            const replies = await recommendationService.getRecommendedReplies(
              userId,
              Math.floor(limit * 0.3)
            )

            // Combine and sort by score
            recommendedContent = [
              ...posts.map(p => ({ ...p, type: 'post' })),
              ...replies.map(r => ({ ...r, type: 'reply' }))
            ]
              .sort((a, b) => b.score - a.score)
              .slice(0, limit)
          }
          break

        default:
          throw createError({
            statusCode: 400,
            statusMessage: 'Invalid type parameter'
          })
      }
    } else {
      // Fallback to general trending content for non-authenticated users
      const { default: prisma } = await import('~/lib/prisma')

      // Get recent posts with good engagement
      const posts = await prisma.post.findMany({
        where: {
          replyToId:
            type === 'replies'
              ? { not: null }
              : type === 'posts'
                ? null
                : undefined
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              address: true,
              displayName: true,
              profilePicture: true
            }
          },
          media: true,
          interactions: true,
          replies: true,
          replyTo:
            type !== 'posts'
              ? {
                  include: {
                    user: {
                      select: {
                        id: true,
                        name: true,
                        address: true,
                        displayName: true,
                        profilePicture: true
                      }
                    }
                  }
                }
              : undefined
        },
        orderBy: [{ createdAt: 'desc' }],
        take: limit
      })

      recommendedContent = posts.map(post => ({
        ...post,
        type: post.replyToId ? 'reply' : 'post',
        score: post.interactions.length + post.replies.length * 2,
        createdAt: post.createdAt.toISOString(),
        // author: post.user,
        originalPost: post.replyTo
          ? {
              id: post.replyTo.id,
              content: post.replyTo.content,
              // author: (post.replyTo as any).user,
              createdAt: post.replyTo.createdAt.toISOString()
            }
          : undefined,
        reply: post.replyToId
          ? {
              id: post.id,
              content: post.content,
              // author: post.user,
              createdAt: post.createdAt.toISOString(),
              media: post.media
            }
          : undefined
      }))
    }
    return {
      content: recommendedContent,
      hasMore: recommendedContent.length === limit
    }
  } catch (error) {
    console.error('Failed to get recommended feed:', error)
    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to get recommended feed'
    })
  }
})
