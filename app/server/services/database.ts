import prisma from '~/lib/prisma'
import type { Token } from '~/types/database'

export interface UserWithState {
  id: number
  address: string
  name: string | null
  email: string
  onChainId: number // This is guaranteed to be non-null
  onChainState: {
    id: number
    onChainId: number
    statePda: string
    tokenCount: number
  } | null
  createdAt: Date
  updatedAt: Date
}

/**
 * Get the next available onChainId for a new user
 */
export async function getNextOnChainId (): Promise<number> {
  const maxUser = await prisma.user.findFirst({
    where: { onChainId: { not: null } },
    orderBy: { onChainId: 'desc' },
    select: { onChainId: true }
  })

  return (maxUser?.onChainId || 0) + 1
}

/**
 * Create or get a user with onChainId assignment
 */
export async function createOrGetUser (
  walletAddress: string
): Promise<UserWithState> {
  // First try to find existing user
  let user = await prisma.user.findUnique({
    where: { address: walletAddress },
    include: { onChainState: true }
  })

  if (!user) {
    // Create new user with sequential onChainId
    const onChainId = await getNextOnChainId()
    let name = `${walletAddress.slice(0, 8)}`
    // check if the user already exists with the same name
    const existingUser = await prisma.user.findFirst({
      where: { name }
    })
    if (existingUser) {
      name += `_${existingUser.id}`
    }

    user = await prisma.user.create({
      data: {
        address: walletAddress,
        name: name,
        displayName: `User ${walletAddress.slice(0, 8)}`,
        email: `${walletAddress.slice(0, 8)}@wallet.local`,
        onChainId: onChainId
      },
      include: { onChainState: true }
    })
  } else if (!user.onChainId) {
    // Existing user without onChainId - assign one
    const onChainId = await getNextOnChainId()

    user = await prisma.user.update({
      where: { id: user.id },
      data: { onChainId: onChainId },
      include: { onChainState: true }
    })
  }

  return {
    id: user.id,
    address: user.address,
    name: user.name,
    email: user.email,
    onChainId: user.onChainId!,
    onChainState: user.onChainState
      ? {
          id: user.onChainState.id,
          onChainId: user.onChainState.onChainId,
          statePda: user.onChainState.statePda,
          tokenCount: user.onChainState.tokenCount
        }
      : null,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt
  }
}

/**
 * Create user state record in database after creating on-chain
 */
export async function createUserStateRecord (
  userId: number,
  onChainId: number,
  statePda: string
) {
  return await prisma.userState.create({
    data: {
      userId: userId,
      onChainId: onChainId,
      statePda: statePda,
      tokenCount: 0
    }
  })
}

/**
 * Update token count in database after creating a token
 */
export async function incrementTokenCount (onChainId: number): Promise<number> {
  const userState = await prisma.userState.update({
    where: { onChainId: onChainId },
    data: { tokenCount: { increment: 1 } }
  })

  return userState.tokenCount
}

/**
 * Get user by onChainId
 */
export async function getUserByOnChainId (
  onChainId: number
): Promise<UserWithState | null> {
  const user = await prisma.user.findUnique({
    where: { onChainId: onChainId },
    include: { onChainState: true }
  })

  if (!user) return null

  return {
    id: user.id,
    address: user.address,
    name: user.name,
    email: user.email,
    onChainId: user.onChainId!,
    onChainState: user.onChainState
      ? {
          id: user.onChainState.id,
          onChainId: user.onChainState.onChainId,
          statePda: user.onChainState.statePda,
          tokenCount: user.onChainState.tokenCount
        }
      : null,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt
  }
}

/**
 * Get user with state by wallet address
 */
export async function getUserByAddress (
  address: string
): Promise<UserWithState | null> {
  const user = await prisma.user.findUnique({
    where: { address: address },
    include: { onChainState: true }
  })

  if (!user || !user.onChainId) return null

  return {
    id: user.id,
    address: user.address,
    name: user.name,
    email: user.email,
    onChainId: user.onChainId, // This is guaranteed to be non-null due to the check above
    onChainState: user.onChainState
      ? {
          id: user.onChainState.id,
          onChainId: user.onChainState.onChainId,
          statePda: user.onChainState.statePda,
          tokenCount: user.onChainState.tokenCount
        }
      : null,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt
  }
}

export async function getTokenByAddress (
  address: string
): Promise<Token | null> {
  const token = await prisma.token.findUnique({
    where: { address: address }
  })

  return token
}
