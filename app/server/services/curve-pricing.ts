import { PublicKey } from '@solana/web3.js'

/**
 * TypeScript implementation matching the exact Rust bonding curve pricing model
 * Uses constant product formula (x * y = k) with identical parameters to the Rust version
 */

export interface TokenCurveData {
  tokenMint: string
  solReserves: bigint
  tokenReserves: bigint
  targetMarketCap: bigint
  currentMarketCap: bigint
  lastPrice: number
  creator: string
  lastPurchaseTimestamp: number
  purchaseCountWindow: number
  purchaseWindowStart: number
  bump: number
}

export interface PricingCalculation {
  amount: bigint
  fee: bigint
  total: bigint
  newSolReserves: bigint
  newTokenReserves: bigint
  newPrice: number
  newMarketCap: bigint
}

export interface BuyResult extends PricingCalculation {
  solCost: bigint
  tokensReceived: bigint
}

export interface SellResult extends PricingCalculation {
  solReceived: bigint
  tokensSold: bigint
}

export enum PricingError {
  INVALID_TOKEN_AMOUNT = 'Invalid token amount',
  EXCEEDS_PURCHASE_LIMIT = 'Purchase amount exceeds maximum allowed per transaction',
  EXCEEDS_SELL_LIMIT = 'Sell amount exceeds maximum allowed per transaction',
  INSUFFICIENT_RESERVES = 'Insufficient reserves for transaction',
  CALCULATION_OVERFLOW = 'Calculation overflow error',
  RATE_LIMITED = 'Purchase rate limited - please wait',
  TOKEN_MIGRATING = 'Token is migrating to DEX',
  INSUFFICIENT_TOKENS = 'Insufficient tokens available',
  INSUFFICIENT_SOL_RESERVES = 'Insufficient SOL reserves in curve'
}

export class CurvePricingService {
  // Constants matching the Rust implementation exactly
  private static readonly TRADING_FEE_BASIS_POINTS = 6 // 0.6% like pump.fun
  private static readonly FEE_DENOMINATOR = 1000
  private static readonly GRADUATION_THRESHOLD = 85_000_000_000n // ~85 SOL
  private static readonly TOTAL_SUPPLY = 1_000_000_000n * 10n ** 9n // 1B tokens with 9 decimals
  private static readonly INITIAL_SOL_RESERVES = 32_000_000_000n // 32 SOL in lamports
  private static readonly INITIAL_TOKEN_RESERVES = 1_000_000_000n * 10n ** 9n // 1B tokens
  private static readonly LAMPORTS_PER_SOL = 1_000_000_000n

  // Rate limiting constants
  private static readonly PURCHASE_WINDOW_SECONDS = 60
  private static readonly MAX_PURCHASES_PER_WINDOW = 50
  private static readonly MIN_TIME_BETWEEN_PURCHASES = 0 // Disabled for testing

  // Anti-manipulation constants
  private static readonly HIGH_ACTIVITY_THRESHOLD = 7
  private static readonly ACTIVITY_MULTIPLIER = 1.25 // 25% price increase during high activity
  private static readonly MIGRATION_THRESHOLD_PERCENTAGE = 995 // 99.5%
  private static readonly MAX_SELLABLE_PERCENTAGE = 95 // 95% of total supply

  /**
   * Initialize bonding curve with initial liquidity (ultra-low starting price)
   * Returns the initial SOL and token reserves for the curve
   */
  static initializeCurve (): { solReserves: bigint; tokenReserves: bigint } {
    return {
      solReserves: this.INITIAL_SOL_RESERVES,
      tokenReserves: this.INITIAL_TOKEN_RESERVES
    }
  }

  /**
   * Calculate the cost in SOL for buying a specific amount of tokens
   * Uses constant product bonding curve: x * y = k
   */
  static calculateBuyCost (
    tokenAmount: bigint,
    solReserves: bigint,
    tokenReserves: bigint
  ): bigint {
    if (tokenAmount <= 0n) {
      throw new Error(PricingError.INVALID_TOKEN_AMOUNT)
    }
    if (solReserves <= 0n || tokenReserves <= 0n) {
      throw new Error(PricingError.INSUFFICIENT_RESERVES)
    }

    // Calculate k (constant product)
    const k = solReserves * tokenReserves

    // Calculate new token reserves after purchase
    const newTokenReserves = tokenReserves - tokenAmount
    if (newTokenReserves <= 0n) {
      throw new Error(PricingError.INSUFFICIENT_RESERVES)
    }

    // Calculate required SOL amount: new_sol_reserves = k / new_token_reserves
    const newSolReserves = k / newTokenReserves
    const solAmount = newSolReserves - solReserves

    // Apply trading fee (0.6% like pump.fun)
    const fee =
      (solAmount * BigInt(this.TRADING_FEE_BASIS_POINTS)) /
      BigInt(this.FEE_DENOMINATOR)
    const totalCost = solAmount + fee

    return totalCost
  }

  /**
   * Calculate the amount of SOL to receive for selling a specific amount of tokens
   * Uses constant product bonding curve: x * y = k
   */
  static calculateSellAmount (
    tokenAmount: bigint,
    solReserves: bigint,
    tokenReserves: bigint
  ): bigint {
    if (tokenAmount <= 0n) {
      throw new Error(PricingError.INVALID_TOKEN_AMOUNT)
    }
    if (solReserves <= 0n || tokenReserves <= 0n) {
      throw new Error(PricingError.INSUFFICIENT_RESERVES)
    }

    // Calculate k (constant product)
    const k = solReserves * tokenReserves

    // Calculate new token reserves after selling
    const newTokenReserves = tokenReserves + tokenAmount

    // Calculate new SOL reserves: new_sol_reserves = k / new_token_reserves
    const newSolReserves = k / newTokenReserves
    const solAmount = solReserves - newSolReserves

    // Apply trading fee (0.6% like pump.fun)
    const fee =
      (solAmount * BigInt(this.TRADING_FEE_BASIS_POINTS)) /
      BigInt(this.FEE_DENOMINATOR)
    const finalAmount = solAmount - fee

    return finalAmount > 0n ? finalAmount : 0n
  }

  /**
   * Calculate current price per token in lamports
   */
  static calculateCurrentPrice (
    solReserves: bigint,
    tokenReserves: bigint
  ): number {
    if (tokenReserves === 0n) {
      return Number.MAX_SAFE_INTEGER // Infinite price if no tokens
    }

    // Price = SOL reserves / token reserves (normalized to per token with 9 decimals)
    const pricePerSmallestUnit = Number(solReserves) / Number(tokenReserves)
    const pricePerToken = pricePerSmallestUnit * Number(10n ** 9n)

    return pricePerToken
  }

  /**
   * Check if token is ready for graduation to DEX (like pump.fun)
   */
  static isGraduationReady (solReserves: bigint): boolean {
    return solReserves >= this.GRADUATION_THRESHOLD
  }

  /**
   * Calculate market cap based on current reserves (actual SOL that flowed in)
   */
  static calculateMarketCap (solReserves: bigint): bigint {
    return solReserves
  }

  /**
   * Calculate theoretical market cap based on current price and circulating supply
   */
  static calculateTheoreticalMarketCap (
    solReserves: bigint,
    tokenReserves: bigint,
    totalSupply: bigint = this.TOTAL_SUPPLY
  ): bigint {
    const pricePerToken = this.calculateCurrentPrice(solReserves, tokenReserves)
    const circulatingSupply = totalSupply - tokenReserves

    // Convert price back to lamports per token (with decimals)
    const priceInLamports = BigInt(
      Math.floor(pricePerToken / Number(10n ** 9n))
    )

    return (priceInLamports * circulatingSupply) / 10n ** 9n
  }

  /**
   * Check if a purchase should be rate-limited based on time-based restrictions
   */
  static shouldRateLimitPurchase (
    lastPurchaseTimestamp: number,
    purchaseCountWindow: number,
    purchaseWindowStart: number,
    currentTimestamp: number
  ): boolean {
    // Check minimum time between purchases (disabled for testing)
    if (
      this.MIN_TIME_BETWEEN_PURCHASES > 0 &&
      currentTimestamp - lastPurchaseTimestamp < this.MIN_TIME_BETWEEN_PURCHASES
    ) {
      return true
    }

    // Check if we're in a new window
    if (
      currentTimestamp - purchaseWindowStart >=
      this.PURCHASE_WINDOW_SECONDS
    ) {
      return false // New window, allow purchase
    }

    // Check if we've exceeded the purchase count for current window
    return purchaseCountWindow >= this.MAX_PURCHASES_PER_WINDOW
  }

  /**
   * Calculate dynamic pricing multiplier based on recent activity
   */
  static getActivityBasedMultiplier (
    purchaseCountWindow: number,
    purchaseWindowStart: number,
    currentTimestamp: number
  ): number {
    // Check if we're in an active window
    if (currentTimestamp - purchaseWindowStart < this.PURCHASE_WINDOW_SECONDS) {
      if (purchaseCountWindow >= this.HIGH_ACTIVITY_THRESHOLD) {
        return this.ACTIVITY_MULTIPLIER
      }
    }

    return 1.0 // No multiplier
  }

  /**
   * Check if token is ready for migration (99.5% threshold)
   */
  static isTokenMigrating (
    currentMarketCap: bigint,
    targetMarketCap: bigint,
    tokenReserves: bigint
  ): boolean {
    // Check market cap threshold
    if (targetMarketCap > this.LAMPORTS_PER_SOL) {
      // Only check if target > 1 SOL
      const migrationThreshold =
        (targetMarketCap * BigInt(this.MIGRATION_THRESHOLD_PERCENTAGE)) / 1000n
      if (currentMarketCap >= migrationThreshold) {
        return true
      }
    }

    // Check supply threshold (99.5% of total supply sold)
    const tokensSold = this.TOTAL_SUPPLY - tokenReserves
    const thresholdBuffer = (this.TOTAL_SUPPLY / 1000n) * 5n // 0.5%
    const supplyThreshold = this.TOTAL_SUPPLY - thresholdBuffer // 99.5%

    return tokensSold >= supplyThreshold
  }

  /**
   * Comprehensive buy calculation with all protections and multipliers
   */
  static calculateBuyWithProtections (
    tokenAmount: bigint,
    curveData: TokenCurveData,
    currentTimestamp: number = Math.floor(Date.now() / 1000)
  ): BuyResult {
    // Validate inputs
    if (tokenAmount <= 0n) {
      throw new Error(PricingError.INVALID_TOKEN_AMOUNT)
    }

    // Check if token is migrating
    if (
      this.isTokenMigrating(
        curveData.currentMarketCap,
        curveData.targetMarketCap,
        curveData.tokenReserves
      )
    ) {
      throw new Error(PricingError.TOKEN_MIGRATING)
    }

    // Check rate limiting
    if (
      this.shouldRateLimitPurchase(
        curveData.lastPurchaseTimestamp,
        curveData.purchaseCountWindow,
        curveData.purchaseWindowStart,
        currentTimestamp
      )
    ) {
      throw new Error(PricingError.RATE_LIMITED)
    }

    // Check token availability
    if (curveData.tokenReserves < tokenAmount) {
      throw new Error(PricingError.INSUFFICIENT_TOKENS)
    }

    // Check 95% supply cap
    const remainingAfterPurchase = curveData.tokenReserves - tokenAmount
    const tokensSoldAfterPurchase = this.TOTAL_SUPPLY - remainingAfterPurchase
    const maxSellable =
      (this.TOTAL_SUPPLY * BigInt(this.MAX_SELLABLE_PERCENTAGE)) / 100n

    if (tokensSoldAfterPurchase > maxSellable) {
      throw new Error(PricingError.INSUFFICIENT_TOKENS)
    }

    // Calculate base cost
    const baseCost = this.calculateBuyCost(
      tokenAmount,
      curveData.solReserves,
      curveData.tokenReserves
    )

    // Apply activity-based multiplier
    const activityMultiplier = this.getActivityBasedMultiplier(
      curveData.purchaseCountWindow,
      curveData.purchaseWindowStart,
      currentTimestamp
    )
    const costInLamports = BigInt(
      Math.floor(Number(baseCost) * activityMultiplier)
    )

    // Calculate new states
    const newSolReserves = curveData.solReserves + costInLamports
    const newTokenReserves = curveData.tokenReserves - tokenAmount
    const newPrice = this.calculateCurrentPrice(
      newSolReserves,
      newTokenReserves
    )
    const newMarketCap = this.calculateMarketCap(newSolReserves)

    // Calculate fee breakdown
    const solAmount = this.calculateBuyCost(
      tokenAmount,
      curveData.solReserves,
      curveData.tokenReserves
    )
    const rawCost =
      (solAmount * BigInt(this.FEE_DENOMINATOR)) /
      (BigInt(this.FEE_DENOMINATOR) + BigInt(this.TRADING_FEE_BASIS_POINTS))
    const fee = solAmount - rawCost

    return {
      amount: costInLamports,
      fee,
      total: costInLamports,
      newSolReserves,
      newTokenReserves,
      newPrice,
      newMarketCap,
      solCost: costInLamports,
      tokensReceived: tokenAmount
    }
  }

  /**
   * Comprehensive sell calculation with all protections
   */
  static calculateSellWithProtections (
    tokenAmount: bigint,
    curveData: TokenCurveData,
    currentTimestamp: number = Math.floor(Date.now() / 1000)
  ): SellResult {
    // Validate inputs
    if (tokenAmount <= 0n) {
      throw new Error(PricingError.INVALID_TOKEN_AMOUNT)
    }

    // Check if token is migrating
    if (
      this.isTokenMigrating(
        curveData.currentMarketCap,
        curveData.targetMarketCap,
        curveData.tokenReserves
      )
    ) {
      throw new Error(PricingError.TOKEN_MIGRATING)
    }

    // Check rate limiting
    if (
      this.shouldRateLimitPurchase(
        curveData.lastPurchaseTimestamp,
        curveData.purchaseCountWindow,
        curveData.purchaseWindowStart,
        currentTimestamp
      )
    ) {
      throw new Error(PricingError.RATE_LIMITED)
    }

    // Calculate sell amount
    const solAmount = this.calculateSellAmount(
      tokenAmount,
      curveData.solReserves,
      curveData.tokenReserves
    )

    // Check if curve has enough SOL reserves
    if (solAmount > curveData.solReserves) {
      throw new Error(PricingError.INSUFFICIENT_SOL_RESERVES)
    }

    // Calculate new states
    const newSolReserves = curveData.solReserves - solAmount
    const newTokenReserves = curveData.tokenReserves + tokenAmount
    const newPrice = this.calculateCurrentPrice(
      newSolReserves,
      newTokenReserves
    )
    const newMarketCap = this.calculateMarketCap(newSolReserves)

    // Calculate fee breakdown
    const rawAmount =
      (solAmount * BigInt(this.FEE_DENOMINATOR)) /
      (BigInt(this.FEE_DENOMINATOR) - BigInt(this.TRADING_FEE_BASIS_POINTS))
    const fee = rawAmount - solAmount

    return {
      amount: solAmount,
      fee,
      total: solAmount,
      newSolReserves,
      newTokenReserves,
      newPrice,
      newMarketCap,
      solReceived: solAmount,
      tokensSold: tokenAmount
    }
  }

  /**
   * Update purchase tracking for rate limiting
   */
  static updatePurchaseTracking (
    curveData: TokenCurveData,
    currentTimestamp: number
  ): {
    purchaseCountWindow: number
    purchaseWindowStart: number
    lastPurchaseTimestamp: number
  } {
    // If we're in a new window, reset the counter
    if (
      currentTimestamp - curveData.purchaseWindowStart >=
      this.PURCHASE_WINDOW_SECONDS
    ) {
      return {
        purchaseWindowStart: currentTimestamp,
        purchaseCountWindow: 1,
        lastPurchaseTimestamp: currentTimestamp
      }
    } else {
      return {
        purchaseWindowStart: curveData.purchaseWindowStart,
        purchaseCountWindow: curveData.purchaseCountWindow + 1,
        lastPurchaseTimestamp: currentTimestamp
      }
    }
  }

  /**
   * Convert lamports to SOL for display
   */
  static lamportsToSol (lamports: bigint): number {
    return Number(lamports) / Number(this.LAMPORTS_PER_SOL)
  }

  /**
   * Convert SOL to lamports
   */
  static solToLamports (sol: number): bigint {
    return BigInt(Math.floor(sol * Number(this.LAMPORTS_PER_SOL)))
  }

  /**
   * Convert token amount with decimals to display amount
   */
  static tokenAmountToDisplay (amount: bigint, decimals: number = 9): number {
    return Number(amount) / Math.pow(10, decimals)
  }

  /**
   * Convert display token amount to amount with decimals
   */
  static displayToTokenAmount (
    displayAmount: number,
    decimals: number = 9
  ): bigint {
    return BigInt(Math.floor(displayAmount * Math.pow(10, decimals)))
  }

  /**
   * Calculate percentage of total supply sold
   */
  static calculateSupplyPercentageSold (tokenReserves: bigint): number {
    const tokensSold = this.TOTAL_SUPPLY - tokenReserves
    return Number((tokensSold * 100n) / this.TOTAL_SUPPLY)
  }

  /**
   * Get price impact for a trade
   */
  static calculatePriceImpact (
    tokenAmount: bigint,
    solReserves: bigint,
    tokenReserves: bigint,
    isBuy: boolean
  ): number {
    const currentPrice = this.calculateCurrentPrice(solReserves, tokenReserves)

    let newSolReserves: bigint
    let newTokenReserves: bigint

    if (isBuy) {
      const cost = this.calculateBuyCost(
        tokenAmount,
        solReserves,
        tokenReserves
      )
      newSolReserves = solReserves + cost
      newTokenReserves = tokenReserves - tokenAmount
    } else {
      const received = this.calculateSellAmount(
        tokenAmount,
        solReserves,
        tokenReserves
      )
      newSolReserves = solReserves - received
      newTokenReserves = tokenReserves + tokenAmount
    }

    const newPrice = this.calculateCurrentPrice(
      newSolReserves,
      newTokenReserves
    )
    const priceImpact = Math.abs((newPrice - currentPrice) / currentPrice) * 100

    return priceImpact
  }

  /**
   * Simulate a trade and return all relevant information
   */
  static simulateTrade (
    tokenAmount: bigint,
    curveData: TokenCurveData,
    isBuy: boolean,
    currentTimestamp: number = Math.floor(Date.now() / 1000)
  ): {
    valid: boolean
    error?: string
    result?: BuyResult | SellResult
    priceImpact: number
    newSupplyPercentage: number
  } {
    try {
      const priceImpact = this.calculatePriceImpact(
        tokenAmount,
        curveData.solReserves,
        curveData.tokenReserves,
        isBuy
      )

      let result: BuyResult | SellResult
      let newSupplyPercentage: number

      if (isBuy) {
        result = this.calculateBuyWithProtections(
          tokenAmount,
          curveData,
          currentTimestamp
        )
        newSupplyPercentage = this.calculateSupplyPercentageSold(
          result.newTokenReserves
        )
      } else {
        result = this.calculateSellWithProtections(
          tokenAmount,
          curveData,
          currentTimestamp
        )
        newSupplyPercentage = this.calculateSupplyPercentageSold(
          result.newTokenReserves
        )
      }

      return {
        valid: true,
        result,
        priceImpact,
        newSupplyPercentage
      }
    } catch (error) {
      return {
        valid: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        priceImpact: 0,
        newSupplyPercentage: this.calculateSupplyPercentageSold(
          curveData.tokenReserves
        )
      }
    }
  }
}
