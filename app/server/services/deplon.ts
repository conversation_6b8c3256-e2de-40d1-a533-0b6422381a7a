/**
 * Program IDL in camelCase format in order to be used in JS/TS.
 *
 * Note that this is only a type helper and is not the actual IDL. The original
 * IDL can be found at `target/idl/deplon.json`.
 */
export type Deplon = {
  address: 'HEdgfKqP5AbTNRLHK7GwM2vsbYjo1Kb1r1P8cnr7NLP8'
  metadata: {
    name: 'deplon'
    version: '0.1.0'
    spec: '0.1.0'
    description: 'Created with Anchor'
  }
  instructions: [
    {
      name: 'buyToken'
      discriminator: [138, 127, 14, 91, 38, 87, 115, 105]
      accounts: [
        {
          name: 'buyer'
          writable: true
          signer: true
        },
        {
          name: 'mint'
          docs: ['The token mint being purchased']
          writable: true
        },
        {
          name: 'buyerTokenAccount'
          docs: ['Buyer\'s token account (where purchased tokens go)']
          writable: true
          pda: {
            seeds: [
              {
                kind: 'account'
                path: 'buyer'
              },
              {
                kind: 'const'
                value: [
                  6,
                  221,
                  246,
                  225,
                  215,
                  101,
                  161,
                  147,
                  217,
                  203,
                  225,
                  70,
                  206,
                  235,
                  121,
                  172,
                  28,
                  180,
                  133,
                  237,
                  95,
                  91,
                  55,
                  145,
                  58,
                  140,
                  245,
                  133,
                  126,
                  255,
                  0,
                  169
                ]
              },
              {
                kind: 'account'
                path: 'mint'
              }
            ]
            program: {
              kind: 'const'
              value: [
                140,
                151,
                37,
                143,
                78,
                36,
                137,
                241,
                187,
                61,
                16,
                41,
                20,
                142,
                13,
                131,
                11,
                90,
                19,
                153,
                218,
                255,
                16,
                132,
                4,
                142,
                123,
                216,
                219,
                233,
                248,
                89
              ]
            }
          }
        },
        {
          name: 'curveTokenAccount'
          docs: ['Curve\'s token account (source of tokens being sold)']
          writable: true
          pda: {
            seeds: [
              {
                kind: 'account'
                path: 'tokenCurveAccount'
              },
              {
                kind: 'const'
                value: [
                  6,
                  221,
                  246,
                  225,
                  215,
                  101,
                  161,
                  147,
                  217,
                  203,
                  225,
                  70,
                  206,
                  235,
                  121,
                  172,
                  28,
                  180,
                  133,
                  237,
                  95,
                  91,
                  55,
                  145,
                  58,
                  140,
                  245,
                  133,
                  126,
                  255,
                  0,
                  169
                ]
              },
              {
                kind: 'account'
                path: 'mint'
              }
            ]
            program: {
              kind: 'const'
              value: [
                140,
                151,
                37,
                143,
                78,
                36,
                137,
                241,
                187,
                61,
                16,
                41,
                20,
                142,
                13,
                131,
                11,
                90,
                19,
                153,
                218,
                255,
                16,
                132,
                4,
                142,
                123,
                216,
                219,
                233,
                248,
                89
              ]
            }
          }
        },
        {
          name: 'tokenCurveAccount'
          docs: ['The token curve account containing the bonding curve state']
          writable: true
          pda: {
            seeds: [
              {
                kind: 'const'
                value: [99, 117, 114, 118, 101]
              },
              {
                kind: 'account'
                path: 'mint'
              }
            ]
          }
        },
        {
          name: 'systemProgram'
          docs: ['System program for SOL transfers']
          address: '11111111111111111111111111111111'
        },
        {
          name: 'tokenProgram'
          docs: ['Token program for token transfers']
          address: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'
        },
        {
          name: 'associatedTokenProgram'
          docs: ['Associated Token Program']
          address: 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL'
        }
      ]
      args: [
        {
          name: 'tokenAmount'
          type: 'u64'
        },
        {
          name: 'maxSolCost'
          type: 'u64'
        }
      ]
    },
    {
      name: 'createToken'
      discriminator: [84, 52, 204, 228, 24, 140, 234, 75]
      accounts: [
        {
          name: 'mint'
          writable: true
          pda: {
            seeds: [
              {
                kind: 'const'
                value: [109, 105, 110, 116]
              },
              {
                kind: 'arg'
                path: 'userId'
              },
              {
                kind: 'arg'
                path: 'tokenCount'
              }
            ]
          }
        },
        {
          name: 'payer'
          writable: true
          signer: true
        },
        {
          name: 'user'
        },
        {
          name: 'metadata'
          writable: true
          pda: {
            seeds: [
              {
                kind: 'const'
                value: [109, 101, 116, 97, 100, 97, 116, 97]
              },
              {
                kind: 'const'
                value: [
                  11,
                  112,
                  101,
                  177,
                  227,
                  209,
                  124,
                  69,
                  56,
                  157,
                  82,
                  127,
                  107,
                  4,
                  195,
                  205,
                  88,
                  184,
                  108,
                  115,
                  26,
                  160,
                  253,
                  181,
                  73,
                  182,
                  209,
                  188,
                  3,
                  248,
                  41,
                  70
                ]
              },
              {
                kind: 'account'
                path: 'mint'
              }
            ]
            program: {
              kind: 'const'
              value: [
                11,
                112,
                101,
                177,
                227,
                209,
                124,
                69,
                56,
                157,
                82,
                127,
                107,
                4,
                195,
                205,
                88,
                184,
                108,
                115,
                26,
                160,
                253,
                181,
                73,
                182,
                209,
                188,
                3,
                248,
                41,
                70
              ]
            }
          }
        },
        {
          name: 'mintAuthority'
          pda: {
            seeds: [
              {
                kind: 'const'
                value: [
                  109,
                  105,
                  110,
                  116,
                  95,
                  97,
                  117,
                  116,
                  104,
                  111,
                  114,
                  105,
                  116,
                  121
                ]
              },
              {
                kind: 'arg'
                path: 'userId'
              }
            ]
          }
        },
        {
          name: 'tokenCurveAccount'
          writable: true
          pda: {
            seeds: [
              {
                kind: 'const'
                value: [99, 117, 114, 118, 101]
              },
              {
                kind: 'account'
                path: 'mint'
              }
            ]
          }
        },
        {
          name: 'curveTokenAccount'
          writable: true
          pda: {
            seeds: [
              {
                kind: 'account'
                path: 'tokenCurveAccount'
              },
              {
                kind: 'const'
                value: [
                  6,
                  221,
                  246,
                  225,
                  215,
                  101,
                  161,
                  147,
                  217,
                  203,
                  225,
                  70,
                  206,
                  235,
                  121,
                  172,
                  28,
                  180,
                  133,
                  237,
                  95,
                  91,
                  55,
                  145,
                  58,
                  140,
                  245,
                  133,
                  126,
                  255,
                  0,
                  169
                ]
              },
              {
                kind: 'account'
                path: 'mint'
              }
            ]
            program: {
              kind: 'const'
              value: [
                140,
                151,
                37,
                143,
                78,
                36,
                137,
                241,
                187,
                61,
                16,
                41,
                20,
                142,
                13,
                131,
                11,
                90,
                19,
                153,
                218,
                255,
                16,
                132,
                4,
                142,
                123,
                216,
                219,
                233,
                248,
                89
              ]
            }
          }
        },
        {
          name: 'userState'
          writable: true
          pda: {
            seeds: [
              {
                kind: 'const'
                value: [117, 115, 101, 114, 95, 115, 116, 97, 116, 101]
              },
              {
                kind: 'arg'
                path: 'userId'
              }
            ]
          }
        },
        {
          name: 'associatedTokenProgram'
          address: 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL'
        },
        {
          name: 'systemProgram'
          address: '11111111111111111111111111111111'
        },
        {
          name: 'tokenProgram'
          address: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'
        },
        {
          name: 'rent'
          address: 'SysvarRent111111111111111111111111111111111'
        },
        {
          name: 'tokenMetadataProgram'
          address: 'metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s'
        }
      ]
      args: [
        {
          name: 'userId'
          type: 'u32'
        },
        {
          name: 'tokenCount'
          type: 'u32'
        },
        {
          name: 'name'
          type: 'string'
        },
        {
          name: 'symbol'
          type: 'string'
        },
        {
          name: 'uri'
          type: 'string'
        }
      ]
    },
    {
      name: 'initUserState'
      discriminator: [229, 142, 179, 158, 177, 92, 220, 92]
      accounts: [
        {
          name: 'userState'
          writable: true
          pda: {
            seeds: [
              {
                kind: 'const'
                value: [117, 115, 101, 114, 95, 115, 116, 97, 116, 101]
              },
              {
                kind: 'arg'
                path: 'userId'
              }
            ]
          }
        },
        {
          name: 'payer'
          writable: true
          signer: true
        },
        {
          name: 'user'
        },
        {
          name: 'systemProgram'
          address: '11111111111111111111111111111111'
        },
        {
          name: 'rent'
          address: 'SysvarRent111111111111111111111111111111111'
        }
      ]
      args: [
        {
          name: 'userId'
          type: 'u32'
        }
      ]
    },
    {
      name: 'initialize'
      discriminator: [175, 175, 109, 31, 13, 152, 155, 237]
      accounts: []
      args: []
    },
    {
      name: 'sellToken'
      discriminator: [109, 61, 40, 187, 230, 176, 135, 174]
      accounts: [
        {
          name: 'seller'
          writable: true
          signer: true
        },
        {
          name: 'mint'
          docs: ['The token mint being sold']
          writable: true
        },
        {
          name: 'sellerTokenAccount'
          docs: ['Seller\'s token account (where tokens are withdrawn from)']
          writable: true
          pda: {
            seeds: [
              {
                kind: 'account'
                path: 'seller'
              },
              {
                kind: 'const'
                value: [
                  6,
                  221,
                  246,
                  225,
                  215,
                  101,
                  161,
                  147,
                  217,
                  203,
                  225,
                  70,
                  206,
                  235,
                  121,
                  172,
                  28,
                  180,
                  133,
                  237,
                  95,
                  91,
                  55,
                  145,
                  58,
                  140,
                  245,
                  133,
                  126,
                  255,
                  0,
                  169
                ]
              },
              {
                kind: 'account'
                path: 'mint'
              }
            ]
            program: {
              kind: 'const'
              value: [
                140,
                151,
                37,
                143,
                78,
                36,
                137,
                241,
                187,
                61,
                16,
                41,
                20,
                142,
                13,
                131,
                11,
                90,
                19,
                153,
                218,
                255,
                16,
                132,
                4,
                142,
                123,
                216,
                219,
                233,
                248,
                89
              ]
            }
          }
        },
        {
          name: 'curveTokenAccount'
          docs: ['Curve\'s token account (destination for sold tokens)']
          writable: true
          pda: {
            seeds: [
              {
                kind: 'account'
                path: 'tokenCurveAccount'
              },
              {
                kind: 'const'
                value: [
                  6,
                  221,
                  246,
                  225,
                  215,
                  101,
                  161,
                  147,
                  217,
                  203,
                  225,
                  70,
                  206,
                  235,
                  121,
                  172,
                  28,
                  180,
                  133,
                  237,
                  95,
                  91,
                  55,
                  145,
                  58,
                  140,
                  245,
                  133,
                  126,
                  255,
                  0,
                  169
                ]
              },
              {
                kind: 'account'
                path: 'mint'
              }
            ]
            program: {
              kind: 'const'
              value: [
                140,
                151,
                37,
                143,
                78,
                36,
                137,
                241,
                187,
                61,
                16,
                41,
                20,
                142,
                13,
                131,
                11,
                90,
                19,
                153,
                218,
                255,
                16,
                132,
                4,
                142,
                123,
                216,
                219,
                233,
                248,
                89
              ]
            }
          }
        },
        {
          name: 'tokenCurveAccount'
          docs: ['The token curve account containing the bonding curve state']
          writable: true
          pda: {
            seeds: [
              {
                kind: 'const'
                value: [99, 117, 114, 118, 101]
              },
              {
                kind: 'account'
                path: 'mint'
              }
            ]
          }
        },
        {
          name: 'systemProgram'
          docs: ['System program for SOL transfers']
          address: '11111111111111111111111111111111'
        },
        {
          name: 'tokenProgram'
          docs: ['Token program for token transfers']
          address: 'TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA'
        },
        {
          name: 'associatedTokenProgram'
          docs: ['Associated Token Program']
          address: 'ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL'
        }
      ]
      args: [
        {
          name: 'tokenAmount'
          type: 'u64'
        },
        {
          name: 'minSolAmount'
          type: 'u64'
        }
      ]
    },
    {
      name: 'updateTargetSolReserves'
      discriminator: [215, 32, 138, 84, 204, 74, 140, 117]
      accounts: [
        {
          name: 'platformAuthority'
          docs: ['Platform authority who can update target SOL reserves']
          writable: true
          signer: true
        },
        {
          name: 'mint'
          docs: ['The token mint']
          writable: true
        },
        {
          name: 'tokenCurveAccount'
          docs: ['The token curve account to update']
          writable: true
          pda: {
            seeds: [
              {
                kind: 'const'
                value: [99, 117, 114, 118, 101]
              },
              {
                kind: 'account'
                path: 'mint'
              }
            ]
          }
        }
      ]
      args: [
        {
          name: 'newTargetSolReservesSol'
          type: 'u64'
        }
      ]
    }
  ]
  accounts: [
    {
      name: 'tokenCurveAccount'
      discriminator: [155, 48, 177, 152, 28, 2, 252, 99]
    },
    {
      name: 'userState'
      discriminator: [72, 177, 85, 249, 76, 167, 186, 126]
    }
  ]
  events: [
    {
      name: 'buy'
      discriminator: [104, 229, 167, 8, 240, 133, 178, 57]
    },
    {
      name: 'sell'
      discriminator: [208, 253, 142, 56, 83, 4, 87, 225]
    }
  ]
  errors: [
    {
      code: 6000
      name: 'invalidTokenAmount'
      msg: 'Invalid token amount'
    },
    {
      code: 6001
      name: 'insufficientTokens'
      msg: 'Insufficient tokens to sell'
    },
    {
      code: 6002
      name: 'insufficientSolAmount'
      msg: 'Insufficient SOL amount received'
    },
    {
      code: 6003
      name: 'insufficientSolReserves'
      msg: 'Insufficient SOL reserves in curve'
    },
    {
      code: 6004
      name: 'exceedsSellLimit'
      msg: 'Sell amount exceeds maximum allowed per transaction'
    },
    {
      code: 6005
      name: 'tokenMigrating'
      msg: 'Token is migrating to DEX - trading and transfers are disabled'
    },
    {
      code: 6006
      name: 'rateLimited'
      msg: 'Sell rate limited - please wait before making another sell'
    }
  ]
  types: [
    {
      name: 'buy'
      type: {
        kind: 'struct'
        fields: [
          {
            name: 'buyer'
            type: 'pubkey'
          },
          {
            name: 'mint'
            type: 'pubkey'
          },
          {
            name: 'tokenAmount'
            type: 'u64'
          },
          {
            name: 'solCost'
            type: 'u64'
          },
          {
            name: 'priceBefore'
            type: 'f64'
          },
          {
            name: 'priceAfter'
            type: 'f64'
          },
          {
            name: 'solResBefore'
            type: 'u64'
          },
          {
            name: 'solResAfter'
            type: 'u64'
          }
        ]
      }
    },
    {
      name: 'sell'
      type: {
        kind: 'struct'
        fields: [
          {
            name: 'seller'
            type: 'pubkey'
          },
          {
            name: 'mint'
            type: 'pubkey'
          },
          {
            name: 'tokenAmount'
            type: 'u64'
          },
          {
            name: 'solAmount'
            type: 'u64'
          },
          {
            name: 'priceBefore'
            type: 'f64'
          },
          {
            name: 'priceAfter'
            type: 'f64'
          },
          {
            name: 'solResBefore'
            type: 'u64'
          },
          {
            name: 'solResAfter'
            type: 'u64'
          }
        ]
      }
    },
    {
      name: 'tokenCurveAccount'
      type: {
        kind: 'struct'
        fields: [
          {
            name: 'tokenMint'
            type: 'pubkey'
          },
          {
            name: 'solReserves'
            docs: ['Current SOL reserves in the pool (in lamports)']
            type: 'u64'
          },
          {
            name: 'tokenReserves'
            docs: ['Current token reserves in the pool (with decimals)']
            type: 'u64'
          },
          {
            name: 'targetSolReserves'
            docs: ['Target SOL reserves for migration to Raydium (in lamports)']
            type: 'u64'
          },
          {
            name: 'lastPrice'
            docs: ['Current last price of the token in SOL']
            type: 'f64'
          },
          {
            name: 'creator'
            docs: ['The creator of this token']
            type: 'pubkey'
          },
          {
            name: 'lastPurchaseTimestamp'
            docs: [
              'Timestamp of the last purchase (for anti-sniper protection)'
            ]
            type: 'i64'
          },
          {
            name: 'purchaseCountWindow'
            docs: ['Number of purchases in the current time window']
            type: 'u16'
          },
          {
            name: 'purchaseWindowStart'
            docs: ['Timestamp when the current purchase window started']
            type: 'i64'
          },
          {
            name: 'bump'
            docs: ['Pool bump seed']
            type: 'u8'
          }
        ]
      }
    },
    {
      name: 'userState'
      type: {
        kind: 'struct'
        fields: [
          {
            name: 'owner'
            type: 'pubkey'
          },
          {
            name: 'tokenCount'
            type: 'u64'
          }
        ]
      }
    }
  ]
}
