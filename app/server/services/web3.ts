import {
  Keypair,
  <PERSON>K<PERSON>,
  <PERSON><PERSON><PERSON>ram,
  SYSVAR_RENT_PUBKEY,
  Transaction
} from '@solana/web3.js'
import { Program, AnchorProvider, Wallet } from '@coral-xyz/anchor'
import * as anchor from '@coral-xyz/anchor'
import type { Deplon } from './deplon'
import path from 'path'
import fs from 'fs'
import {
  ASSOCIATED_TOKEN_PROGRAM_ID,
  TOKEN_PROGRAM_ID
} from '@solana/spl-token'
import type { NuxtError } from 'nuxt/app'

export let PROGRAM: Program<Deplon> | null = null

export interface Web3UserState {
  owner: PublicKey
  tokenCount: anchor.BN
}

export interface TokenMetadata {
  name: string
  symbol: string
  description?: string
  image?: string
}

export interface UserInfo {
  onChainId: number
  walletAddress: string
}

const initializeProgram = async () => {
  if (PROGRAM) return PROGRAM

  try {
    // Read the IDL file directly
    const idlPath = path.resolve('./deplon.json')
    const idlString = fs.readFileSync(idlPath, 'utf8')
    const idl = JSON.parse(idlString)

    // Read program ID from Anchor.toml or use environment variable
    // const programId = process.env.WEB3_PROGRAM

    // Create connection and provider
    const connection = new anchor.web3.Connection(
      process.env.WEB3_PROVIDER_URL || 'http://localhost:8899'
    )

    const wallet = new Wallet(getAuthorityKeypair())
    const provider = new AnchorProvider(connection, wallet, {})

    // Create program instance
    PROGRAM = new Program(idl, provider) as Program<Deplon>

    return PROGRAM
  } catch (error: any) {
    throw new Error(`Failed to initialize Anchor program: ${error.message}`)
  }
}

export { initializeProgram }

// Get user state PDA using sequential onChainId (not wallet address)
export const getUserStatePDAFromOnChainId = (
  onChainId: number
): [string, number] => {
  // Use the program ID from environment or default
  const programId = new PublicKey(
    process.env.WEB3_PROGRAM || 'HEdgfKqP5AbTNRLHK7GwM2vsbYjo1Kb1r1P8cnr7NLP8'
  )

  // userId is u32 in the IDL, so use 4 bytes in little-endian format
  const userIdBuffer = Buffer.alloc(4)
  userIdBuffer.writeUInt32LE(onChainId, 0)

  const [userStatePda, userStateBump] = PublicKey.findProgramAddressSync(
    [Buffer.from('user_state'), userIdBuffer],
    programId
  )
  return [userStatePda.toBase58(), userStateBump]
}

export const getTokenMintPda = (onChainId: number, tokenCount: number) => {
  // Use the program ID from environment or default
  const programId = new PublicKey(
    process.env.WEB3_PROGRAM || 'HEdgfKqP5AbTNRLHK7GwM2vsbYjo1Kb1r1P8cnr7NLP8'
  )

  // Both userId and tokenCount are u32 in the IDL, so use 4 bytes each
  const userIdBuffer = Buffer.alloc(4)
  userIdBuffer.writeUInt32LE(onChainId, 0)

  const tokenCountBuffer = Buffer.alloc(4)
  tokenCountBuffer.writeUInt32LE(tokenCount, 0)

  return PublicKey.findProgramAddressSync(
    [Buffer.from('mint'), userIdBuffer, tokenCountBuffer],
    programId
  )
}

export const getMintAuthorityPda = (onChainId: number) => {
  // Use the program ID from environment or default
  const programId = new PublicKey(
    process.env.WEB3_PROGRAM || 'HEdgfKqP5AbTNRLHK7GwM2vsbYjo1Kb1r1P8cnr7NLP8'
  )

  // userId is u32 in the IDL, so use 4 bytes
  const userIdBuffer = Buffer.alloc(4)
  userIdBuffer.writeUInt32LE(onChainId, 0)

  return PublicKey.findProgramAddressSync(
    [Buffer.from('mint_authority'), userIdBuffer],
    programId
  )
}

export const getTokenMintAccount = async (
  onChainId: number,
  tokenCount: number
) => {
  const program = await initializeProgram()
  const [mintPda, _] = getTokenMintPda(onChainId, tokenCount)
  return await program.provider.connection.getAccountInfo(
    new PublicKey(mintPda)
  )
}

export const getAuthorityKeypair = (): Keypair => {
  // plain text private key
  const privateKeyPath = process.env.WEB3_AUTHORITY_PRIVATE
  if (!privateKeyPath) {
    throw new Error(
      'WEB3_AUTHORITY_PRIVATE is not set in environment variables'
    )
  }
  const privateKeyBytes = JSON.parse(
    fs.readFileSync(privateKeyPath, 'utf8').trim()
  )
  if (!Array.isArray(privateKeyBytes) || privateKeyBytes.length !== 64) {
    throw new Error('Invalid private key format')
  }

  try {
    return Keypair.fromSecretKey(Uint8Array.from(privateKeyBytes))
  } catch (error: any) {
    throw new Error(
      `Failed to create Keypair from private key: ${error.message}`
    )
  }
}

export const createUserStateOnChain = async (onChainId: number) => {
  try {
    // Initialize the program
    const program = await initializeProgram()

    // Get the user state PDA using onChainId
    const [userStatePda] = getUserStatePDAFromOnChainId(onChainId)
    const userStatePubkey = new PublicKey(userStatePda)

    // Check if user state already exists
    const existingUserState =
      await program.account.userState.fetchNullable(userStatePubkey)

    if (existingUserState) {
      console.warn('User state already exists for onChainId:', onChainId)
      return userStatePda // Already exists, return the PDA
    }

    // Get authority keypair for signing
    const authority = getAuthorityKeypair()

    // Create the init_user_state instruction (authority-only, no user signature)
    await program.methods
      .initUserState(onChainId)
      .accountsPartial({
        userState: userStatePubkey,
        payer: authority.publicKey,
        user: authority.publicKey, // Authority acts as the user for this instruction
        systemProgram: SystemProgram.programId,
        rent: new PublicKey('SysvarRent111111111111111111111111111111111')
      })
      .signers([authority])
      .rpc()

    return userStatePda
  } catch (error: unknown) {
    console.error('Failed to create user state on-chain:', error as NuxtError)
    throw new Error(
      `On-chain user state creation failed: ${(error as NuxtError).message}`
    )
  }
}

export const getOrCreateUserState = async (onChainId: number) => {
  try {
    const program = await initializeProgram()
    const [stateAddress] = getUserStatePDAFromOnChainId(onChainId)
    const userState = await program.account.userState.fetchNullable(
      new PublicKey(stateAddress)
    )

    if (!userState) {
      throw new Error(
        `User state does not exist for onChainId ${onChainId}. User state must be created first.`
      )
    }

    return userState
  } catch (error: any) {
    throw new Error(`Failed to get user state: ${error.message}`)
  }
}

export const createToken = async (
  metadata: TokenMetadata,
  onChainId: number,
  tokenCount: number
) => {
  try {
    const program = await initializeProgram()
    const authorityKp = getAuthorityKeypair()

    // Get the user state PDA
    const [userStatePda] = getUserStatePDAFromOnChainId(onChainId)

    // Get the token mint PDA that will be created
    const [mintPda] = getTokenMintPda(onChainId, tokenCount)

    // Get the mint authority PDA
    const [mintAuthorityPda] = getMintAuthorityPda(onChainId)

    // Authority creates token for the user - no user signature required
    const tx = await program.methods
      .createToken(
        onChainId,
        tokenCount,
        metadata.name,
        metadata.symbol,
        'https://example.com/metadata.json'
      )
      .accountsPartial({
        mint: new PublicKey(mintPda),
        payer: authorityKp.publicKey,
        user: authorityKp.publicKey, // Authority acts as the user
        mintAuthority: new PublicKey(mintAuthorityPda),
        userState: new PublicKey(userStatePda),
        systemProgram: SystemProgram.programId,
        tokenProgram: TOKEN_PROGRAM_ID,
        rent: SYSVAR_RENT_PUBKEY,
        associatedTokenProgram: ASSOCIATED_TOKEN_PROGRAM_ID,
        tokenMetadataProgram: new PublicKey(
          'metaqbxxUerdq28cj1RbAWkYQm3ybzjb6a8bt518x1s'
        )
      })
      .signers([authorityKp])
      .rpc()

    console.log(
      `Authority created token '${metadata.name}' for onChainId ${onChainId} with tx: ${tx}`
    )

    return [mintPda, tokenCount + 1] // Return the mint PDA and new token count
  } catch (error: any) {
    console.error(`Failed to create token: ${error.message}`)
    throw error
  }
}
