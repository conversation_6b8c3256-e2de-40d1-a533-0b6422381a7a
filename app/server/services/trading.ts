import { <PERSON><PERSON> } from 'helius-sdk'
import type { Peer } from 'crossws'
import { Message } from 'crossws'
import { PublicKey } from '@solana/web3.js'
import type { Deplon } from './deplon'
import type { Program } from '@coral-xyz/anchor'
import { initializeProgram } from './web3'
import { getTokenByAddress, getUser<PERSON>yAddress } from './database'
import { BN } from 'bn.js'
import { getLastCachedPrice } from '~/server/utils/solprice'
import prisma from '~/lib/prisma'
import type { Token, User } from '~/types/database'

enum TradingMessageType {
  // initial connection message
  CONNECTED,
  // price and mcap data only (eg in the trading for)
  TOKEN_PRICE,
  // token info data (eg in the sidebar including the price)
  TOKEN_INFO,
  // token activity data (eg transactions and holders)
  TOKEN_ACTIVITY,
  // buy token notification
  TOKEN_BUY,
  // sell token notification
  TOKEN_SELL
}

interface TradingMessage {
  type: TradingMessageType
  token: string
  data: any
}

interface ConnectionContext {
  token: string
  lastSeen: number
}

interface TokenPriceData {
  timestamp?: number // Timestamp of the price data
  priceLamports?: number // Price in lamports
  priceUSD?: number // Price in USD
  priceSol?: number // Price in SOL
  mcap?: number // Current market cap in USD
  currentSolPrice?: number // Current SOL price in USD
}

export interface TokenInfo {
  address: string // Token address
  curveAdress?: string // Curve address for the token
  name?: string // Token name
  symbol?: string // Token symbol
  decimals?: number // Number of decimals
  currentPrice?: TokenPriceData
  targetMcap: number // Target market cap in USD
  volume24h?: number // 24-hour trading volume in USD
  circulatingSupply?: number // Circulating supply of the token
  totalSupply?: number // Total supply of the token should be $1_000_000_000
  solReserves?: number // SOL reserves for bonding curve
  tokenReserves?: number // Token reserves for bonding curve
}

class TradingService {
  // since helius doesn't support multiple connections per address
  private wsConnection: WebSocket | null = null
  private connectedClients: Map<Peer, ConnectionContext> = new Map()
  public static instance: TradingService | null = null
  private program: Program<Deplon> | null = null

  constructor(private readonly helius: Helius) {
    this.initProgram().then(() => {
      this.initProgramListener()
    })
  }

  public getTokenPriceLamports(curve: any) {
    // curve.lastPrice is price per smallest token unit (with 9 decimals)
    // To get price per full token in lamports, multiply by 10^9
    return curve.lastPrice * 1e9
  }

  public solToUsd(sol: number): number {
    const solPrice = getLastCachedPrice()
    if (solPrice) {
      return solPrice * sol
    }
    return 0
  }

  public lamportsToUsd(lamports: number): number {
    const lamportsInSol = lamports / 1e9 // Convert lamports to SOL
    const solPrice = getLastCachedPrice()
    if (solPrice) {
      return solPrice * lamportsInSol // Convert SOL to USD
    }

    return 0
  }

  // general token info
  public async getTokenInfo(address: string) {
    const curve = await this.getTokenCurveAccount(address)
    if (!curve) {
      throw new Error(`No curve found for token: ${address}`)
    }

    const token = await getTokenByAddress(address)

    if (!token) {
      throw new Error(`No token found for address: ${address}`)
    }

    const totalSupply = new BN('**********').mul(new BN(1e9)) // 1B tokens with 9 decimals
    const initialTokenReserves = new BN('************0000000') // 1B initial tokens with 9 decimals
    const currentTokenReserves = new BN(
      curve.tokenReserves?.toString() || '************'
    ) // Convert to BN for precision
    const circulatingSupply = initialTokenReserves.sub(currentTokenReserves)

    const priceLamports = this.getTokenPriceLamports(curve)
    const priceSol = priceLamports / 1e9 // Convert lamports to SOL
    const priceUSD = this.solToUsd(priceSol)

    const tokenInfo: TokenInfo = {
      address: token.address,
      name: token.name,
      symbol: token.symbol,
      decimals: 9,
      currentPrice: {
        priceLamports: priceLamports,
        priceUSD: priceUSD,
        priceSol: priceSol,
        mcap: 0, // TODO: calculate real market cap
        currentSolPrice: getLastCachedPrice()
      },
      // TODO: create a mcap target service to update target dynamically
      targetMcap: 32, // convert lamports to SOL
      volume24h: 0,
      circulatingSupply: parseFloat(circulatingSupply.toString()) / 1e9, // convert to SOL
      totalSupply: parseFloat(totalSupply.toString()) / 1e9, // convert
      // Add reserves data for bonding curve simulation (keep in lamports for precision)
      solReserves: parseFloat(curve.solReserves?.toString() || '0'), // keep as lamports
      tokenReserves: parseFloat(curve.tokenReserves?.toString() || '0') // keep as base units
    }

    return tokenInfo
  }

  public async getTokenCurveAccount(token: string) {
    if (!this.program) {
      throw new Error('Program not initialized')
    }

    const mintPubkey = new PublicKey(token)
    const [tokenCurveAccount] = PublicKey.findProgramAddressSync(
      [Buffer.from('curve'), mintPubkey.toBuffer()],
      this.program.programId
    )
    const data =
      await this.program.account.tokenCurveAccount.fetchNullable(
        tokenCurveAccount
      )
    return {
      curveAddress: tokenCurveAccount.toString(),
      ...data
    }
  }

  private async createDbTransaction(
    sig: string,
    type: 'BUY' | 'SELL',
    user: User,
    token: Token,
    tokenAmount: string | number,
    solAmount: string | number,
    priceBefore: number,
    priceAfter: number,
    solResBefore: string | number,
    solResAfter: string | number
  ) {
    // check isOwner
    const isOwner = false

    const dbTransaction = await prisma.transaction.create({
      data: {
        userId: user.id,
        tokenId: token.id,
        tokenAmount: BigInt(tokenAmount.toString()), // Convert to BigInt
        amountLamports: BigInt(solAmount.toString()), // Convert to BigInt
        transactionHash: sig,
        type: type,
        isOwner: isOwner,
        isBot: false, // TODO: bot database
        priceBefore: priceBefore,
        priceAfter: priceAfter,
        solResBefore: BigInt(solResBefore.toString()), // Convert to BigInt
        solResAfter: BigInt(solResAfter.toString()) // Convert to BigInt
      }
    })

    return dbTransaction
  }

  // listen on the program logs for transactions
  private initProgramListener() {
    if (!this.program) {
      throw new Error('Program not initialized')
    }

    this.program.addEventListener('buy', async (event, slot, signature) => {
      // Check if this transaction has already been recorded FIRST
      const existingTransaction = await prisma.transaction.findUnique({
        where: { transactionHash: signature }
      })

      if (existingTransaction) {
        return
      }

      // to do create transaction and broadcast to clients
      const transaction =
        await this.program?.provider.connection.getTransaction(signature, {
          commitment: 'finalized'
        })

      if (!transaction) {
        // transaction not found or not confirmed yet
        return
      }

      // get user and token info
      const transactionAddress = event.buyer.toString()
      const user = await getUserByAddress(transactionAddress)
      const token = await getTokenByAddress(event.mint.toString())

      if (!user || !token) {
        return
      }

      // Create a new transaction in the database
      const dbTransaction = await this.createDbTransaction(
        signature,
        'BUY',
        user,
        token,
        event.tokenAmount.toString(), // Convert to string to avoid overflow
        event.solCost.toString(), // SOL amount in lamports as string
        event.priceBefore,
        event.priceAfter,
        event.solResBefore.toString(),
        event.solResAfter.toString()
      )

      // Broadcast fresh token info to all clients after buy
      await this.broadcastTokenInfo(event.mint.toString())
    })

    this.program.addEventListener('sell', async (event, slot, signature) => {
      // Check if this transaction has already been recorded FIRST
      const existingTransaction = await prisma.transaction.findUnique({
        where: { transactionHash: signature }
      })

      if (existingTransaction) {
        return
      }

      // to do create transaction and broadcast to clients
      const transaction =
        await this.program?.provider.connection.getTransaction(signature, {
          commitment: 'finalized'
        })

      if (!transaction) {
        // transaction not found or not confirmed yet
        return
      }

      // get user and token info
      const transactionAddress = event.seller.toString()
      const user = await getUserByAddress(transactionAddress)
      const token = await getTokenByAddress(event.mint.toString())

      if (!user || !token) {
        return
      }

      // Create a new transaction in the database
      await this.createDbTransaction(
        signature,
        'SELL',
        user,
        token,
        event.tokenAmount.toString(), // Convert to string to avoid overflow
        event.solAmount.toString(), // SOL amount in lamports as string
        event.priceBefore,
        event.priceAfter,
        event.solResBefore.toString(),
        event.solResAfter.toString()
      )

      // Broadcast fresh token info to all clients after sell
      await this.broadcastTokenInfo(event.mint.toString())
    })
  }

  // connections
  public onConnect(peer: Peer, address?: string) {
    // upon connection send price data immediately
    this.connectedClients.set(peer, {
      token: address ?? '',
      lastSeen: Date.now()
    })

    this.sendMessage(peer, {
      type: TradingMessageType.CONNECTED,
      token: address || '',
      data: {
        message: 'Welcome to the trading service!'
      }
    })
  }

  public onDisconnect(peer: Peer) {}
  public async onMessage(peer: Peer, message: TradingMessage) {
    if (this.connectedClients.has(peer)) {
      const context = this.connectedClients.get(peer)
      if (context) {
        context.lastSeen = Date.now()
      }
    } else {
      // user not subscribed to this token
      return
    }

    // Handle different message types
    switch (message.type) {
      case TradingMessageType.TOKEN_PRICE:
        // Handle token price updates
        break
      case TradingMessageType.TOKEN_INFO:
        {
          const tokenInfo = await this.getTokenInfo(message.token)
          this.sendMessage(peer, {
            type: TradingMessageType.TOKEN_INFO,
            token: message.token,
            data: tokenInfo
          })
        }
        break
      case TradingMessageType.TOKEN_BUY:
        // Broadcast fresh token info to all clients after buy
        await this.broadcastTokenInfo(message.token)
        break
      case TradingMessageType.TOKEN_SELL:
        // Broadcast fresh token info to all clients after sell
        await this.broadcastTokenInfo(message.token)
        break
      case TradingMessageType.TOKEN_ACTIVITY:
        // Handle token activity updates
        break
      default:
        console.warn(`Unknown message type: ${message.type}`)
    }
  }

  public broadcastMessage(topic: string, data: unknown) {}

  // Broadcast fresh token info to all connected clients for a specific token
  public async broadcastTokenInfo(tokenAddress: string) {
    try {
      const tokenInfo = await this.getTokenInfo(tokenAddress)

      // Send updated token info to all connected clients for this token
      for (const [peer, context] of this.connectedClients.entries()) {
        if (context.token === tokenAddress) {
          this.sendMessage(peer, {
            type: TradingMessageType.TOKEN_INFO,
            token: tokenAddress,
            data: tokenInfo
          })
        }
      }
    } catch (error) {
      console.error(
        `Failed to broadcast token info for ${tokenAddress}:`,
        error
      )
    }
  }

  public sendMessage(peer: Peer, message: TradingMessage) {
    if (this.connectedClients.has(peer)) {
      peer.send(JSON.stringify(message))
    } else {
      console.warn(
        `Peer ${peer.id} is not connected or not subscribed to the topic.`
      )
    }
  }

  private async initProgram() {
    this.program = await initializeProgram()
  }
}

// singleton trading service
export function useTradingService() {
  if (!TradingService.instance) {
    const helius = new Helius(process.env.HELIUS_API_KEY || '')
    TradingService.instance = new TradingService(helius)
  }
  return TradingService.instance
}
