// post interactions handling logic
// users on frontend may like or interact with posts in various ways
// this file will handle those interactions and update the state accordingly
// in realtime for both the connected clients and server
// eg. liking a post, commenting on a post, sharing a post, etc.
// we need to ensure that these interactions are handled efficiently
// and that the state is updated in a way that reflects the latest changes
// the frontend need to use WebSockets to load the latest interactions for the posts that are already in view or relevant ones such as the current post open or all profile posts
// this file will also handle the WebSocket connection for post interactions

import prisma from '~/lib/prisma'
import type { InteractionType } from '@prisma/client'
import { sendNotification } from '~/server/api/ws/notifications'

export interface PostInteractionData {
  postId: number
  userId: number
  type: InteractionType
  content?: string // For comments
}

export interface PostInteractionResponse {
  success: boolean
  message: string
  data?: {
    liked: boolean
    likeCount: number
  }
  error?: string
}

export class PostInteractionsWs {
  private connectedClients: Map<string, any> = new Map()

  addClient (clientId: string, client: any) {
    this.connectedClients.set(clientId, client)
  }

  removeClient (clientId: string) {
    this.connectedClients.delete(clientId)
  }

  // Broadcast to all connected clients
  broadcast (event: string, data: any) {
    this.connectedClients.forEach(client => {
      try {
        client.send(JSON.stringify({ event, data }))
      } catch (error) {
        console.error('Failed to send message to client:', error)
      }
    })
  }

  // Broadcast to specific clients (e.g., followers of a user)
  broadcastToUsers (userIds: number[], event: string, data: any) {
    // Implementation would depend on how you track user->client mapping
    this.broadcast(event, data) // For now, broadcast to all
  }

  // Handle post like interaction
  async onPostLike (
    postId: number,
    userId: number
  ): Promise<PostInteractionResponse> {
    try {
      // Check if user already liked the post
      const existingLike = await prisma.postLike.findUnique({
        where: {
          postId_userId: {
            postId,
            userId
          }
        }
      })

      if (existingLike) {
        // Unlike the post
        await prisma.postLike.delete({
          where: {
            postId_userId: {
              postId,
              userId
            }
          }
        })

        // Decrease like count
        const updatedPost = await prisma.post.update({
          where: { id: postId },
          data: {
            likeCount: {
              decrement: 1
            }
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                address: true
              }
            }
          }
        })

        // Record interaction for analytics
        await prisma.interaction.create({
          data: {
            userId,
            postId,
            type: 'LIKE'
          }
        })

        // Broadcast unlike event
        this.broadcast('post:unliked', {
          postId,
          userId,
          likeCount: updatedPost.likeCount
        })

        return {
          success: true,
          message: 'Post unliked successfully',
          data: {
            liked: false,
            likeCount: updatedPost.likeCount
          }
        }
      } else {
        // Like the post
        await prisma.postLike.create({
          data: {
            postId,
            userId
          }
        })

        // Increase like count
        const updatedPost = await prisma.post.update({
          where: { id: postId },
          data: {
            likeCount: {
              increment: 1
            }
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                address: true
              }
            }
          }
        })

        // Record interaction for analytics
        await prisma.interaction.create({
          data: {
            userId,
            postId,
            type: 'LIKE'
          }
        })

        // Broadcast like event
        this.broadcast('post:liked', {
          postId,
          userId,
          likeCount: updatedPost.likeCount
        })

        // Send notification to post author (if not self-like)
        if (updatedPost.userId !== userId) {
          const liker = await prisma.user.findUnique({
            where: { id: userId },
            select: { name: true }
          })

          await sendNotification({
            sourceUserId: userId,
            targetUserId: updatedPost.userId,
            type: 'LIKE',
            content: 'liked your post',
            postId: postId
          })
        }

        return {
          success: true,
          message: 'Post liked successfully',
          data: {
            liked: true,
            likeCount: updatedPost.likeCount
          }
        }
      }
    } catch (error) {
      console.error('Error in onPostLike:', error)
      return {
        success: false,
        message: 'Failed to like/unlike post',
        error: error instanceof Error ? error.message : 'Unknown error',
        data: {}
      }
    }
  }

  // Handle post reply interaction
  async onPostReply (
    postId: number,
    userId: number,
    content: string
  ): Promise<PostInteractionResponse> {
    try {
      if (!content || content.trim().length === 0) {
        return {
          success: false,
          message: 'Reply content cannot be empty',
          error: 'Empty content'
        }
      }

      // Create the reply as a new post
      const reply = await prisma.post.create({
        data: {
          userId,
          content: content.trim(),
          replyToId: postId
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              address: true
            }
          }
        }
      })

      // Increase reply count on the parent post
      const updatedPost = await prisma.post.update({
        where: { id: postId },
        data: {
          replyCount: {
            increment: 1
          }
        },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              address: true
            }
          }
        }
      })

      // Record interaction for analytics
      await prisma.interaction.create({
        data: {
          userId,
          postId,
          type: 'REPLY'
        }
      })

      // Broadcast reply event
      this.broadcast('post:replied', {
        postId,
        reply,
        replyCount: updatedPost.replyCount
      })

      // Send notification to post author (if not self-reply)
      if (updatedPost.userId !== userId) {
        const replier = await prisma.user.findUnique({
          where: { id: userId },
          select: { name: true }
        })

        await sendNotification({
          sourceUserId: userId,
          targetUserId: updatedPost.userId,
          type: 'COMMENT',
          content: 'replied to your post',
          postId: postId
        })
      }

      return {
        success: true,
        message: 'Reply added successfully',
        data: {
          reply,
          replyCount: updatedPost.replyCount
        }
      }
    } catch (error) {
      console.error('Error in onPostReply:', error)
      return {
        success: false,
        message: 'Failed to add reply',
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  // Handle post share interaction
  async onPostShare (
    postId: number,
    userId: number
  ): Promise<PostInteractionResponse> {
    try {
      // Check if user already shared the post
      const existingShare = await prisma.postShare.findUnique({
        where: {
          postId_userId: {
            postId,
            userId
          }
        }
      })

      if (existingShare) {
        // Unshare the post
        await prisma.postShare.delete({
          where: {
            postId_userId: {
              postId,
              userId
            }
          }
        })

        // Decrease share count
        const updatedPost = await prisma.post.update({
          where: { id: postId },
          data: {
            shareCount: {
              decrement: 1
            }
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                address: true
              }
            }
          }
        })

        // Broadcast unshare event
        this.broadcast('post:unshared', {
          postId,
          userId,
          shareCount: updatedPost.shareCount
        })

        return {
          success: true,
          message: 'Post unshared successfully',
          data: {
            shared: false,
            shareCount: updatedPost.shareCount
          }
        }
      } else {
        // Share the post
        await prisma.postShare.create({
          data: {
            postId,
            userId
          }
        })

        // Increase share count
        const updatedPost = await prisma.post.update({
          where: { id: postId },
          data: {
            shareCount: {
              increment: 1
            }
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                address: true
              }
            }
          }
        })

        // Record interaction for analytics
        await prisma.interaction.create({
          data: {
            userId,
            postId,
            type: 'SHARE'
          }
        })

        // Broadcast share event
        this.broadcast('post:shared', {
          postId,
          userId,
          shareCount: updatedPost.shareCount
        })

        // Send notification to post author (if not self-share)
        if (updatedPost.userId !== userId) {
          const sharer = await prisma.user.findUnique({
            where: { id: userId },
            select: { name: true }
          })

          await sendNotification({
            sourceUserId: userId,
            targetUserId: updatedPost.userId,
            type: 'SHARE',
            content: 'shared your post',
            postId: postId
          })
        }

        return {
          success: true,
          message: 'Post shared successfully',
          data: {
            shared: true,
            shareCount: updatedPost.shareCount
          }
        }
      }
    } catch (error) {
      console.error('Error in onPostShare:', error)
      return {
        success: false,
        message: 'Failed to share/unshare post',
        error: error instanceof Error ? error.message : 'Unknown error',
        data: {}
      }
    }
  }

  // Get post interaction status for a user
  async getPostInteractionStatus (postId: number, userId: number | null) {
    try {
      const post = await prisma.post.findUnique({
        where: { id: postId },
        select: {
          likeCount: true,
          replyCount: true,
          shareCount: true
        }
      })

      // Always return public counts
      const result = {
        liked: false,
        shared: false,
        likeCount: post?.likeCount || 0,
        replyCount: post?.replyCount || 0,
        shareCount: post?.shareCount || 0
      }

      // If user is authenticated, also check their personal interaction status
      if (userId) {
        const [liked, shared] = await Promise.all([
          prisma.postLike.findUnique({
            where: {
              postId_userId: {
                postId,
                userId
              }
            }
          }),
          prisma.postShare.findUnique({
            where: {
              postId_userId: {
                postId,
                userId
              }
            }
          })
        ])

        result.liked = !!liked
        result.shared = !!shared
      }

      return result
    } catch (error) {
      console.error('Error getting post interaction status:', error)
      return {
        liked: false,
        shared: false,
        likeCount: 0,
        replyCount: 0,
        shareCount: 0
      }
    }
  }

  // Get interaction status for multiple posts (batch)
  async getBatchPostInteractionStatus (
    postIds: number[],
    userId: number | null
  ) {
    try {
      // Get all posts with their counts
      const posts = await prisma.post.findMany({
        where: {
          id: {
            in: postIds
          }
        },
        select: {
          id: true,
          likeCount: true,
          replyCount: true,
          shareCount: true
        }
      })

      const result: Record<number, any> = {}

      // Initialize all requested posts with default values
      postIds.forEach(postId => {
        const post = posts.find(p => p.id === postId)
        result[postId] = {
          liked: false,
          shared: false,
          likeCount: post?.likeCount || 0,
          replyCount: post?.replyCount || 0,
          shareCount: post?.shareCount || 0
        }
      })

      // If user is authenticated, check their personal interaction status
      if (userId) {
        const [likes, shares] = await Promise.all([
          prisma.postLike.findMany({
            where: {
              postId: { in: postIds },
              userId
            },
            select: { postId: true }
          }),
          prisma.postShare.findMany({
            where: {
              postId: { in: postIds },
              userId
            },
            select: { postId: true }
          })
        ])

        // Update liked status
        likes.forEach(like => {
          if (result[like.postId]) {
            result[like.postId].liked = true
          }
        })

        // Update shared status
        shares.forEach(share => {
          if (result[share.postId]) {
            result[share.postId].shared = true
          }
        })
      }

      return result
    } catch (error) {
      console.error('Error getting batch post interaction status:', error)
      // Return default values for all requested posts
      const result: Record<number, any> = {}
      postIds.forEach(postId => {
        result[postId] = {
          liked: false,
          shared: false,
          likeCount: 0,
          replyCount: 0,
          shareCount: 0
        }
      })
      return result
    }
  }

  // Get replies for a post
  async getPostReplies (postId: number, limit: number = 10, offset: number = 0) {
    try {
      const replies = await prisma.post.findMany({
        where: { replyToId: postId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              address: true
            }
          },
          _count: {
            select: {
              likes: true,
              replies: true,
              shares: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        },
        skip: offset,
        take: limit
      })

      return replies.map(reply => ({
        ...reply,
        likeCount: reply._count.likes,
        replyCount: reply._count.replies,
        shareCount: reply._count.shares
      }))
    } catch (error) {
      console.error('Error getting post replies:', error)
      return []
    }
  }
}

export class PostInteractionsService {
  private wsHandler: PostInteractionsWs

  constructor () {
    this.wsHandler = new PostInteractionsWs()
  }

  getWsHandler () {
    return this.wsHandler
  }

  // Like/Unlike a post
  async toggleLike (postId: number, userId: number) {
    return await this.wsHandler.onPostLike(postId, userId)
  }

  // Add a reply to a post
  async addReply (postId: number, userId: number, content: string) {
    return await this.wsHandler.onPostReply(postId, userId, content)
  }

  // Share/Unshare a post
  async toggleShare (postId: number, userId: number) {
    return await this.wsHandler.onPostShare(postId, userId)
  }

  // Get interaction status for a post
  async getInteractionStatus (postId: number, userId: number | null) {
    return await this.wsHandler.getPostInteractionStatus(postId, userId)
  }

  // Get interaction status for multiple posts (batch)
  async getBatchInteractionStatus (postIds: number[], userId: number | null) {
    return await this.wsHandler.getBatchPostInteractionStatus(postIds, userId)
  }

  // Get replies for a post
  async getReplies (postId: number, limit?: number, offset?: number) {
    return await this.wsHandler.getPostReplies(postId, limit, offset)
  }
}

// Export singleton instance
export const postInteractionsService = new PostInteractionsService()
