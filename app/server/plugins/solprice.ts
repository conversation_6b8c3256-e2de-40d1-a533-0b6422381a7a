import { fetchFresh } from '../utils/solprice'

// This plugin fetches the latest Solana price every minute
// and caches it for use in the application

export default defineNitroPlugin(nitroApp => {
  fetchFresh()
  const interval = setInterval(
    () => {
      fetchFresh()
    },
    1000 * 60 * 0.5 // every half a minute
  )
  nitroApp.hooks.hookOnce('close', () => {
    clearInterval(interval)
  })
})
