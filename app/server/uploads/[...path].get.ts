export default defineEventHandler(async event => {
  const path = getRouterParam(event, 'path')

  if (!path) {
    throw createError({
      statusCode: 404,
      statusMessage: 'File not found'
    })
  }

  try {
    // Get the storage instance
    const storage = useStorage('uploads')

    // Get the file from storage
    const file = await storage.getItemRaw(path)

    if (!file) {
      throw createError({
        statusCode: 404,
        statusMessage: 'File not found'
      })
    }

    // Determine content type based on file extension
    const extension = path.split('.').pop()?.toLowerCase()
    let contentType = 'application/octet-stream'

    switch (extension) {
      case 'jpg':
      case 'jpeg':
        contentType = 'image/jpeg'
        break
      case 'png':
        contentType = 'image/png'
        break
      case 'webp':
        contentType = 'image/webp'
        break
      case 'gif':
        contentType = 'image/gif'
        break
      case 'svg':
        contentType = 'image/svg+xml'
        break
    }

    // Set appropriate headers
    setHeader(event, 'Content-Type', contentType)
    setHeader(event, 'Cache-Control', 'public, max-age=31536000') // Cache for 1 year

    // Return the file buffer
    return file
  } catch (error: any) {
    console.error('Error serving upload file:', error)
    throw createError({
      statusCode: 404,
      statusMessage: 'File not found'
    })
  }
})
