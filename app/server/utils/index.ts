import crypto from 'crypto'
import type { Cluster } from '@solana/web3.js'
import {
  clusterApiUrl,
  Connection,
  Keypair,
  LAMPORTS_PER_SOL
} from '@solana/web3.js'
import { readFileSync } from 'fs'
import { homedir } from 'os'
import path from 'path'

export const validateCode = (code: string): boolean => {
  // TODO: Implement code validation
  return true
}

// Generate a random 6-digit code
export const generateCode = (): string => {
  return crypto.randomInt(100000, 999999).toString()
}

export const generateSessionToken = (): string => {
  return crypto.randomBytes(16).toString('hex')
}

export async function loadKeypairFromFile (filePath: string): Promise<Keypair> {
  const resolvedPath = path.resolve(
    filePath.startsWith('~') ? filePath.replace('~', homedir()) : filePath
  )
  const loadedKeyBytes = Uint8Array.from(
    JSON.parse(readFileSync(resolvedPath, 'utf8'))
  )
  return await Keypair.fromSecretKey(loadedKeyBytes)
}

export async function loadDefaultKeypair (): Promise<Keypair> {
  return await loadKeypairFromFile('~/.config/solana/id.json')
}

export async function loadDefaultKeypairWithAirdrop (
  cluster: Cluster
): Promise<Keypair> {
  const keypair = await loadDefaultKeypair()
  const connection = new Connection(clusterApiUrl(cluster), 'finalized')

  try {
    const balance = await connection.getBalance(keypair.publicKey)

    // 1 LAMPORTS_PER_SOL === 1 SOL
    console.log(`Balance: ${balance} lamports`)
    if (balance < 0.05 * LAMPORTS_PER_SOL) {
      console.log('Balance low requesting airdrop')
      await connection.requestAirdrop(keypair.publicKey, 1_000_000_000)
    }
  } catch (err) {
    console.error('Error fetching balance:', err)
  }
  return keypair
}
