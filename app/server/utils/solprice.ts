// last 3 cached prices
// with the most recent one
const lastCachedPrices = new Map<Date, number>()

export function getCachedPrice (date: Date): number | undefined {
  return lastCachedPrices.get(date)
}

export function getLastCachedPrice (): number | undefined {
  const lastDate = Array.from(lastCachedPrices.keys()).sort(
    (a, b) => b.getTime() - a.getTime()
  )[0]
  return lastDate ? lastCachedPrices.get(lastDate) : undefined
}

// https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd
export function fetchFresh () {
  return $fetch<{
    So11111111111111111111111111111111111111112?: { usdPrice: number }
  }>(
    'https://lite-api.jup.ag/price/v3?ids=So11111111111111111111111111111111111111112'
  )
    .then(data => {
      const price = data.So11111111111111111111111111111111111111112?.usdPrice
      if (price) {
        const now = new Date()
        lastCachedPrices.set(now, price)
        // Keep only the last 3 prices
        if (lastCachedPrices.size > 3) {
          const oldestDate = Array.from(lastCachedPrices.keys()).sort(
            (a, b) => a.getTime() - b.getTime()
          )[0]
          lastCachedPrices.delete(oldestDate)
        }
      }
      return price
    })
    .catch(error => {
      console.error('Error fetching Solana price:', error)
      throw error
    })
}
