import {
  validateImageFile,
  generateFileName,
  deleteStorageFile
} from './storage'
import prisma from '~/lib/prisma'

export interface MediaUploadResult {
  url: string
  type: string
  mimeType: string
  width?: number
  height?: number
  size: number
}

export async function uploadPostMedia (
  files: File[],
  userId: number,
  postId?: number
): Promise<MediaUploadResult[]> {
  const storage = useStorage('uploads')
  const results: MediaUploadResult[] = []

  for (let i = 0; i < files.length; i++) {
    const file = files[i]

    // Validate file (max 10MB for posts)
    validateImageFile(file, 10 * 1024 * 1024)

    const fileExtension = file.name.split('.').pop() || 'jpg'
    const fileName = generateFileName(
      userId,
      `post_${postId || 'temp'}_${i}`,
      fileExtension
    )

    try {
      const buffer = Buffer.from(await file.arrayBuffer())

      // Get image dimensions if it's an image
      let width: number | undefined
      let height: number | undefined

      if (file.type.startsWith('image/')) {
        // For now, we'll skip dimension detection to keep it simple
        // You can add image processing library later if needed
      }

      await storage.setItemRaw(fileName, buffer)

      results.push({
        url: `/uploads/${fileName}`,
        type: 'image',
        mimeType: file.type,
        width,
        height,
        size: buffer.length
      })
    } catch (error) {
      console.error('Failed to upload media file:', error)
      throw createError({
        statusCode: 500,
        statusMessage: `Failed to upload media file: ${file.name}`
      })
    }
  }

  return results
}

export async function savePostMediaToDatabase (
  postId: number,
  mediaResults: MediaUploadResult[]
): Promise<void> {
  const mediaData = mediaResults.map((media, index) => ({
    postId,
    url: media.url,
    type: media.type,
    mimeType: media.mimeType,
    width: media.width,
    height: media.height,
    size: media.size,
    order: index
  }))

  await prisma.postMedia.createMany({
    data: mediaData
  })
}

export async function deletePostMedia (postId: number): Promise<void> {
  // Get all media for the post
  const mediaFiles = await prisma.postMedia.findMany({
    where: { postId },
    select: { url: true }
  })

  // Delete files from storage
  for (const media of mediaFiles) {
    await deleteStorageFile(media.url)
  }

  // Delete database records
  await prisma.postMedia.deleteMany({
    where: { postId }
  })
}

export function getImageGridLayout (imageCount: number) {
  if (imageCount === 1) {
    return {
      containerClass: 'grid grid-cols-1 gap-1 rounded-lg overflow-hidden',
      imageClass: 'w-full h-auto max-h-96 object-cover',
      showOverlay: false
    }
  } else if (imageCount === 2) {
    return {
      containerClass: 'grid grid-cols-2 gap-1 rounded-lg overflow-hidden',
      imageClass: 'w-full h-48 object-cover',
      showOverlay: false
    }
  } else if (imageCount === 3) {
    return {
      containerClass: 'grid grid-cols-2 gap-1 rounded-lg overflow-hidden',
      imageClass: (index: number) =>
        index === 0
          ? 'w-full h-96 object-cover col-span-1'
          : 'w-full h-47 object-cover',
      showOverlay: false
    }
  } else if (imageCount === 4) {
    return {
      containerClass: 'grid grid-cols-2 gap-1 rounded-lg overflow-hidden',
      imageClass: 'w-full h-48 object-cover',
      showOverlay: false
    }
  } else {
    // 5+ images: show first 3 and "+X more" overlay on 4th
    return {
      containerClass: 'grid grid-cols-2 gap-1 rounded-lg overflow-hidden',
      imageClass: (index: number) =>
        index === 0
          ? 'w-full h-96 object-cover col-span-1'
          : 'w-full h-47 object-cover',
      showOverlay: true,
      overlayCount: imageCount - 3
    }
  }
}
