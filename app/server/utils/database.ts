import prisma from '~/lib/prisma'

export const createPost = async (content: string, userId: number) => {
  const post = await prisma.post.create({
    data: {
      content,
      userId
    }
  })

  return post
}

export const createHashtag = async (name: string) => {
  const hashtag = await prisma.hashtag.create({
    data: {
      name
    }
  })

  return hashtag
}

export const createMention = async (
  postId: number,
  userId?: number,
  hashtagId?: number,
  tokenId?: number
) => {
  const mention = await prisma.mention.create({
    data: {
      postId,
      userId,
      hashtagId,
      tokenId
    }
  })

  return mention
}

// fetch a post with all it's related data such as replies and authors
export const getPostByIdExpanded = async (id: number) => {
  const post = await prisma.post.findUnique({
    where: { id },
    include: {
      user: true,
      media: {
        orderBy: { order: 'asc' }
      },
      mentions: {
        include: {
          user: true,
          hashtag: true,
          token: true
        }
      }
    }
  })

  return post
}

export const getPostRepliesByPostId = async (postId: number) => {
  const replies = await prisma.post.findMany({
    where: { replyToId: postId },
    include: {
      user: true,
      media: {
        orderBy: { order: 'asc' }
      },
      mentions: {
        include: {
          user: true,
          hashtag: true,
          token: true
        }
      }
    }
  })

  return replies
}
