import { BN } from 'bn.js'
import { PublicKey } from '@solana/web3.js'
import { initializeProgram } from '~/server/services/web3'

/**
 * INTERNAL ONLY: Update target market cap on-chain
 * This function should NEVER be exposed as an API endpoint
 * Only called from server-side business logic
 */
export async function updateTargetMarketCapInternal(
  tokenAddress: string,
  newTargetSol: number,
  reason: string
): Promise<{ success: boolean; transaction?: string; error?: string }> {
  try {
    // SECURITY: Validate target doesn't exceed smart contract limit
    const MAX_TARGET_SOL = 10000
    if (newTargetSol > MAX_TARGET_SOL) {
      throw new Error(
        `Target market cap cannot exceed ${MAX_TARGET_SOL} SOL (smart contract limit)`
      )
    }

    // BUSINESS LOGIC: Validate minimum target for profitability
    const MIN_TARGET_SOL = 50
    if (newTargetSol < MIN_TARGET_SOL) {
      throw new Error(
        `Target market cap must be at least ${MIN_TARGET_SOL} SOL for profitability`
      )
    }

    // Initialize the Anchor program
    const program = await initializeProgram()

    // Convert addresses to PublicKey objects
    const mintPubkey = new PublicKey(tokenAddress)

    // Get the curve account PDA
    const [tokenCurveAccount] = PublicKey.findProgramAddressSync(
      [Buffer.from('curve'), mintPubkey.toBuffer()],
      program.programId
    )

    // Convert new target to lamports
    const newTargetLamports = new BN(newTargetSol * 1e9)

    // Use platform authority keypair
    const platformAuthority = program.provider.wallet

    if (!platformAuthority) {
      throw new Error('Platform authority not configured')
    }

    try {
      // Call the update_target_market_cap instruction
      const tx = await program.methods
        .updateTargetMarketCap(newTargetLamports)
        .accountsStrict({
          platformAuthority: platformAuthority.publicKey,
          mint: mintPubkey,
          tokenCurveAccount: tokenCurveAccount
        })
        .rpc()

      return {
        success: true,
        transaction: tx
      }
    } catch (updateError: any) {
      console.error(
        '❌ Error updating target market cap on-chain:',
        updateError
      )

      return {
        success: false,
        error: updateError.message
      }
    }
  } catch (error: any) {
    console.error('❌ Error in internal target update:', error)
    return {
      success: false,
      error: error.message
    }
  }
}
