/**
 * Storage utilities for handling file uploads and deletions
 * This abstraction allows easy switching between file system and cloud storage
 */

/**
 * Delete a file from storage
 * @param filePath - The path to the file (without /uploads prefix)
 */
export async function deleteStorageFile (filePath: string): Promise<void> {
  const storage = useStorage('uploads')

  try {
    // Remove the /uploads prefix if present
    const cleanPath = filePath.startsWith('/uploads/')
      ? filePath.substring('/uploads/'.length)
      : filePath

    await storage.removeItem(cleanPath)
  } catch (error) {
    console.error('Failed to delete file from storage:', error)
    // Don't throw error - file deletion is not critical
  }
}

/**
 * Generate a unique filename with timestamp
 * @param userId - The user ID
 * @param type - The type of file (profile, cover, etc.)
 * @param extension - The file extension
 */
export function generateFileName (
  userId: number,
  type: string,
  extension: string
): string {
  return `users/${userId}/${type}-${Date.now()}.${extension}`
}

/**
 * Validate image file type and size
 * @param file - The file to validate
 * @param maxSizeBytes - Maximum file size in bytes
 */
export function validateImageFile (file: File, maxSizeBytes: number): void {
  if (!file.type.startsWith('image/')) {
    throw createError({
      statusCode: 400,
      statusMessage: 'File must be an image'
    })
  }

  if (file.size > maxSizeBytes) {
    const maxSizeMB = Math.round(maxSizeBytes / (1024 * 1024))
    throw createError({
      statusCode: 400,
      statusMessage: `File must be smaller than ${maxSizeMB}MB`
    })
  }
}
