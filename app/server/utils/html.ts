export const clearHtmlTags = (content: string) => {
  return content
    .replace(/<[^>]*>/g, '')
    .replace(/&nbsp;/g, ' ')
    .trim()
}

export const cleanPostContent = (content: string) => {
  // Remove HTML tags and extra spaces
  let cleanedContent = clearHtmlTags(content)

  // Replace multiple spaces with a single space
  cleanedContent = cleanedContent.replace(/\s+/g, ' ')

  // Trim leading and trailing spaces
  return cleanedContent.trim()
}
