import prisma from '~/lib/prisma'

interface RecommendationWeights {
  like: number
  reply: number
  share: number
  view: number
  mention: number
  bookmark: number
  recency: number
  popularity: number
}

const DEFAULT_WEIGHTS: RecommendationWeights = {
  like: 3,
  reply: 5,
  share: 4,
  view: 1,
  mention: 2,
  bookmark: 6,
  recency: 2,
  popularity: 1.5
}

export class RecommendationService {
  private weights: RecommendationWeights

  constructor (weights?: Partial<RecommendationWeights>) {
    this.weights = { ...DEFAULT_WEIGHTS, ...weights }
  }

  async getRecommendedPosts (userId: number, limit = 10) {
    // Get user's interaction history to understand preferences
    const userInteractions = await prisma.interaction.findMany({
      where: { userId },
      select: {
        type: true,
        postId: true,
        tokenId: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' },
      take: 500 // Last 500 interactions
    })

    // Get posts with scores
    const posts = await prisma.post.findMany({
      where: {
        userId: { not: userId }, // Exclude own posts
        replyToId: null // Only original posts
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            displayName: true,
            address: true,
            profilePicture: true
          }
        },
        media: {
          orderBy: { order: 'asc' }
        },
        _count: {
          select: {
            likes: true,
            replies: true,
            shares: true,
            interactions: true
          }
        },
        interactions: {
          where: { userId },
          select: { type: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 100 // Get recent posts to score
    })

    // Calculate scores for each post
    const scoredPosts = posts.map(post => ({
      ...post,
      // author: post.user, // Map user to author for frontend compatibility
      createdAt: post.createdAt.toISOString(), // Ensure consistent date format
      score: this.calculatePostScore(post, userInteractions)
    }))

    // Sort by score and return top results
    return scoredPosts.sort((a, b) => b.score - a.score).slice(0, limit)
  }

  async getRecommendedReplies (userId: number, limit = 10) {
    // Similar to posts but for replies
    const userInteractions = await prisma.interaction.findMany({
      where: { userId },
      select: {
        type: true,
        postId: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' },
      take: 500
    })

    const replies = await prisma.post.findMany({
      where: {
        userId: { not: userId },
        replyToId: { not: null } // Only replies
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            displayName: true,
            address: true,
            profilePicture: true
          }
        },
        media: {
          orderBy: { order: 'asc' }
        },
        replyTo: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                displayName: true,
                address: true,
                profilePicture: true
              }
            },
            media: {
              orderBy: { order: 'asc' }
            }
          }
        },
        _count: {
          select: {
            likes: true,
            replies: true,
            shares: true,
            interactions: true
          }
        },
        interactions: {
          where: { userId },
          select: { type: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 100
    })

    const scoredReplies = replies.map(reply => ({
      reply: {
        id: reply.id,
        content: reply.content,
        authorId: reply.userId,
        createdAt: reply.createdAt.toISOString(),
        media: reply.media,
        user: reply.user,
        stats: {
          likes: reply._count.likes,
          replies: reply._count.replies,
          shares: reply._count.shares
        }
      },
      originalPost: {
        id: reply.replyTo!.id,
        content: reply.replyTo!.content,
        createdAt: reply.replyTo!.createdAt.toISOString(),
        media: reply.replyTo!.media,
        author: {
          id: reply.replyTo!.user.id,
          name:
            reply.replyTo!.user.displayName ||
            reply.replyTo!.user.name ||
            'Anonymous',
          address: reply.replyTo!.user.address,
          profileImage: reply.replyTo!.user.profilePicture || ''
        }
      },
      score: this.calculatePostScore(reply, userInteractions)
    }))

    return scoredReplies.sort((a, b) => b.score - a.score).slice(0, limit)
  }

  async getRecommendedProfiles (userId: number, limit = 3) {
    // Get users the current user interacts with
    const userInteractions = await prisma.interaction.findMany({
      where: { userId },
      include: {
        post: {
          select: { userId: true }
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 500
    })

    // Get users to recommend
    const users = await prisma.user.findMany({
      where: {
        id: { not: userId },
        // Exclude users already followed
        followers: {
          none: { followerId: userId }
        }
      },
      select: {
        id: true,
        name: true,
        displayName: true,
        address: true,
        profilePicture: true,
        _count: {
          select: {
            followers: true,
            posts: true,
            interactions: true
          }
        }
      },
      take: 50
    })

    // Calculate scores for each user
    const scoredUsers = users.map(user => ({
      ...user,
      score: this.calculateUserScore(user, userInteractions)
    }))

    return scoredUsers
      .sort((a, b) => b.score - a.score)
      .slice(0, limit)
      .map(user => ({
        id: user.id,
        name: user.displayName || user.name || 'Anonymous',
        address: user.address,
        profileImage: user.profilePicture || '',
        followers: user._count.followers
      }))
  }

  async getRecommendedTokens (userId: number, limit = 5) {
    // Get user's token interactions
    const userTokenInteractions = await prisma.interaction.findMany({
      where: {
        userId,
        tokenId: { not: null }
      },
      select: {
        type: true,
        tokenId: true,
        createdAt: true
      },
      orderBy: { createdAt: 'desc' },
      take: 200
    })

    // Get tokens to recommend
    const tokens = await prisma.token.findMany({
      where: {
        userId: { not: userId } // Exclude user's own tokens
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            displayName: true,
            address: true
          }
        },
        _count: {
          select: {
            interactions: true,
            transactions: true
          }
        },
        transactions: {
          select: {
            type: true,
            tokenAmount: true,
            amountLamports: true,
            createdAt: true
          },
          orderBy: { createdAt: 'desc' },
          take: 10
        }
      },
      orderBy: { createdAt: 'desc' },
      take: 50
    })

    // Calculate scores for each token
    const scoredTokens = tokens.map(token => ({
      ...token,
      score: this.calculateTokenScore(token, userTokenInteractions)
    }))

    return scoredTokens.sort((a, b) => b.score - a.score).slice(0, limit)
  }

  private calculatePostScore (post: any, userInteractions: any[]): number {
    let score = 0

    // Popularity score
    score += post._count.likes * this.weights.popularity
    score += post._count.replies * this.weights.popularity * 1.5
    score += post._count.shares * this.weights.popularity * 2

    // Recency score (newer posts get higher score)
    const daysSincePosted =
      (Date.now() - new Date(post.createdAt).getTime()) / (1000 * 60 * 60 * 24)
    score += Math.max(0, this.weights.recency * (7 - daysSincePosted))

    // User interaction patterns
    const authorInteractions = userInteractions.filter(
      i => i.postId && post.userId === post.user.id
    )

    authorInteractions.forEach(interaction => {
      switch (interaction.type) {
        case 'LIKE':
          score += this.weights.like
          break
        case 'REPLY':
          score += this.weights.reply
          break
        case 'SHARE':
          score += this.weights.share
          break
        case 'VIEW':
          score += this.weights.view
          break
        case 'MENTION':
          score += this.weights.mention
          break
        case 'BOOKMARK':
          score += this.weights.bookmark
          break
      }
    })

    // Penalize if user already interacted with this specific post
    if (post.interactions.length > 0) {
      score *= 0.3 // Reduce score for already seen content
    }

    return score
  }

  private calculateUserScore (user: any, userInteractions: any[]): number {
    let score = 0

    // User popularity
    score += user._count.followers * 0.1
    score += user._count.posts * 0.05
    score += user._count.interactions * 0.02

    // How much the current user interacts with this user's content
    const interactionsWithUser = userInteractions.filter(
      i => i.post?.userId === user.id
    ).length

    score += interactionsWithUser * 2

    return score
  }

  private calculateTokenScore (
    token: any,
    userTokenInteractions: any[]
  ): number {
    let score = 0

    // Token activity
    score += token._count.interactions * 0.5
    score += token._count.transactions * 1

    // Recent transaction volume
    const recentTransactions = token.transactions.slice(0, 5)
    const totalVolume = recentTransactions.reduce(
      (sum: number, tx: any) => sum + (tx.solAmount || 0),
      0
    )
    score += totalVolume * 0.1

    // User's token interaction patterns
    const similarTokenInteractions = userTokenInteractions.filter(
      i => i.tokenId === token.id
    ).length

    score += similarTokenInteractions * 3

    // Recency
    const daysSinceCreated =
      (Date.now() - new Date(token.createdAt).getTime()) / (1000 * 60 * 60 * 24)
    score += Math.max(0, 2 * (30 - daysSinceCreated))

    return score
  }
}

// Singleton instance
export const recommendationService = new RecommendationService()
