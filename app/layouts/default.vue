<script setup lang="ts">
const layout = useLayout()
</script>

<template>
  <div
    class="flex justify-center min-h-screen"
    :class="{ 'h-screen': layout.isLoading }"
  >
    <!-- Mobile Header - only visible on mobile -->
    <MobileHeader />

    <div
      class="flex w-full justify-center pt-16 md:pt-0"
      :class="{
        container: layout.isContainer
      }"
    >
      <!-- Left Sidebar - hidden on mobile, visible on md+ -->
      <SidebarLeft />

      <main
        class="flex-1 w-full pb-20 md:pb-0"
        :class="{
          'max-w-xl': layout.isContainer
        }"
      >
        <slot />
      </main>

      <div
        v-if="layout.shouldShowRightSidebar"
        class="relative w-full max-w-xs"
      >
        <SidebarRight v-if="!layout.isChatPage" />
        <ChatUserList v-else />
      </div>
    </div>

    <!-- Mobile Bottom Navigation - only visible on mobile -->
    <MobileBottomNav />

    <LoadingSplash v-if="layout.isLoading" />
  </div>
</template>
