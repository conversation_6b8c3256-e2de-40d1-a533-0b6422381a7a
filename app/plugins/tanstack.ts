import { QueryClient, VueQueryPlugin } from '@tanstack/vue-query'

export default defineNuxtPlugin(nuxtApp => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        // Refetch on window focus for real-time updates
        refetchOnWindowFocus: true,
        // Refetch when network reconnects
        refetchOnReconnect: true,
        // Keep data for 5 minutes in cache
        staleTime: 5 * 60 * 1000, // 5 minutes
        // Keep in memory for 10 minutes
        gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
        // Retry failed requests
        retry: 3,
        // Retry delay with exponential backoff
        retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000)
      }
    }
  })

  nuxtApp.vueApp.use(VueQueryPlugin, { queryClient })
  nuxtApp.provide('queryClient', queryClient)
})
