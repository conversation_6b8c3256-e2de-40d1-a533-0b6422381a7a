{"name": "deplon", "private": true, "type": "module", "version": "1.0.0", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "lint": "eslint .", "lint:fix": "eslint . --fix", "prisma:reset": "npx prisma@6.15.0 migrate reset", "prisma:migrate": "npx prisma@6.15.0 migrate dev", "prisma:generate": "npx prisma@6.15.0 generate", "prettier": "prettier --check .", "prettier:fix": "prettier --write ."}, "dependencies": {"@coral-xyz/anchor": "^0.31.1", "@nuxt/eslint": "1.9.0", "@nuxt/fonts": "^0.11.4", "@nuxt/image": "1.11.0", "@prisma/nuxt": "^0.3.0", "@solana/kit": "^2.2.1", "@solana/spl-token": "^0.4.13", "@solana/wallet-adapter-base": "^0.9.27", "@solana/web3.js": "^1.98.2", "@tailwindcss/vite": "^4.1.11", "@tanstack/vue-query": "^5.83.0", "@vueuse/core": "^13.5.0", "@vueuse/nuxt": "^13.5.0", "bs58": "^6.0.0", "buffer": "^6.0.3", "eslint": "^9.36.0", "helius-sdk": "^1.5.1", "jayson": "^4.2.0", "lightweight-charts": "^5.0.8", "nodemailer": "^6.9.13", "nuxt": "^3.17.6", "nuxt-auth-utils": "0.5.20", "nuxt-gtag": "3.0.3", "nuxt-nodemailer": "1.1.2", "pinia": "^3.0.3", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "vite": "^7.0.2", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@nuxt/icon": "^1.15.0", "@pinia/nuxt": "^0.11.1", "clsx": "^2.1.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.4", "prettier": "^3.6.2", "reka-ui": "^2.3.2", "tailwind-variants": "^1.0.0"}}