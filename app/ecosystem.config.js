export default {
  apps: [
    {
      name: 'mazenut',
      exec_mode: 'cluster',
      instances: 'max',
      script: './.output/server/index.mjs',
      env: {
        PORT: 3000,
        NODE_ENV: 'development'
      },
      env_production: {
        PORT: process.env.NUXT_PORT || 3001,
        NODE_ENV: 'production'
      },
      env_staging: {
        PORT: process.env.STAGING_PORT || 3002,
        NODE_ENV: 'staging'
      }
    }
  ]
}
