generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id                  Int            @id @default(autoincrement())
  email               String         @unique
  address             String         @unique
  name                String?
  displayName         String?
  bio                 String?
  profilePicture      String?
  coverImage          String?
  website             String?
  twitterHandle       String?        @unique
  emailVerified       Boolean        @default(false) // Indicates if the user's email is verified
  onChainId           Int?           @unique // Sequential ID for on-chain user state
  createdAt           DateTime       @default(now())
  updatedAt           DateTime       @updatedAt
  authCodes           AuthCode[]
  onChainState        UserState?
  posts               Post[]
  tokens              Token[]
  postLikes           PostLike[]
  postShares          PostShare[]
  mentions            Mention[]
  transactions        Transaction[]
  followers           Follow[]       @relation("Follower")
  followed            Follow[]       @relation("Followed")
  interactions        Interaction[]
  notifications       Notification[] @relation("targetUser") // notifs sent to this user
  sourceNotifications Notification[] @relation("sourceUser") // notifs sent by this user
  messagesSent       Message[]      @relation("MessageSender")
  messagesReceived   Message[]      @relation("MessageReceiver")
}

model UserState {
  id         Int      @id @default(autoincrement())
  userId     Int      @unique
  onChainId  Int      @unique
  statePda   String   @unique // The PDA address of the on-chain user state
  tokenCount Int      @default(0)
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([onChainId])
  @@index([statePda])
}

model AuthCode {
  id        Int      @id @default(autoincrement())
  userId    Int
  code      String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([code])
}

model Post {
  id         Int      @id @default(autoincrement())
  userId     Int
  content    String
  likeCount  Int      @default(0)
  replyCount Int      @default(0)
  shareCount Int      @default(0)
  replyToId  Int? // For replies to other posts
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  user          User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  likes         PostLike[]
  shares        PostShare[]
  mentions      Mention[]
  interactions  Interaction[]
  media         PostMedia[]
  replyTo       Post?          @relation("PostReplies", fields: [replyToId], references: [id])
  replies       Post[]         @relation("PostReplies")
  notifications Notification[]

  @@index([userId])
  @@index([createdAt])
  @@index([replyToId])
}

model PostMedia {
  id        Int      @id @default(autoincrement())
  postId    Int
  url       String // File path or URL to the media
  type      String // 'image' or 'video' (for future expansion)
  mimeType  String? // MIME type (image/jpeg, image/png, etc.)
  width     Int? // Image width in pixels
  height    Int? // Image height in pixels
  size      Int? // File size in bytes
  order     Int      @default(0) // Order of media in post (for sorting)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  post Post @relation(fields: [postId], references: [id], onDelete: Cascade)

  @@index([postId])
  @@index([postId, order])
}

model PostLike {
  id        Int      @id @default(autoincrement())
  postId    Int
  userId    Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  post Post @relation(fields: [postId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([postId, userId]) // Prevent duplicate likes
  @@index([postId])
  @@index([userId])
}

model PostShare {
  id        Int      @id @default(autoincrement())
  postId    Int
  userId    Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  post Post @relation(fields: [postId], references: [id], onDelete: Cascade)
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([postId, userId]) // Prevent duplicate shares from same user
  @@index([postId])
  @@index([userId])
  @@index([createdAt])
}

model Hashtag {
  id        Int       @id @default(autoincrement())
  name      String    @unique
  postCount Int       @default(0)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  mentions  Mention[]

  @@index([name])
  @@index([postCount])
}

model Mention {
  id        Int      @id @default(autoincrement())
  postId    Int
  userId    Int? // For user mentions
  hashtagId Int? // For hashtag mentions
  tokenId   Int? // For token mentions
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  post    Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  user    User?    @relation(fields: [userId], references: [id], onDelete: Cascade)
  hashtag Hashtag? @relation(fields: [hashtagId], references: [id], onDelete: Cascade)
  token   Token?   @relation(fields: [tokenId], references: [id], onDelete: Cascade)

  @@index([postId])
  @@index([userId])
  @@index([hashtagId])
  @@index([tokenId])
}

model Token {
  id           Int      @id @default(autoincrement())
  userId       Int
  name         String
  symbol       String
  description  String?
  image        String?
  twitter      String?
  telegram     String?
  website      String?
  instagram    String?
  discord      String?
  address      String   @unique // Token addresses should be unique
  curveAddress String?  @unique
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  user          User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  mentions      Mention[]
  transactions  Transaction[]
  priceLogs     PriceLog[]
  interactions  Interaction[]
  notificaitons Notification[]

  @@index([userId])
  @@index([address])
  @@index([name, address])
}

enum TransactionType {
  BUY
  SELL
  TRANSFER
  SWAP
}

model Transaction {
  id              Int             @id @default(autoincrement())
  userId          Int?
  tokenId         Int
  tokenAmount     BigInt // 9 decimals
  amountLamports  BigInt? // SOL amount spent/received in lamports
  priceBefore     Float? // Price before the transaction
  priceAfter      Float? // Price after the transaction
  solResBefore    BigInt? // SOL reserves before the transaction
  solResAfter     BigInt? // SOL reserves after the transaction
  transactionHash String          @unique // Unique transaction identifier
  isOwner         Boolean         @default(false) // Indicates if the user is the owner of the transaction
  isBot           Boolean         @default(false) // Indicates if the transaction is from a bot
  type            TransactionType

  user  User? @relation(fields: [userId], references: [id], onDelete: Cascade)
  token Token @relation(fields: [tokenId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@index([userId])
  @@index([tokenId])
  @@index([transactionHash])
}

model PriceLog {
  id        Int      @id @default(autoincrement())
  tokenId   Int
  price     Float
  volume    Float? // Optional volume for the price log
  timestamp DateTime @default(now())

  token Token @relation(fields: [tokenId], references: [id], onDelete: Cascade)

  @@index([tokenId])
  @@index([timestamp])
}

model Follow {
  id         Int      @id @default(autoincrement())
  followerId Int
  followedId Int
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  follower User @relation("Follower", fields: [followerId], references: [id], onDelete: Cascade)
  followed User @relation("Followed", fields: [followedId], references: [id], onDelete: Cascade)
  userId   Int?

  @@unique([followerId, followedId]) // Prevent duplicate follows
  @@index([followerId])
  @@index([followedId])
}

enum InteractionType {
  LIKE
  REPLY
  SHARE
  REPOST
  MENTION
  VIEW
  BOOKMARK
}

// used in the recommender system of coins and posts
model Interaction {
  id        Int             @id @default(autoincrement())
  userId    Int
  postId    Int?
  tokenId   Int?
  type      InteractionType
  createdAt DateTime        @default(now())
  updatedAt DateTime        @updatedAt

  user  User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  post  Post?  @relation(fields: [postId], references: [id], onDelete: Cascade)
  token Token? @relation(fields: [tokenId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([postId])
  @@index([tokenId])
}

enum EmailSubscriptionType {
  NEWSLETTER
  UPDATES
  PROMOTIONS
  LAUNCH
}

model EmailSubscription {
  id        Int                   @id @default(autoincrement())
  email     String                @unique
  type      EmailSubscriptionType
  createdAt DateTime              @default(now())
  updatedAt DateTime              @updatedAt

  @@index([email])
  @@index([email, type])
}

enum NotificationType {
  COMMENT
  LIKE
  FOLLOW
  MENTION
  SHARE
  TOKEN
}

model Notification {
  id           Int              @id @default(autoincrement())
  sourceUserId Int?
  targetUserId Int? // User receiving the notification
  tokenId      Int?
  postId       Int?
  type         NotificationType
  content      String
  read         Boolean          @default(false)
  createdAt    DateTime         @default(now())
  updatedAt    DateTime         @updatedAt

  sourceUser User?  @relation("sourceUser", fields: [sourceUserId], references: [id], onDelete: Cascade)
  targetUser User?  @relation("targetUser", fields: [targetUserId], references: [id], onDelete: Cascade)
  token      Token? @relation(fields: [tokenId], references: [id], onDelete: Cascade)
  post       Post?  @relation(fields: [postId], references: [id], onDelete: Cascade)
  userId     Int?

  @@index([sourceUserId])
  @@index([type])
}


// conversation between two users
model Conversation {
  id           Int       @id @default(autoincrement())
  lastMessage  String?
  lastUpdated  DateTime  @default(now()) @updatedAt
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  messages     Message[]
}

model Message {
  id             Int       @id @default(autoincrement())
  conversationId Int
  senderId       Int
  receiverId     Int
  content        String
  read           Boolean   @default(false)
  createdAt      DateTime  @default(now())
  updatedAt      DateTime  @updatedAt
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  sender       User         @relation("MessageSender", fields: [senderId], references: [id], onDelete: Cascade)
  receiver     User         @relation("MessageReceiver", fields: [receiverId], references: [id], onDelete: Cascade)
  @@index([conversationId])
  @@index([senderId])
  @@index([receiverId])
  @@index([receiverId, senderId])
}