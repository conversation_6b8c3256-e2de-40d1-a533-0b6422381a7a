/*
  Warnings:

  - You are about to alter the column `priceAfter` on the `Transaction` table. The data in that column could be lost. The data in that column will be cast from `BigInt` to `Float`.
  - You are about to alter the column `priceBefore` on the `Transaction` table. The data in that column could be lost. The data in that column will be cast from `BigInt` to `Float`.

*/
-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_Transaction" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "userId" INTEGER NOT NULL,
    "tokenId" INTEGER NOT NULL,
    "tokenAmount" BIGINT NOT NULL,
    "amountLamports" BIGINT,
    "priceBefore" REAL,
    "priceAfter" REAL,
    "solResBefore" BIGINT,
    "solResAfter" BIGINT,
    "transactionHash" TEXT NOT NULL,
    "isOwner" BOOLEAN NOT NULL DEFAULT false,
    "isBot" BOOLEAN NOT NULL DEFAULT false,
    "type" TEXT NOT NULL,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "Transaction_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE CASCADE ON UPDATE CASCADE,
    CONSTRAINT "Transaction_tokenId_fkey" FOREIGN KEY ("tokenId") REFERENCES "Token" ("id") ON DELETE CASCADE ON UPDATE CASCADE
);
INSERT INTO "new_Transaction" ("amountLamports", "createdAt", "id", "isBot", "isOwner", "priceAfter", "priceBefore", "solResAfter", "solResBefore", "tokenAmount", "tokenId", "transactionHash", "type", "updatedAt", "userId") SELECT "amountLamports", "createdAt", "id", "isBot", "isOwner", "priceAfter", "priceBefore", "solResAfter", "solResBefore", "tokenAmount", "tokenId", "transactionHash", "type", "updatedAt", "userId" FROM "Transaction";
DROP TABLE "Transaction";
ALTER TABLE "new_Transaction" RENAME TO "Transaction";
CREATE UNIQUE INDEX "Transaction_transactionHash_key" ON "Transaction"("transactionHash");
CREATE INDEX "Transaction_userId_idx" ON "Transaction"("userId");
CREATE INDEX "Transaction_tokenId_idx" ON "Transaction"("tokenId");
CREATE INDEX "Transaction_transactionHash_idx" ON "Transaction"("transactionHash");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
