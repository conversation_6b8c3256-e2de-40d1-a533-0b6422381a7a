/*
  Warnings:

  - A unique constraint covering the columns `[onChainId]` on the table `User` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "User" ADD COLUMN "onChainId" INTEGER;

-- CreateTable
CREATE TABLE "UserState" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "userId" INTEGER NOT NULL,
    "onChainId" INTEGER NOT NULL,
    "statePda" TEXT NOT NULL,
    "tokenCount" INTEGER NOT NULL DEFAULT 0,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL,
    CONSTRAINT "UserState_userId_fkey" FOREIGN KEY ("userId") REFERENCES "User" ("id") ON DELETE RESTRICT ON UPDATE CASCADE
);

-- CreateIndex
CREATE UNIQUE INDEX "UserState_userId_key" ON "UserState"("userId");

-- CreateIndex
CREATE UNIQUE INDEX "UserState_onChainId_key" ON "UserState"("onChainId");

-- CreateIndex
CREATE UNIQUE INDEX "UserState_statePda_key" ON "UserState"("statePda");

-- CreateIndex
CREATE UNIQUE INDEX "User_onChainId_key" ON "User"("onChainId");
