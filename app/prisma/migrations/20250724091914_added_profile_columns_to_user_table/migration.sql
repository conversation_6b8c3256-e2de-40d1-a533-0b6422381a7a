-- RedefineTables
PRAGMA defer_foreign_keys=ON;
PRAGMA foreign_keys=OFF;
CREATE TABLE "new_User" (
    "id" INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
    "email" TEXT NOT NULL,
    "address" TEXT NOT NULL,
    "name" TEXT,
    "displayName" TEXT,
    "bio" TEXT,
    "profilePicture" TEXT,
    "coverImage" TEXT,
    "website" TEXT,
    "twitterHandle" TEXT,
    "emailVerified" BOOLEAN NOT NULL DEFAULT false,
    "onChainId" INTEGER,
    "createdAt" DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" DATETIME NOT NULL
);
INSERT INTO "new_User" ("address", "createdAt", "email", "id", "name", "onChainId", "updatedAt") SELECT "address", "createdAt", "email", "id", "name", "onChainId", "updatedAt" FROM "User";
DROP TABLE "User";
ALTER TABLE "new_User" RENAME TO "User";
CREATE UNIQUE INDEX "User_email_key" ON "User"("email");
CREATE UNIQUE INDEX "User_address_key" ON "User"("address");
CREATE UNIQUE INDEX "User_twitterHandle_key" ON "User"("twitterHandle");
CREATE UNIQUE INDEX "User_onChainId_key" ON "User"("onChainId");
PRAGMA foreign_keys=ON;
PRAGMA defer_foreign_keys=OFF;
