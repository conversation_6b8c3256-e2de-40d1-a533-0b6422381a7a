import BN from 'bn.js'
import { Program, AnchorProvider, web3 } from '@coral-xyz/anchor/dist/browser'
import {
  getAssociatedTokenAddress,
  TOKEN_PROGRAM_ID,
  ASSOCIATED_TOKEN_PROGRAM_ID
} from '@solana/spl-token'
import type { Deplon } from '~/server/services/deplon'
import idl from '~/deplon.json'

export const useFinance = defineStore('finance', () => {
  const currentToken = ref<string | null>(null)
  const wallet = useWallet()
  const program = ref<Program<Deplon> | null>()
  const connection = ref<web3.Connection>()
  const currentTokenOwnedAmount = ref<number>(0)
  const currentTokenPrice = ref('0')

  // Function to refresh current token balance
  const refreshTokenBalance = async (tokenAddress: string) => {
    try {
      const wallet = useWallet()
      if (!wallet.connected || !wallet.publicKey) {
        return
      }

      const response = await $fetch('/api/fin/balance', {
        method: 'GET',
        query: {
          address: tokenAddress,
          userAddress: wallet.publicKey.toString()
        }
      })

      currentTokenOwnedAmount.value = response.balance
    } catch (error) {
      console.error('Error refreshing token balance:', error)
    }
  }

  onMounted(async () => {
    const config = useRuntimeConfig()
    const programId = useRuntimeConfig().public.web3ProgramId
    if (!programId) {
      throw new Error('Web3 program ID is not configured')
    }

    connection.value = new web3.Connection(
      config.public.rpcEndpoint ?? 'http://localhost:8899',
      'confirmed'
    )

    // wait a bit for auto-connect
    await new Promise<void>(resolve => {
      let c = 0
      const interval = setInterval(() => {
        if (c > 10) {
          clearInterval(interval)
          console.warn('cant connect')
          resolve()
          return
        }
        if (wallet.connected && wallet.publicKey) {
          clearInterval(interval)
          resolve()
        }

        c++
      }, 500)
    })

    if (wallet.connected && wallet.publicKey) {
      const anchorWallet = {
        publicKey: wallet.publicKey,
        signTransaction: wallet.signTransaction,
        signAllTransactions: wallet.signAllTransactions
      }
      const provider = new AnchorProvider(connection.value, anchorWallet, {})
      program.value = new Program(idl, provider) as Program<Deplon>
    }
  })

  const buyCurrentToken = async (
    address: string,
    solAmount: number,
    tokensAmount: number
  ) => {
    if (!address) {
      throw new Error('Error executing BUY: token address is required')
    }
    if (!solAmount || solAmount <= 0) {
      throw new Error('Error executing BUY: valid SOL amount is required')
    }

    if (!wallet.publicKey) {
      throw new Error('Wallet not connected')
    }

    try {
      // Calculate max_sol_cost with slippage tolerance (default 1%)
      const slippageTolerance = 0.01 // 1% slippage tolerance
      const maxSolCost = new BN(
        Math.ceil(solAmount * 1e9 * (1 + slippageTolerance))
      )

      // Convert tokensAmount to base units properly to preserve decimals
      // Use string manipulation to avoid floating point overflow
      const tokensAmountStr = tokensAmount.toString()
      const [integerPart, decimalPart = ''] = tokensAmountStr.split('.')
      const paddedDecimal = decimalPart.padEnd(9, '0').substring(0, 9)
      const tokensAmountInBaseUnits = new BN(integerPart + paddedDecimal)

      // Manually resolve all required accounts
      const mintPubkey = new web3.PublicKey(address)

      if (!program.value) {
        throw new Error('Program not initialized')
      }

      // Get the curve account PDA
      const [tokenCurveAccount] = web3.PublicKey.findProgramAddressSync(
        [Buffer.from('curve'), mintPubkey.toBuffer()],
        program.value.programId
      )

      // Get buyer's associated token account
      const buyerTokenAccount = await getAssociatedTokenAddress(
        mintPubkey,
        wallet.publicKey
      )

      // Get curve's associated token account
      const curveTokenAccount = await getAssociatedTokenAddress(
        mintPubkey,
        tokenCurveAccount,
        true // allowOwnerOffCurve
      )

      const tx = await program.value.methods
        .buyToken(
          tokensAmountInBaseUnits, // tokens to purchase
          maxSolCost // max sol price with slippage protection
        )
        .accountsStrict({
          buyer: wallet.publicKey,
          mint: mintPubkey,
          buyerTokenAccount: buyerTokenAccount,
          curveTokenAccount: curveTokenAccount,
          tokenCurveAccount: tokenCurveAccount,
          systemProgram: web3.SystemProgram.programId,
          tokenProgram: TOKEN_PROGRAM_ID,
          associatedTokenProgram: ASSOCIATED_TOKEN_PROGRAM_ID
        })
        .transaction()

      if (!tx || !program.value?.provider.sendAndConfirm) {
        throw new Error('Failed to create transaction')
      }

      const { blockhash, lastValidBlockHeight } =
        await program.value.provider.connection.getLatestBlockhash()
      tx.recentBlockhash = blockhash
      tx.feePayer = wallet.publicKey
      tx.lastValidBlockHeight = lastValidBlockHeight
      const signedTx = await wallet.signTransaction(tx)

      try {
        const txSig =
          await program.value.provider.connection.sendRawTransaction(
            signedTx.serialize(),
            {
              skipPreflight: false,
              preflightCommitment: 'confirmed',
              maxRetries: 1
            }
          )
        return { signature: txSig, tokensAmount }
      } catch (error: any) {
        if (error?.message?.includes('already been processed')) {
          return { signature: null, tokensAmount }
        }

        throw error
      }
    } catch (error: any) {
      // Handle specific error types gracefully
      if (error?.message?.includes('already been processed')) {
        return { signature: 'already-processed' }
      } else if (error?.message?.includes('User rejected')) {
        console.error('Error buying token:', error)
        throw new Error('Transaction was cancelled by user')
      } else if (error?.message?.includes('Insufficient funds')) {
        console.error('Error buying token:', error)
        throw new Error('Insufficient SOL balance')
      } else if (error?.message?.includes('Transaction failed')) {
        console.error('Error buying token:', error)
        throw new Error('Transaction failed on blockchain')
      }

      throw error
    }
  }

  const sellCurrentToken = async (
    address: string,
    tokenAmount: number,
    solAmount: number
  ) => {
    if (!address) {
      throw new Error('Error executing SELL: token address is required')
    }

    if (!tokenAmount || tokenAmount <= 0) {
      throw new Error('Error executing SELL: valid token amount is required')
    }

    if (!wallet.publicKey) {
      throw new Error('Wallet not connected')
    }

    try {
      // Calculate min_sol_amount with slippage tolerance (default 1%)
      // This ensures the user doesn't receive significantly less SOL than expected
      const slippageTolerance = 0.01 // 1% slippage tolerance
      const minSolAmount = new BN(
        Math.floor(solAmount * 1e9 * (1 - slippageTolerance))
      )

      // Convert tokenAmount to base units properly to preserve decimals
      // Use string manipulation to avoid floating point overflow
      const tokenAmountStr = tokenAmount.toString()
      const [integerPart, decimalPart = ''] = tokenAmountStr.split('.')
      const paddedDecimal = decimalPart.padEnd(9, '0').substring(0, 9)
      const tokenAmountInBaseUnits = new BN(integerPart + paddedDecimal)

      // Manually resolve all required accounts
      const mintPubkey = new web3.PublicKey(address)

      if (!program.value) {
        throw new Error('Program not initialized')
      }

      // Get the curve account PDA
      const [tokenCurveAccount] = web3.PublicKey.findProgramAddressSync(
        [Buffer.from('curve'), mintPubkey.toBuffer()],
        program.value.programId
      )

      // Get seller's associated token account
      const sellerTokenAccount = await getAssociatedTokenAddress(
        mintPubkey,
        wallet.publicKey
      )

      // Get curve's associated token account
      const curveTokenAccount = await getAssociatedTokenAddress(
        mintPubkey,
        tokenCurveAccount,
        true // allowOwnerOffCurve
      )

      const tx = await program.value.methods
        .sellToken(
          tokenAmountInBaseUnits, // tokens to sell
          minSolAmount // min sol amount with slippage protection
        )
        .accountsPartial({
          seller: wallet.publicKey,
          mint: mintPubkey,
          sellerTokenAccount: sellerTokenAccount,
          curveTokenAccount: curveTokenAccount,
          tokenCurveAccount: tokenCurveAccount,
          systemProgram: web3.SystemProgram.programId,
          tokenProgram: TOKEN_PROGRAM_ID
        })
        .transaction()

      if (!tx || !program.value?.provider.sendAndConfirm) {
        throw new Error('Failed to create transaction')
      }

      const { blockhash, lastValidBlockHeight } =
        await program.value.provider.connection.getLatestBlockhash()
      tx.recentBlockhash = blockhash
      tx.feePayer = wallet.publicKey
      tx.lastValidBlockHeight = lastValidBlockHeight
      const signedTx = await wallet.signTransaction(tx)

      try {
        const txSig =
          await program.value.provider.connection.sendRawTransaction(
            signedTx.serialize(),
            {
              skipPreflight: false,
              preflightCommitment: 'confirmed',
              maxRetries: 1
            }
          )

        // Update local state and refresh balance
        currentToken.value = address

        return { signature: txSig, tokensAmount: tokenAmount }
      } catch (error: any) {
        if (error?.message?.includes('already been processed')) {
          return { signature: null, tokensAmount: tokenAmount }
        }

        throw error
      }
    } catch (error: any) {
      // Handle specific error types gracefully
      if (error?.message?.includes('already been processed')) {
        return { signature: 'already-processed' }
      } else if (error?.message?.includes('User rejected')) {
        console.error('Error selling token:', error)
        throw new Error('Transaction was cancelled by user')
      } else if (error?.message?.includes('Insufficient funds')) {
        console.error('Error selling token:', error)
        throw new Error('Insufficient token balance')
      } else if (error?.message?.includes('Transaction failed')) {
        console.error('Error selling token:', error)
        throw new Error('Transaction failed on blockchain')
      }

      throw error
    }
  }

  const getCurrentTokenBalance = (): number => {
    return currentTokenOwnedAmount.value
  }

  return {
    currentToken,
    currentTokenOwnedAmount,
    currentTokenPrice,
    buyCurrentToken,
    sellCurrentToken,
    refreshTokenBalance,
    getCurrentTokenBalance
  }
})
