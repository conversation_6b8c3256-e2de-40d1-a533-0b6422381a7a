import { breakpointsTailwind } from '@vueuse/core'

export const useLayout = defineStore('layout', () => {
  const isTokenPage = ref(false)
  const isChatPage = ref(false)
  const isLoading = ref(true)
  const breakpoints = useBreakpoints(breakpointsTailwind, { ssrWidth: 768 })
  //  is sidebar fully expanded
  const isFullLeftSidebar = ref(false)
  const shouldShowRightSidebar = ref(false)
  // .container should not be there if isn't a full right sidebar
  const isContainer = ref(false)

  onMounted(() => {
    isLoading.value = true
  })

  watch([breakpoints.current(), isTokenPage], () => {
    if (isTokenPage.value) {
      setTimeout(() => {
        isLoading.value = false
      }, 100)
      return
    }

    if (breakpoints.isSmaller('md')) {
      // Mobile: hide left sidebar completely (moved to bottom nav), hide right sidebar
      isFullLeftSidebar.value = false
      isContainer.value = false
      shouldShowRightSidebar.value = false
    } else if (breakpoints.isSmaller('xl')) {
      // Tablet (md to lg): show left sidebar collapsed, hide right sidebar
      isFullLeftSidebar.value = false
      isContainer.value = false
      shouldShowRightSidebar.value = false
    } else {
      // Desktop (xl and up): show everything
      isFullLeftSidebar.value = true
      isContainer.value = true
      shouldShowRightSidebar.value = true
    }

    setTimeout(() => {
      isLoading.value = false
    }, 100)
  })

  const setIsTokenPage = (value: boolean) => {
    isTokenPage.value = value
    // If it's a token page, we want to show the right sidebar
    shouldShowRightSidebar.value = false
    isFullLeftSidebar.value = false
    isContainer.value = false
  }

  const setIsChatPage = (value: boolean) => {
    isChatPage.value = value
  }

  const disableRightSidebar = () => {
    shouldShowRightSidebar.value = false
  }

  const narrowLeftSidebar = () => {
    isFullLeftSidebar.value = false
  }

  return {
    isFullLeftSidebar,
    isContainer,
    shouldShowRightSidebar,
    isLoading,
    breakpoints,
    isChatPage,
    setIsTokenPage,
    disableRightSidebar,
    narrowLeftSidebar,
    setIsChatPage
  }
})
