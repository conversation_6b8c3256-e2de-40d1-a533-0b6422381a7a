import { defineStore } from 'pinia'
import type {
  PublicKey,
  Transaction,
  VersionedTransaction
} from '@solana/web3.js'

declare global {
  interface Window {
    phantom?: {
      solana?: PhantomProvider
    }
  }
}

export const useWallet = defineStore('wallet', () => {
  const connected = ref<boolean>(false)
  const connecting = ref<boolean>(false)
  const publicKey = ref<PublicKey | null>(null)
  const phantom = ref<PhantomProvider | null>(null)

  // Check if Phantom is installed with better detection for Firefox
  const isPhantomInstalled = computed(() => {
    if (!import.meta.client) return false

    // Primary check for Phantom
    if (window.phantom?.solana?.isPhantom) return true

    // Fallback check for global solana object
    if ((window as any).solana?.isPhantom) return true

    // Additional check for Firefox - sometimes the provider is available but not immediately
    return !!(window.phantom || (window as any).solana)
  })

  // Get the phantom provider with retry logic for Firefox
  const getProvider = (): PhantomProvider | null => {
    if (!import.meta.client) return null

    if (window.phantom?.solana?.isPhantom) {
      return window.phantom.solana
    }

    // Fallback to global solana object (some versions of Phantom)
    if ((window as any).solana?.isPhantom) {
      return (window as any).solana
    }

    return null
  }

  // Wait for provider to be available (especially needed for Firefox)
  const waitForProvider = (timeout = 3000): Promise<PhantomProvider | null> => {
    return new Promise(resolve => {
      if (!import.meta.client) {
        resolve(null)
        return
      }

      const provider = getProvider()
      if (provider) {
        resolve(provider)
        return
      }

      let attempts = 0
      const maxAttempts = timeout / 100

      const checkProvider = () => {
        attempts++
        const provider = getProvider()

        if (provider) {
          resolve(provider)
        } else if (attempts >= maxAttempts) {
          resolve(null)
        } else {
          setTimeout(checkProvider, 100)
        }
      }

      checkProvider()
    })
  }

  // Initialize phantom provider and check existing connection
  const initialize = async () => {
    if (!import.meta.client) return

    try {
      // Wait for provider to be available (especially important for Firefox)
      const provider = await waitForProvider(5000)
      if (!provider) {
        console.warn('Phantom provider not found after waiting')
        return
      }

      phantom.value = provider

      // Check if already connected with additional verification
      if (provider.isConnected && provider.publicKey) {
        try {
          // Verify the connection is still valid by attempting a quick check
          // This helps catch stale connections that Firefox sometimes reports
          connected.value = true
          publicKey.value = provider.publicKey
        } catch (error) {
          console.warn('Stale wallet connection detected, clearing state')
          handleDisconnect()
        }
      } else {
        // If wallet isn't auto-connected, check if user is logged in
        // and try to reconnect silently (especially important for Firefox)
        await attemptSilentReconnect()
      }

      // Listen for account changes
      provider.on('accountChanged', (newPublicKey: PublicKey | null) => {
        if (newPublicKey) {
          connected.value = true
          publicKey.value = newPublicKey
        } else {
          handleDisconnect()
        }
      })

      // Listen for disconnect
      provider.on('disconnect', () => {
        handleDisconnect()
      })
    } catch (error) {
      console.error('Failed to initialize wallet:', error)
    }
  }

  // Attempt to silently reconnect wallet if user is authenticated
  const attemptSilentReconnect = async () => {
    try {
      const session = useUserSession()
      await session.fetch() // Ensure session is loaded

      if (session.loggedIn.value && session.user.value?.address) {
        const provider = phantom.value
        if (!provider) return

        // Try to connect with onlyIfTrusted: true for silent reconnection
        try {
          const response = await provider.connect({ onlyIfTrusted: true })

          // Verify this matches the logged-in user
          if (response.publicKey.toBase58() === session.user.value.address) {
            connected.value = true
            publicKey.value = response.publicKey
          }
        } catch (silentConnectError: unknown) {
          // Silent connection failed, this is normal if user hasn't granted persistent permission
          console.error(
            'Silent wallet reconnection not available, user will need to connect manually'
          )
        }
      }
    } catch (error) {
      console.error('Error during silent reconnect attempt:', error)
    }
  }

  const handleDisconnect = () => {
    connected.value = false
    connecting.value = false
    publicKey.value = null
  }

  const connect = async () => {
    if (!isPhantomInstalled.value) {
      throw new Error(
        'Phantom wallet is not installed. Please install it from https://phantom.app/'
      )
    }

    // Wait for provider to be available (Firefox compatibility)
    const provider = await waitForProvider(5000)
    if (!provider) {
      throw new Error('Unable to detect Phantom wallet after waiting')
    }

    try {
      connecting.value = true

      // Use onlyIfTrusted: false to force the connection popup
      // This is important for Firefox where auto-connection might fail
      const response = await provider.connect({ onlyIfTrusted: false })

      connected.value = true
      publicKey.value = response.publicKey
      phantom.value = provider

      return response.publicKey
    } catch (error) {
      console.error('Failed to connect wallet:', error)
      handleDisconnect()
      throw error
    } finally {
      connecting.value = false
    }
  }

  const disconnect = async () => {
    if (!phantom.value) return

    try {
      await phantom.value.disconnect()
      handleDisconnect()
    } catch (error) {
      console.error('Error disconnecting wallet:', error)
      // Still reset local state even if disconnect fails
      handleDisconnect()
    }
  }

  const signTransaction = async (
    transaction: Transaction | VersionedTransaction
  ) => {
    if (!phantom.value || !connected.value) {
      throw new Error('Wallet not connected')
    }

    try {
      const signedTransaction = await phantom.value.signTransaction(transaction)
      return signedTransaction
    } catch (error) {
      console.error('Failed to sign transaction:', error)
      throw error
    }
  }

  const signAllTransactions = async (
    transactions: (Transaction | VersionedTransaction)[]
  ) => {
    if (!phantom.value || !connected.value) {
      throw new Error('Wallet not connected')
    }

    try {
      const signedTransactions =
        await phantom.value.signAllTransactions(transactions)
      return signedTransactions
    } catch (error) {
      console.error('Failed to sign transactions:', error)
      throw error
    }
  }

  const signMessage = async (
    message: Uint8Array | string,
    display?: 'utf8' | 'hex'
  ) => {
    if (!phantom.value || !connected.value) {
      throw new Error('Wallet not connected')
    }

    try {
      const result = await phantom.value.signMessage(message, display)
      return result
    } catch (error) {
      console.error('Failed to sign message:', error)
      throw error
    }
  }

  // Check if the current wallet connection matches the authenticated user
  const isWalletMatchingAuth = async () => {
    if (!connected.value || !publicKey.value) return false

    try {
      // Get the current user session
      const session = useUserSession()
      if (!session.loggedIn.value || !session.user.value?.onChainId) {
        return false
      }

      // Compare wallet public key with user's address (not onChainId)
      return publicKey.value.toBase58() === session.user.value.address
    } catch (error) {
      console.error('Error checking wallet-auth match:', error)
      return false
    }
  }

  // Reconnect wallet if user is logged in but wallet is disconnected
  const reconnectIfNeeded = async () => {
    if (!import.meta.client) return false

    const session = useUserSession()
    await session.fetch() // Ensure session is fresh

    // If user is logged in but wallet isn't connected, try to reconnect
    if (session.loggedIn.value && !connected.value) {
      try {
        // First try silent reconnection
        await attemptSilentReconnect()

        // If still not connected, return false so UI can prompt user
        if (!connected.value) {
          return false
        }

        // Verify the reconnected wallet matches the logged-in user
        const matches = await isWalletMatchingAuth()
        if (!matches) {
          console.warn('Reconnected wallet does not match logged-in user')
          handleDisconnect()
          return false
        }

        return true
      } catch (error) {
        console.error('Failed to reconnect wallet:', error)
        return false
      }
    }

    return true
  }

  // Check wallet state and prompt reconnection if needed
  const ensureWalletConnected = async () => {
    if (connected.value) {
      // Verify the connection matches auth
      const matches = await isWalletMatchingAuth()
      return matches
    }

    // Try to reconnect if needed
    return await reconnectIfNeeded()
  }

  // Initialize on client side with better timing
  onMounted(async () => {
    // Add a small delay to ensure DOM is fully loaded
    // This is especially important for Firefox
    await new Promise(resolve => setTimeout(resolve, 100))
    await initialize()
  })

  // Watch for authentication state changes and ensure wallet is connected
  if (import.meta.client) {
    const session = useUserSession()
    watch(
      () => session.loggedIn.value,
      async isLoggedIn => {
        if (isLoggedIn && !connected.value) {
          // User just logged in but wallet isn't connected
          // Try silent reconnection (important for page refreshes in Firefox)
          await new Promise(resolve => setTimeout(resolve, 500)) // Small delay
          await attemptSilentReconnect()
        }
      },
      { immediate: false }
    )
  }

  return {
    // State
    connected,
    connecting,
    publicKey,
    isPhantomInstalled,

    // Methods
    connect,
    disconnect,
    signTransaction,
    signAllTransactions,
    signMessage,
    isWalletMatchingAuth,
    reconnectIfNeeded,
    ensureWalletConnected,

    // Legacy compatibility - BUT don't use these for login logic!
    // Use a separate auth store for actual login state
    loggedIn: connected, // ⚠️ This will be true on auto-reconnect
    handleWalletLogin: connect,
    logout: disconnect
  }
})
