import { skipHydrate } from 'pinia'
import type { PostWithRelationAndCounts as Post } from '~/types/database'

export interface Hashtag {
  id: number
  name: string
  postCount: number
  createdAt: string
  updatedAt: string
}

export interface Token {
  id: number
  name: string
  symbol: string
  address: string
  userId: number
  createdAt: string
  updatedAt: string
}

export const usePostsStore = defineStore('posts', () => {
  const posts = ref<Post[]>([])
  const currentSelectedPost = ref<Post | null>(null)
  const postOverlayVisible = ref(false)
  const selectedImageIndex = ref<number>(0) // Tracks the selected image index
  const hashtags = ref<Hashtag[]>([])
  const tokens = ref<Token[]>([])
  const internalLoading = ref(true)
  const isCreating = ref(false)
  const layout = useLayout()
  const isLoading = computed(() => internalLoading.value || layout.isLoading)

  const parseContent = (content: string) => {
    return content
      .replace(/#(\d+)/g, (_, id) => {
        const hashtag = hashtags.value.find(h => h.id === parseInt(id))
        return hashtag ? `#${hashtag.name}` : `#${id}`
      })
      .replace(/\$(\d+)/g, (_, id) => {
        const token = tokens.value.find(t => t.id === parseInt(id))
        return token ? `$${token.symbol}` : `$${id}`
      })
    // Also handle cases where token symbols are already in the content
    // This ensures existing content with symbols still displays correctly
  }

  const createPost = async (content: string, images?: File[]) => {
    if (!content.trim() && (!images || images.length === 0)) {
      throw new Error('Post content or images are required')
    }

    isCreating.value = true
    try {
      let response: Post

      if (images && images.length > 0) {
        // Use FormData for posts with images
        const formData = new FormData()
        formData.append('content', content.trim())

        // Append each image with the same field name
        images.forEach(image => {
          formData.append('images', image)
        })

        response = await $fetch<Post>('/api/posts/create', {
          method: 'POST',
          body: formData
        })
      } else {
        // Use JSON for text-only posts (legacy support)
        response = await $fetch<Post>('/api/posts/create', {
          method: 'POST',
          body: { content: content.trim() }
        })
      }

      posts.value.unshift(response)
      return response
    } finally {
      isCreating.value = false
    }
  }

  const fetchHashtags = async () => {
    try {
      const response = await $fetch<Hashtag[]>('/api/hashtags')
      hashtags.value = response
    } catch (error) {
      console.error('Failed to fetch hashtags:', error)
    }
  }

  const fetchTokens = async () => {
    try {
      const response = await $fetch<Token[]>('/api/tokens')
      tokens.value = response
    } catch (error) {
      console.error('Failed to fetch tokens:', error)
    }
  }

  const fetchPosts = async () => {
    internalLoading.value = true
    try {
      const response = await $fetch<Post[]>('/api/posts')
      posts.value = response
    } catch (error) {
      console.error('Failed to fetch posts:', error)
    } finally {
      setTimeout(() => {
        internalLoading.value = false
      }, 1000)
    }
  }

  /**
   * Fetch a post by ID with its replies and sub-replies
   * This function is used to get a specific post's details
   */
  const getExpandedPostById = async (
    id: number
  ): Promise<Post & { replies: Post[] }> => {
    const { data, error } = await useFetch<{
      post: Post & { replies: Post[] }
    }>(`/api/posts/${id}`, {
      key: `post-${id}`
    })

    if (error.value) {
      showError(`Failed to fetch post: ${error.value.message}`)
      throw error.value
    }

    const apiPost = data.value?.post
    if (!apiPost) {
      showError('Post not found')
      throw new Error('Post not found')
    }

    const normalize = (p: Post): Post => ({
      ...p,
      createdAt: new Date(p.createdAt),
      updatedAt: new Date(p.updatedAt)
    })

    return {
      ...normalize(apiPost),
      replies: (apiPost.replies ?? []).map(normalize)
    }
  }

  // Initialize data on store creation
  const initializeStore = async () => {
    await Promise.all([fetchHashtags(), fetchTokens(), fetchPosts()])
  }

  const handleOpenPostOverlay = (postId: number, imageIndex: number = 0) => {
    currentSelectedPost.value =
      posts.value.find((p: Post) => p.id === postId) || null
    selectedImageIndex.value = imageIndex
    postOverlayVisible.value = true
  }

  return {
    posts: skipHydrate(readonly(posts)),
    hashtags: skipHydrate(readonly(hashtags)),
    tokens: skipHydrate(readonly(tokens)),
    isLoading: skipHydrate(readonly(isLoading)),
    isCreating: skipHydrate(readonly(isCreating)),
    currentSelectedPost,
    postOverlayVisible,
    selectedImageIndex,
    parseContent,
    createPost,
    fetch: fetchPosts,
    fetchHashtags,
    fetchTokens,
    initializeStore,
    getExpandedPostById,
    handleOpenPostOverlay
  }
})
