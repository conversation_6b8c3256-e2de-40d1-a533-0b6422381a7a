import type { NuxtError } from '#app'
import { defineStore } from 'pinia'

export const useAuth = defineStore('auth', () => {
  const session = useUserSession()
  const isLoggedIn = computed(() => session.loggedIn.value)
  const wallet = useWallet()
  const isLoading = ref(false)

  const login = async () => {
    try {
      isLoading.value = true

      // Step 1: Check if wallet is available
      if (!wallet.isPhantomInstalled) {
        throw new Error(
          'Phantom wallet is not installed. Please install it from https://phantom.app/'
        )
      }

      // Step 2: Connect wallet if not connected
      if (!wallet.connected) {
        try {
          await wallet.connect()
        } catch (connectError: unknown) {
          const error = connectError as NuxtError
          throw new Error(
            `Failed to connect wallet: ${error?.message || 'Unknown error'}`
          )
        }
      }

      // Step 3: Verify we have a public key
      const publicKey = wallet.publicKey
      if (!publicKey) {
        throw new Error('No wallet public key available after connection')
      }

      // Step 4: Call login API (this now handles everything automatically)
      // The backend will:
      // - Create/get user with onChainId
      // - Create on-chain user state if needed (authority-only, no user signature)
      // - Set up session with all needed data
      const loginResponse = await $fetch('/api/auth/login', {
        method: 'POST',
        body: { publicKey: publicKey.toBase58() }
      })

      // Step 5: Refresh the session to update the reactive state
      await session.fetch()
      return loginResponse
    } catch (error) {
      console.error('Login failed:', error)
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const logout = async () => {
    try {
      isLoading.value = true

      // Call logout API to clear session
      await $fetch('/api/auth/logout', { method: 'POST' })

      // Refresh the session to update the reactive state
      await session.fetch()
    } catch (error) {
      console.error('Logout error:', error)
      // Session should still be cleared even if API fails
      await session.fetch()
    } finally {
      isLoading.value = false
    }
  }

  return {
    isLoggedIn,
    isLoading: readonly(isLoading),
    session: readonly(session),
    login,
    logout
  }
})
