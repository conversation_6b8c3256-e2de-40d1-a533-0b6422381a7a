import type { Notification } from '~/types/database'

export const useNotificationsStore = defineStore('notifications', () => {
  const notifications = ref<Notification[]>([])
  const unreadCount = ref<number>(0)
  let ws: WebSocket | null = null
  const isConnected = ref<boolean>(false)
  const isViewingNotifications = ref<boolean>(false)
  const isLoading = ref<boolean>(false)
  const isInitialLoad = ref<boolean>(true)

  const connectWebSocket = () => {
    // if WebSocket is already connected, do nothing
    if (ws?.readyState === WebSocket.OPEN) return

    const { loggedIn } = useUserSession()
    // if we are not logged in
    if (!loggedIn.value) return

    // Use correct WebSocket URL
    const protocol =
      import.meta.client && window.location.protocol === 'https:'
        ? 'wss:'
        : 'ws:'
    const host = import.meta.client ? window.location.host : 'localhost:3000'
    const wsUrl = `${protocol}//${host}/api/ws/notifications`

    ws = new WebSocket(wsUrl)

    ws.onopen = () => {
      isConnected.value = true
    }

    ws.onmessage = event => {
      try {
        const data = JSON.parse(event.data)

        switch (data.type) {
          case 'new_notification':
            // Only add new notifications if not on initial load to prevent sudden popup
            if (!isInitialLoad.value) {
              notifications.value.unshift(data.data)
              // If user is viewing notifications page, auto-mark as read
              if (isViewingNotifications.value) {
                setTimeout(() => markAsRead(data.data.id), 100)
              }
            }
            break
          case 'unread_count':
            unreadCount.value = data.count
            break
          case 'notifications':
            notifications.value = data.data
            break
        }
      } catch (error) {
        console.error('Error parsing notification message:', error)
      }
    }

    ws.onclose = () => {
      isConnected.value = false

      // Reconnect after 3 seconds
      setTimeout(() => {
        if (!isConnected.value) {
          connectWebSocket()
        }
      }, 3000)
    }

    ws.onerror = error => {
      console.error('Notifications WebSocket error:', error)
      isConnected.value = false
    }
  }

  // Auto-connect when store is created (only on client side)
  if (import.meta.client) {
    connectWebSocket()
  }

  const disconnect = () => {
    if (ws) {
      ws.close()
      ws = null
      isConnected.value = false
    }
  }

  const fetchNotifications = async (
    limit = 20,
    offset = 0,
    markAsRead = false
  ) => {
    try {
      isLoading.value = true
      const data = (await $fetch('/api/notifications', {
        query: { limit, offset }
      })) as Notification[]

      if (offset === 0) {
        notifications.value = data
      } else {
        notifications.value.push(...data)
      }

      // Automatically mark notifications as read if requested
      if (markAsRead && data.some(n => !n.read)) {
        await markAllAsRead()
      }

      isInitialLoad.value = false
    } catch (error) {
      console.error('Failed to fetch notifications:', error)
    } finally {
      isLoading.value = false
    }
  }

  const markAsRead = async (notificationId: number) => {
    try {
      await $fetch('/api/notifications/mark-read', {
        method: 'POST',
        body: { notificationId }
      })

      // Update local state
      const notification = notifications.value.find(
        n => n.id === notificationId
      )
      if (notification && !notification.read) {
        notification.read = true
        unreadCount.value = Math.max(0, unreadCount.value - 1)
      }
    } catch (error) {
      console.error('Failed to mark notification as read:', error)
    }
  }

  const markAllAsRead = async () => {
    try {
      await $fetch('/api/notifications/mark-all-read', {
        method: 'POST'
      })

      // Update local state
      notifications.value.forEach(n => (n.read = true))
      unreadCount.value = 0
    } catch (error) {
      console.error('Failed to mark all notifications as read:', error)
    }
  }

  const getUnreadCount = async () => {
    try {
      const data = await $fetch('/api/notifications/unread-count')
      unreadCount.value = data.count
    } catch (error) {
      console.error('Failed to get unread count:', error)
    }
  }

  // Set viewing state for auto-marking notifications as read
  const setViewingNotifications = (viewing: boolean) => {
    isViewingNotifications.value = viewing
  }

  // Send WebSocket message for real-time actions
  const sendMessage = (message: any) => {
    if (ws?.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(message))
    }
  }

  return {
    notifications,
    unreadCount,
    isConnected,
    isViewingNotifications,
    isLoading,
    isInitialLoad,
    connectWebSocket,
    disconnect,
    fetchNotifications,
    markAsRead,
    markAllAsRead,
    getUnreadCount,
    setViewingNotifications,
    sendMessage
  }
})
