import { WebSocketManager } from '~/composables/useWebsocket'

interface SidebarData {
  success: boolean
  address: string
  marketCap: {
    real: {
      lamports: string
      sol: number
      formatted: string
    }
    target: {
      lamports: string
      sol: number
      base: number
      dynamic: number
      formatted: string
    }
    progress: {
      percentage: number
      isNearMigration: boolean
    }
  }
  supply: {
    total: string
    circulating: string
    reserves: string
    circulatingPercentage: number
  }
  pricing: {
    currentPricePerToken: number
    currentPricePerTokenSol: number
    lastPriceOnChain: number
    priceVelocity: number
  }
  reserves: {
    sol: string
    tokens: string
  }
  onChain: {
    creator: string
    lastPurchaseTimestamp: string
    purchaseCountWindow: number
  }
  timestamp: number
}

interface PriceVelocityTracker {
  timestamp: number
  price: number
  volume: number
  source: 'onchain' | 'websocket'
}

interface DynamicTargetCalculation {
  baseTarget: number
  currentTarget: number
  velocityFactor: number
  timeEstimate: number
  lastAdjustment: number
}

export const useTokens = defineStore('tokens', () => {
  // State
  const sidebarData = ref<Record<string, SidebarData>>({})
  const priceVelocityHistory = ref<Record<string, PriceVelocityTracker[]>>({})
  const dynamicTargets = ref<Record<string, DynamicTargetCalculation>>({})
  const loadingStates = ref<Record<string, boolean>>({})
  const wsConnections = ref<Record<string, any>>({})
  const route = useRoute()
  const currentTokenAddress = computed(() => {
    return (route.params.address as string) ?? null
  })

  // Fetch complete sidebar data for a token
  const fetchSidebarData = async (tokenAddress: string, force = false) => {
    if (!tokenAddress) return

    // Skip if recently fetched and not forced
    const existing = sidebarData.value[tokenAddress]
    if (!force && existing && Date.now() - existing.timestamp < 5000) {
      return existing
    }

    loadingStates.value[tokenAddress] = true

    try {
      const response = await $fetch(
        `/api/trading/sidebar?address=${tokenAddress}`
      )
      sidebarData.value[tokenAddress] = response

      // Initialize dynamic target calculation if not exists
      if (!dynamicTargets.value[tokenAddress]) {
        // Use reasonable base targets - don't start too high
        const reasonableBaseTarget = Math.min(
          response.marketCap.target.base,
          500
        ) // Max 500 SOL base
        const reasonableCurrentTarget = Math.min(
          response.marketCap.target.sol,
          reasonableBaseTarget * 1.1
        ) // Max 10% above base initially

        dynamicTargets.value[tokenAddress] = {
          baseTarget: reasonableBaseTarget,
          currentTarget: reasonableCurrentTarget,
          velocityFactor: 1,
          timeEstimate: Infinity,
          lastAdjustment: Date.now()
        }
      }

      return response
    } catch (error) {
      console.error('Error fetching sidebar data:', error)
      throw error
    } finally {
      loadingStates.value[tokenAddress] = false
    }
  }

  // Track price velocity for dynamic target calculation
  const trackPriceVelocity = (
    tokenAddress: string,
    price: number,
    volume: number = 0,
    source: 'onchain' | 'websocket' = 'websocket'
  ) => {
    if (!priceVelocityHistory.value[tokenAddress]) {
      priceVelocityHistory.value[tokenAddress] = []
    }

    const tracker: PriceVelocityTracker = {
      timestamp: Date.now(),
      price,
      volume,
      source
    }

    // Add new tracker
    priceVelocityHistory.value[tokenAddress].push(tracker)

    // Keep only last 10 minutes of data for velocity calculation
    const tenMinutesAgo = Date.now() - 10 * 60 * 1000
    priceVelocityHistory.value[tokenAddress] = priceVelocityHistory.value[
      tokenAddress
    ]
      .filter(t => t.timestamp > tenMinutesAgo)
      .slice(-50) // Keep max 50 entries

    // Recalculate dynamic target
    calculateDynamicTarget(tokenAddress)
  }

  // Calculate dynamic target to keep token ~30 minutes on platform
  const calculateDynamicTarget = (tokenAddress: string) => {
    const currentData = sidebarData.value[tokenAddress]
    const velocityHistory = priceVelocityHistory.value[tokenAddress] || []
    const currentDynamic = dynamicTargets.value[tokenAddress]

    if (!currentData || !currentDynamic || velocityHistory.length < 2) return

    // Calculate price velocity (change per minute)
    const now = Date.now()
    const fiveMinutesAgo = now - 5 * 60 * 1000
    const recentHistory = velocityHistory.filter(
      v => v.timestamp > fiveMinutesAgo
    )

    if (recentHistory.length < 2) return

    // Calculate average price change per minute
    const timeSpanMs =
      recentHistory[recentHistory.length - 1].timestamp -
      recentHistory[0].timestamp
    const timeSpanMinutes = timeSpanMs / (60 * 1000)
    const priceChange =
      recentHistory[recentHistory.length - 1].price - recentHistory[0].price
    const priceVelocityPerMinute =
      timeSpanMinutes > 0 ? priceChange / timeSpanMinutes : 0

    // Calculate market cap velocity (SOL per minute)
    const currentMarketCapSol = currentData.marketCap.real.sol
    const circulatingSupply = parseFloat(currentData.supply.circulating) / 1e9
    const marketCapVelocityPerMinute =
      circulatingSupply * priceVelocityPerMinute

    // Estimate time to reach current target
    const remainingToTarget = currentDynamic.currentTarget - currentMarketCapSol
    const estimatedMinutesToTarget =
      marketCapVelocityPerMinute > 0
        ? remainingToTarget / marketCapVelocityPerMinute
        : Infinity

    // Target: keep token on platform for MINIMUM ~30 minutes
    const targetMinutes = 30
    let newTarget = currentDynamic.currentTarget
    let shouldUpdateOnChain = false

    // Only adjust if we have a reasonable velocity estimate
    if (estimatedMinutesToTarget < Infinity && estimatedMinutesToTarget > 0) {
      if (estimatedMinutesToTarget < 25) {
        // Too fast - SLIGHTLY increase target to ensure minimum 30 minutes
        // Conservative 2-5% increases only
        const increasePercentage = Math.min(
          5,
          Math.max(2, ((30 - estimatedMinutesToTarget) / 30) * 5)
        )
        newTarget =
          currentDynamic.currentTarget * (1 + increasePercentage / 100)

        shouldUpdateOnChain =
          Math.abs(newTarget - currentDynamic.currentTarget) >=
          currentDynamic.currentTarget * 0.02 // Only update for 2%+ changes
      } else if (estimatedMinutesToTarget > 60) {
        // Moving too slow - SLIGHTLY decrease target to encourage more activity
        // Very conservative 2-3% decreases only
        const decreasePercentage = Math.min(
          3,
          2 + ((estimatedMinutesToTarget - 60) / 60) * 1
        )
        newTarget =
          currentDynamic.currentTarget * (1 - decreasePercentage / 100)

        // Ensure we don't go below original base target
        newTarget = Math.max(newTarget, currentDynamic.baseTarget)

        shouldUpdateOnChain =
          Math.abs(newTarget - currentDynamic.currentTarget) >=
          currentDynamic.currentTarget * 0.02 // Only update for 2%+ changes
      }
    }

    // SAFETY: Enforce maximum target of 10,000 SOL (smart contract limit)
    const MAX_TARGET_SOL = 10000
    if (newTarget > MAX_TARGET_SOL) {
      newTarget = MAX_TARGET_SOL
      console.warn(
        `Target capped at ${MAX_TARGET_SOL} SOL for token ${tokenAddress}`
      )
    }

    // REASONABLE LIMITS: Don't allow crazy increases
    const maxReasonableTarget = currentDynamic.baseTarget * 1.2 // Max 20% above base
    if (newTarget > maxReasonableTarget) {
      newTarget = maxReasonableTarget
    }

    // PROFIT OPTIMIZATION: Only update on-chain if the change is meaningful
    // More frequent updates with smaller changes (2-5%) are better than large jumps
    const minUpdateThreshold = currentDynamic.currentTarget * 0.02 // Minimum 2% change
    const timeSinceLastUpdate = now - currentDynamic.lastAdjustment
    const minUpdateIntervalMs = 5 * 60 * 1000 // Minimum 5 minutes between updates (more responsive)

    shouldUpdateOnChain =
      shouldUpdateOnChain &&
      Math.abs(newTarget - currentDynamic.currentTarget) >=
        minUpdateThreshold &&
      timeSinceLastUpdate >= minUpdateIntervalMs

    // Update dynamic target calculation
    const previousTarget = currentDynamic.currentTarget
    dynamicTargets.value[tokenAddress] = {
      ...currentDynamic,
      currentTarget: newTarget,
      velocityFactor: newTarget / currentDynamic.baseTarget,
      timeEstimate: estimatedMinutesToTarget,
      lastAdjustment: now
    }

    // Update sidebar data with new dynamic target
    if (sidebarData.value[tokenAddress]) {
      sidebarData.value[tokenAddress].marketCap.target.dynamic = newTarget
      sidebarData.value[tokenAddress].marketCap.target.formatted =
        `${newTarget.toFixed(0)} SOL`

      // Recalculate progress with new target
      const newProgress = Math.min((currentMarketCapSol / newTarget) * 100, 100)
      sidebarData.value[tokenAddress].marketCap.progress.percentage =
        newProgress
      sidebarData.value[tokenAddress].marketCap.progress.isNearMigration =
        newProgress >= 99.5
    }
  }

  // Subscribe to real-time updates for a token
  const subscribeToUpdates = (tokenAddress: string) => {
    if (wsConnections.value[tokenAddress]) return // Already subscribed

    // Create a direct websocket manager connection without Vue lifecycle hooks
    const manager = WebSocketManager.getInstance()

    // Connect if not already connected
    if (typeof window !== 'undefined') {
      manager.connect()

      // Subscribe to price context for this token
      manager.subscribe('price', { tokenAddress }, {})

      // Get the context data directly and watch for changes
      const contextData = computed(() => manager.getContextData('price'))

      const stopWatcher = watch(
        contextData,
        newData => {
          if (newData && Array.isArray(newData) && newData.length > 0) {
            const latestPrice = newData[0] // Assuming latest is first
            if (latestPrice.tokenAddress === tokenAddress) {
              // Track price velocity
              trackPriceVelocity(
                tokenAddress,
                latestPrice.price,
                latestPrice.volume24h || 0,
                'websocket'
              )

              // Update sidebar data from multiple possible structures
              let updatedSidebarData = null

              // Check for nested structure (from WebSocket system)
              if (latestPrice.marketCap?.sidebarData) {
                updatedSidebarData = latestPrice.marketCap.sidebarData
              }
              // Check for direct structure (fallback) - type assertion needed
              else if ((latestPrice as any).sidebarData) {
                updatedSidebarData = (latestPrice as any).sidebarData
              }

              if (updatedSidebarData) {
                sidebarData.value[tokenAddress] = {
                  ...updatedSidebarData,
                  timestamp: Date.now() // Update timestamp for freshness
                }

                // Recalculate dynamic target with new data
                calculateDynamicTarget(tokenAddress)
              }
            }
          }
        },
        { immediate: false }
      )

      wsConnections.value[tokenAddress] = {
        manager,
        stopWatcher,
        subscribed: true,
        context: 'price',
        filters: { tokenAddress }
      }
    }
  }

  // Unsubscribe from updates
  const unsubscribeFromUpdates = (tokenAddress: string) => {
    const connection = wsConnections.value[tokenAddress]
    if (connection && typeof window !== 'undefined') {
      // Stop the watcher if it exists
      if (connection.stopWatcher) {
        connection.stopWatcher()
      }

      // Unsubscribe from websocket
      if (connection.manager && connection.context && connection.filters) {
        connection.manager.unsubscribe(
          connection.context,
          connection.filters,
          {}
        )
      }

      delete wsConnections.value[tokenAddress]
    }
  }

  // Getters
  const getSidebarData = (tokenAddress: string | 'current') => {
    if (tokenAddress === 'current' && currentTokenAddress.value) {
      tokenAddress = currentTokenAddress.value
    }
    return computed(() => sidebarData.value[tokenAddress])
  }

  const isLoading = (tokenAddress: string | 'current') => {
    if (tokenAddress === 'current' && currentTokenAddress.value) {
      tokenAddress = currentTokenAddress.value
    }
    return computed(() => loadingStates.value[tokenAddress] || false)
  }

  const getDynamicTargetInfo = (tokenAddress: string | 'current') => {
    if (tokenAddress === 'current' && currentTokenAddress.value) {
      tokenAddress = currentTokenAddress.value
    }
    return computed(() => dynamicTargets.value[tokenAddress])
  }

  const getPriceVelocity = (tokenAddress: string) => {
    return computed(() => {
      const history = priceVelocityHistory.value[tokenAddress] || []
      if (history.length < 2) return 0

      const recent = history.slice(-5) // Last 5 data points
      if (recent.length < 2) return 0

      const timeSpan = recent[recent.length - 1].timestamp - recent[0].timestamp
      const priceChange = recent[recent.length - 1].price - recent[0].price

      return timeSpan > 0 ? (priceChange / timeSpan) * 60000 : 0 // per minute
    })
  }

  const getCurrentTokenInfo = () => {
    if (currentTokenAddress.value) {
      return computed(
        () => sidebarData.value[currentTokenAddress.value] || null
      )
    }

    return computed(() => null)
  }

  const getVelocityIndicator = (tokenAddress: string) => {
    return computed(() => {
      const velocity = getPriceVelocity(tokenAddress).value
      const dynamicInfo = getDynamicTargetInfo(tokenAddress).value

      if (!dynamicInfo) return 'normal'

      if (dynamicInfo.timeEstimate < 15) return 'high'
      if (dynamicInfo.timeEstimate < 30) return 'medium'
      return 'normal'
    })
  }

  return {
    // State (not readonly since we need to modify them in actions)
    sidebarData,
    getCurrentTokenInfo,
    dynamicTargets,
    loadingStates,

    // Actions
    fetchSidebarData,
    trackPriceVelocity,
    subscribeToUpdates,
    unsubscribeFromUpdates,

    // Getters
    getSidebarData,
    isLoading,
    getDynamicTargetInfo,
    getPriceVelocity,
    getVelocityIndicator
  }
})
