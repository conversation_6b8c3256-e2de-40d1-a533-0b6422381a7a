import tailwindcss from '@tailwindcss/vite'

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: { enabled: true },
  css: ['~/assets/css/main.css'],
  runtimeConfig: {
    comingSoon: process.env.NUXT_COMING_SOON,
    maintenanceMode: process.env.NUXT_MAINTENANCE_MODE,
    mobileComingSoon: process.env.NUXT_MOBILE_COMING_SOON,
    public: {
      comingSoon: process.env.NUXT_COMING_SOON,
      maintenanceMode: process.env.NUXT_MAINTENANCE_MODE,
      mobileComingSoon: process.env.NUXT_MOBILE_COMING_SOON,
      host: process.env.NUXT_HOST || 'localhost',
      rpcEndpoint: process.env.WEB3_PROVIDER_URL || 'http://localhost:8899',
      web3ProgramId:
        process.env.WEB3_PROGRAM ||
        'HEdgfKqP5AbTNRLHK7GwM2vsbYjo1Kb1r1P8cnr7NLP8'
    }
  },
  nitro: {
    experimental: {
      websocket: true
    },
    rollupConfig: {
      external: ['whatwg-url', 'ws', 'helius-sdk']
    },
    plugins: [],
    storage: {
      uploads: {
        driver: 'fs',
        base: './public/uploads'
      }
      // To switch to S3, simply change to:
      // uploads: {
      //   driver: 's3',
      //   bucket: process.env.S3_BUCKET,
      //   accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      //   secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      //   region: process.env.AWS_REGION
      // }
    }
  },
  app: {
    head: {
      title: 'Mazenut',
      meta: [
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { charset: 'utf-8' }
      ],
      link: [
        // Favicons
        {
          rel: 'icon',
          type: 'image/png',
          sizes: '32x32',
          href: '/favicon-32x32.png'
        },
        {
          rel: 'icon',
          type: 'image/png',
          sizes: '16x16',
          href: '/favicon-16x16.png'
        },
        { rel: 'shortcut icon', href: '/favicon.ico' },

        // Apple
        {
          rel: 'apple-touch-icon',
          sizes: '180x180',
          href: '/apple-touch-icon.png'
        },

        // Android Chrome
        {
          rel: 'icon',
          type: 'image/png',
          sizes: '192x192',
          href: '/android-chrome-192x192.png'
        },
        {
          rel: 'icon',
          type: 'image/png',
          sizes: '512x512',
          href: '/android-chrome-512x512.png'
        },

        // Web Manifest
        { rel: 'manifest', href: '/site.webmanifest' }
      ],
      bodyAttrs: {
        class: 'scrollbar overflow-x-hidden'
      }
    }
  },
  watchers: {
    chokidar: {
      ignored: ['**/test-ledger/**']
    }
  },
  vite: {
    plugins: [tailwindcss()],
    optimizeDeps: {
      include: [
        '@solana/web3.js',
        'bn.js',
        'uuid',
        'superstruct',
        'helius-sdk',
        'buffer'
      ],
      exclude: ['react', 'react-dom', 'whatwg-url', 'ws']
    },
    ssr: {
      noExternal: [
        '@coral-xyz/anchor',
        '@solana/web3.js',
        'bn.js',
        'jayson',
        'uuid',
        'superstruct',
        'helius-sdk',
        'buffer'
      ]
    },
    define: {
      global: 'globalThis'
    },
    server: {
      watch: {
        ignored: ['**/test-ledger/**']
      }
    },
    resolve: {
      alias: {
        buffer: 'buffer',
        stream: 'stream-browserify',
        util: 'util',
        '.prisma/client/index-browser':
          './node_modules/.prisma/client/index-browser.js'
      }
    }
  },
  modules: [
    '@nuxt/fonts',
    '@nuxt/icon',
    '@vueuse/nuxt',
    '@prisma/nuxt',
    'nuxt-nodemailer',
    'nuxt-auth-utils',
    '@pinia/nuxt',
    'nuxt-gtag',
    '@nuxt/image',
    '@nuxt/eslint'
  ],
  fonts: {
    fontsource:
      'https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap'
  },
  prisma: {
    generateClient: false
  },
  gtag: {
    enabled: process.env.NODE_ENV === 'production',
    id: 'G-BVP5J46GFX'
  },
  nodemailer: {
    from: '<EMAIL>',
    host: 'smtp.resend.com',
    port: 2465,
    secure: true,
    auth: {
      user: 'resend',
      pass: ''
    }
  }
})
