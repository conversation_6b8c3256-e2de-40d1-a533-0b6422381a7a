// Phantom wallet interface
interface PhantomProvider {
  isPhantom: boolean
  publicKey: PublicKey
  isConnected: boolean
  connect: (opts?: {
    onlyIfTrusted?: boolean
  }) => Promise<{ publicKey: PublicKey }>
  disconnect: () => Promise<void>
  signTransaction: (
    transaction: Transaction | VersionedTransaction
  ) => Promise<Transaction | VersionedTransaction>
  signAllTransactions: (
    transactions: (Transaction | VersionedTransaction)[]
  ) => Promise<(Transaction | VersionedTransaction)[]>
  signMessage: (
    message: Uint8Array | string,
    display?: 'utf8' | 'hex'
  ) => Promise<{ signature: Uint8Array; publicKey: PublicKey }>
  on: (event: string, handler: (args: any) => void) => void
  off: (event: string, handler: (args: any) => void) => void
}

declare module '@coral-xyz/anchor/dist/browser' {
  export * from '@coral-xyz/anchor'
}
