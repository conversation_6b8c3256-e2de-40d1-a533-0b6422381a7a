import type { Trans<PERSON>, <PERSON>L<PERSON>, <PERSON>rism<PERSON>, <PERSON> } from '@prisma/client'

export interface Hashtag {
  id: number
  name: string
}

export interface User {
  id: number
  name: string | null
  displayName?: string | null
  profilePicture?: string | null
  address: string
  email?: string
  createdAt?: Date
  updatedAt?: Date
}

export interface PostMedia {
  id: number
  url: string
  type: string
  mimeType?: string | null
  width?: number | null
  height?: number | null
  size?: number | null
  order: number
}

// export interface Post {
//   id: number
//   content: string
//   createdAt: Date
//   updatedAt: Date
//   user: User
//   media?: PostMedia[]
//   replies?: Post[]
// }

export interface Notification {
  id: number
  type: 'comment' | 'like' | 'follow' | 'mention' | 'share' | 'token'
  content: string
  createdAt: string
  sourceUser: {
    id: number
    username: string
    displayName: string
    avatar?: string
  }
  targetUser?: {
    id: number
    username: string
    displayName: string
  }
  post?: {
    id: number
    content: string
  }
  token?: {
    id: number
    symbol: string
    name: string
  }
  read: boolean
}

export interface Token {
  id: number
  userId: number
  name: string
  symbol: string
  address: string
  curveAddress?: string | null
  createdAt: Date
  updatedAt: Date

  user?: User
  mentions?: Hashtag[]
  transactions?: Transaction[]
  priceLogs?: PriceLog[]
  interactions?: Interaction[]
  notifications?: Notification[]
}

export interface TransactionSerialized {
  id: number
  tokenId: number
  userId: number | null
  type: 'BUY' | 'SELL' | 'TRANSFER' | 'SWAP'
  amountLamports?: string // Amount in lamports (1 SOL = 1_000_000_000 lamports)
  tokenAmount?: string | undefined // Amount in tokens
  solResBefore?: string | undefined // SOL reserves before the transaction
  solResAfter?: string | undefined // SOL reserves after the transaction
  transactionHash?: string
  createdAt?: string

  token?: Token
  user?: Pick<User, keyof User> | null
}

export enum InteractionType {
  LIKE = 'LIKE',
  REPLY = 'REPLY',
  SHARE = 'SHARE',
  REPOST = 'REPOST',
  MENTION = 'MENTION',
  VIEW = 'VIEW',
  BOOKMARK = 'BOOKMARK'
}

export interface Interaction {
  id: number
  userId: number
  postId?: number | null
  tokenId?: number | null
  type: InteractionType
  createdAt: Date
  updatedAt: Date

  user?: User
  post?: Post | null
  token?: Token | null
}

export * from '@prisma/client'

export type PostWithUser = Prisma.PostGetPayload<{
  include: {
    user: true
  }
}>

export type PostWithMedia = Prisma.PostGetPayload<{
  include: {
    media: true
  }
}>

export type PostWithUserAndMedia = Prisma.PostGetPayload<{
  include: {
    user: true
    media: true
  }
}>

export type PostWithRelation = Prisma.PostGetPayload<{
  include: {
    user: true
    media: true
  }
}>

export type PostWithUserAsAuthor = PostWithRelation & { author: User }
export type PostWithRelationAndCounts = PostWithRelation & {
  likeCount: number
  replyCount: number
  shareCount: number
}
