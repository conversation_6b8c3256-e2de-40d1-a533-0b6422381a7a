import type { PostInteraction } from './usePostInteractions'

interface BatchInteractionState {
  registeredPosts: Set<number>
  interactions: Ref<Map<number, PostInteraction>>
  isPolling: boolean
  pollInterval: number
}

const state: BatchInteractionState = {
  registeredPosts: new Set(),
  interactions: ref(new Map()),
  isPolling: false,
  pollInterval: 5000 // 3 seconds
}

let pollTimer: ReturnType<typeof setTimeout> | null = null
let fetchTimer: ReturnType<typeof setTimeout> | null = null
let isFetching = false

export const useBatchInteractions = () => {
  // Register a post for batch interaction updates
  const registerPost = (postId: number) => {
    state.registeredPosts.add(postId)

    // If this is the first post, start polling (which will do initial fetch)
    if (!state.isPolling) {
      startPolling()
    } else {
      // If polling is already running, trigger immediate fetch for new posts
      fetchBatchInteractions()
    }

    // Return current interaction data if available
    return (
      state.interactions.value.get(postId) || {
        liked: false,
        shared: false,
        likeCount: 0,
        replyCount: 0,
        shareCount: 0
      }
    )
  }

  // Unregister a post (when component unmounts)
  const unregisterPost = (postId: number) => {
    state.registeredPosts.delete(postId)
    state.interactions.value.delete(postId)

    // Stop polling if no posts are registered
    if (state.registeredPosts.size === 0) {
      stopPolling()
    }
  }

  // Get current interaction data for a post
  const getPostInteractions = (postId: number): PostInteraction | null => {
    return state.interactions.value.get(postId) || null
  }

  // Fetch interactions for all registered posts (with debouncing)
  const fetchBatchInteractions = async () => {
    if (state.registeredPosts.size === 0 || isFetching) return

    // Clear any pending fetch to debounce rapid calls
    if (fetchTimer) {
      clearTimeout(fetchTimer)
    }

    // Small debounce to batch multiple rapid registrations
    fetchTimer = setTimeout(async () => {
      if (isFetching) return
      isFetching = true

      const postIds = Array.from(state.registeredPosts)

      if (postIds.length === 0) {
        isFetching = false
        return
      }

      try {
        const response = await $fetch<{
          success: boolean
          data: Record<string, PostInteraction>
        }>('/api/posts/batch-interactions', {
          method: 'POST',
          body: { postIds }
        })

        if (response.success && response.data) {
          // Update interactions state and trigger reactivity
          Object.entries(response.data).forEach(([postId, interactions]) => {
            const id = parseInt(postId)
            state.interactions.value.set(id, interactions)
          })
        }
      } catch (error) {
        console.error('Failed to fetch batch interactions:', error)
      } finally {
        isFetching = false
      }
    }, 50) // 50ms debounce - fast enough to feel immediate but prevents spam
  }

  // Start polling for interaction updates
  const startPolling = () => {
    if (state.isPolling) return

    state.isPolling = true

    // Initial fetch
    fetchBatchInteractions()

    // Set up polling
    const poll = () => {
      if (state.registeredPosts.size > 0) {
        fetchBatchInteractions()
        pollTimer = setTimeout(poll, state.pollInterval)
      } else {
        state.isPolling = false
      }
    }

    pollTimer = setTimeout(poll, state.pollInterval)
  }

  // Stop polling
  const stopPolling = () => {
    state.isPolling = false
    if (pollTimer) {
      clearTimeout(pollTimer)
      pollTimer = null
    }
  }

  // Update single post interaction (after user action)
  const updatePostInteraction = (
    postId: number,
    interactions: PostInteraction
  ) => {
    state.interactions.value.set(postId, interactions)
  }

  // Get reactive ref for a specific post's interactions
  const usePostInteractions = (postId: number) => {
    const interactions = ref<PostInteraction>(registerPost(postId))

    // Watch for updates
    const unwatch = watch(
      () => state.interactions.value.get(postId),
      newInteractions => {
        if (newInteractions) {
          interactions.value = { ...newInteractions }
        }
      },
      { deep: true }
    )

    // Cleanup on unmount
    onUnmounted(() => {
      unregisterPost(postId)
      unwatch()
    })

    return {
      interactions: readonly(interactions),
      updateInteractions: (newInteractions: PostInteraction) => {
        updatePostInteraction(postId, newInteractions)
        interactions.value = { ...newInteractions }
      }
    }
  }

  return {
    registerPost,
    unregisterPost,
    getPostInteractions,
    fetchBatchInteractions,
    startPolling,
    stopPolling,
    updatePostInteraction,
    usePostInteractions
  }
}
