export const useInteractionTracking = () => {
  const trackInteraction = async (
    type: 'LIKE' | 'REPLY' | 'SHARE' | 'VIEW' | 'MENTION' | 'BOOKMARK',
    postId?: number,
    tokenId?: number
  ) => {
    try {
      await $fetch('/api/interactions/track', {
        method: 'POST',
        body: {
          type,
          postId,
          tokenId
        }
      })
    } catch (error) {
      console.error('Failed to track interaction:', error)
      // Fail silently for analytics
    }
  }

  const trackView = (postId?: number, tokenId?: number) => {
    trackInteraction('VIEW', postId, tokenId)
  }

  const trackLike = (postId: number) => {
    trackInteraction('LIKE', postId)
  }

  const trackReply = (postId: number) => {
    trackInteraction('REPLY', postId)
  }

  const trackShare = (postId: number) => {
    trackInteraction('SHARE', postId)
  }

  const trackBookmark = (postId: number) => {
    trackInteraction('BOOKMARK', postId)
  }

  const trackTokenView = (tokenId: number) => {
    trackInteraction('VIEW', undefined, tokenId)
  }

  return {
    trackInteraction,
    trackView,
    trackLike,
    trackReply,
    trackShare,
    trackBookmark,
    trackTokenView
  }
}
