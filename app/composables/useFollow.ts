export type FollowError = {
  data: {
    statusCode: number
    statusMessage: string
  }
}

export const useFollow = () => {
  const toast = useToast()

  const isLoading = ref(false)

  const toggleFollow = async (userId: number, currentlyFollowing: boolean) => {
    if (isLoading.value) return

    isLoading.value = true

    try {
      const response = await $fetch<{
        success: boolean
        following: boolean
        message: string
      }>('/api/user/follow', {
        method: 'POST',
        body: { userId }
      })

      if (response.success) {
        return response.following
      }
    } catch (error: unknown) {
      console.error('Failed to toggle follow:', error)

      const errorMessage =
        (error as FollowError).data?.statusMessage ||
        'Failed to update follow status'
      toast.error(errorMessage)

      return currentlyFollowing
    } finally {
      isLoading.value = false
    }
  }

  return {
    toggleFollow,
    isLoading: readonly(isLoading)
  }
}
