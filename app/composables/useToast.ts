interface ToastItem {
  id: string
  title: string
  description?: string
  type?: 'success' | 'error' | 'warning' | 'info'
  duration?: number
  action?: {
    label: string
    onClick: () => void
  }
}

interface ToastState {
  toasts: ToastItem[]
}

// Global state for toasts
const toastState = reactive<ToastState>({
  toasts: []
})

export const useToast = () => {
  const addToast = (toast: Omit<ToastItem, 'id'>) => {
    const id = Math.random().toString(36).substring(2, 9)
    const newToast: ToastItem = {
      id,
      duration: 5000,
      type: 'info',
      ...toast
    }

    toastState.toasts.push(newToast)

    // Auto-remove toast after duration
    if (newToast.duration && newToast.duration > 0) {
      setTimeout(() => {
        removeToast(id)
      }, newToast.duration)
    }

    return id
  }

  const removeToast = (id: string) => {
    const index = toastState.toasts.findIndex(toast => toast.id === id)
    if (index > -1) {
      toastState.toasts.splice(index, 1)
    }
  }

  const clearAllToasts = () => {
    toastState.toasts.length = 0
  }

  // Convenience methods
  const success = (
    title: string,
    description?: string,
    options?: Partial<ToastItem>
  ) => {
    return addToast({
      title,
      description,
      type: 'success',
      ...options
    })
  }

  const error = (
    title: string,
    description?: string,
    options?: Partial<ToastItem>
  ) => {
    return addToast({
      title,
      description,
      type: 'error',
      duration: 0, // Error toasts don't auto-close by default
      ...options
    })
  }

  const warning = (
    title: string,
    description?: string,
    options?: Partial<ToastItem>
  ) => {
    return addToast({
      title,
      description,
      type: 'warning',
      ...options
    })
  }

  const info = (
    title: string,
    description?: string,
    options?: Partial<ToastItem>
  ) => {
    return addToast({
      title,
      description,
      type: 'info',
      ...options
    })
  }

  return {
    // State
    toasts: readonly(toastState.toasts),

    // Methods
    addToast,
    removeToast,
    clearAllToasts,

    // Convenience methods
    success,
    error,
    warning,
    info
  }
}

// Export types for use in components
export type { ToastItem }
