// Helper composables for specific contexts

export const useTransactions = (filters?: {
  tokenAddress?: string
  userId?: number
  type?: string
}) => {
  return useWebSocket('transactions', filters)
}

export const useHolders = (tokenAddress: string) => {
  return useWebSocket('holders', { tokenAddress })
}

export const useMarketCap = (tokenAddress?: string) => {
  return useWebSocket('marketcap', tokenAddress ? { tokenAddress } : undefined)
}

export const usePrice = (tokenAddress?: string) => {
  return useWebSocket('price', tokenAddress ? { tokenAddress } : undefined)
}

export const usePosts = (tokenSymbol?: string) => {
  return useWebSocket('posts', tokenSymbol ? { tokenSymbol } : undefined)
}

export const useTokensComp = (filters?: {}) => {
  return useWebSocket('tokens', filters)
}

export const useSolanaWs = (filters?: { event: string }) => {
  return useWebSocket('solana', filters)
}

// Utility functions for working with WebSocket data

export const formatTimeAgo = (dateString: string): string => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000)

  if (diffInSeconds < 60) return `${diffInSeconds}s ago`
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`
  return `${Math.floor(diffInSeconds / 86400)}d ago`
}

export const formatNumber = (num: number, decimals: number = 2): string => {
  if (num >= 1e9) return `${(num / 1e9).toFixed(decimals)}B`
  if (num >= 1e6) return `${(num / 1e6).toFixed(decimals)}M`
  if (num >= 1e3) return `${(num / 1e3).toFixed(decimals)}K`
  return num.toFixed(decimals)
}

export const formatCurrency = (
  amount: number,
  currency: string = 'SOL'
): string => {
  return `${formatNumber(amount)} ${currency}`
}

export const formatAddress = (address: string, length: number = 8): string => {
  if (address.length <= length) return address
  const start = Math.floor(length / 2)
  const end = length - start
  return `${address.slice(0, start)}...${address.slice(-end)}`
}
