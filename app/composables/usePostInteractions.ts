export interface PostInteraction {
  liked: boolean
  shared: boolean
  likeCount: number
  replyCount: number
  shareCount: number
}

export interface PostReply {
  id: number
  content: string
  createdAt: string
  likeCount: number
  replyCount: number
  shareCount: number
  user: {
    id: number
    name: string | null
    address: string | null
  }
}

export const usePostInteractions = () => {
  // Like/unlike a post
  const toggleLike = async (postId: number) => {
    try {
      const response = await $fetch(`/api/posts/${postId}/like`, {
        method: 'POST'
      })

      return response
    } catch (error) {
      console.error('Error toggling like:', error)
      throw error
    }
  }

  // Share/unshare a post
  const toggleShare = async (postId: number) => {
    try {
      const response = await $fetch(`/api/posts/${postId}/share`, {
        method: 'POST'
      })

      return response
    } catch (error) {
      console.error('Error toggling share:', error)
      throw error
    }
  }

  // Add a reply to a post
  const addReply = async (postId: number, content: string) => {
    try {
      const response = await $fetch(`/api/posts/${postId}/reply`, {
        method: 'POST',
        body: { content }
      })

      return response
    } catch (error) {
      console.error('Error adding reply:', error)
      throw error
    }
  }

  // Get replies for a post
  const getReplies = async (
    postId: number,
    limit: number = 10,
    offset: number = 0
  ): Promise<PostReply[]> => {
    try {
      const response = await $fetch(`/api/posts/${postId}/replies`, {
        query: { limit, offset }
      })

      return response.data || []
    } catch (error) {
      console.error('Error getting replies:', error)
      return []
    }
  }

  // Get interaction status for a post
  const getInteractionStatus = async (
    postId: number
  ): Promise<PostInteraction> => {
    try {
      const response = await $fetch(`/api/posts/${postId}/interactions`)

      return response.data
    } catch (error) {
      console.error('Error getting interaction status:', error)
      return {
        liked: false,
        shared: false,
        likeCount: 0,
        replyCount: 0,
        shareCount: 0
      }
    }
  }

  // Trigger confetti animation
  const triggerConfetti = (element: HTMLElement) => {
    // Create confetti elements
    const confettiCount = 12
    const colors = [
      '#ff6b6b',
      '#4ecdc4',
      '#45b7d1',
      '#96ceb4',
      '#feca57',
      '#ff9ff3'
    ]

    // Get the button's bounding rect for positioning
    const rect = element.getBoundingClientRect()
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2

    for (let i = 0; i < confettiCount; i++) {
      const confetti = document.createElement('div')
      confetti.style.cssText = `
        position: fixed;
        width: 6px;
        height: 6px;
        background: ${colors[Math.floor(Math.random() * colors.length)]};
        border-radius: 50%;
        pointer-events: none;
        z-index: 10000;
        left: ${centerX}px;
        top: ${centerY}px;
        opacity: 1;
      `

      // Random direction and speed
      const angle = Math.random() * 360 * (Math.PI / 180)
      const velocity = Math.random() * 50 + 30
      const vx = Math.cos(angle) * velocity
      const vy = Math.sin(angle) * velocity

      // Animate using transform
      let startTime: number
      const duration = 800

      const animate = (currentTime: number) => {
        if (!startTime) startTime = currentTime
        const elapsed = currentTime - startTime
        const progress = Math.min(elapsed / duration, 1)

        // Calculate position
        const x = vx * progress
        const y = vy * progress + progress * progress * 100 // Add gravity

        // Calculate opacity fade
        const opacity = 1 - progress

        confetti.style.transform = `translate(${x}px, ${y}px) rotate(${progress * 720}deg)`
        confetti.style.opacity = opacity.toString()

        if (progress < 1) {
          requestAnimationFrame(animate)
        } else {
          confetti.remove()
        }
      }

      document.body.appendChild(confetti)
      requestAnimationFrame(animate)
    }
  }

  return {
    toggleLike,
    toggleShare,
    addReply,
    getReplies,
    getInteractionStatus,
    triggerConfetti
  }
}

// Add basic styles for smooth transitions
if (import.meta.client) {
  const style = document.createElement('style')
  style.textContent = `
    .fill-current {
      fill: currentColor;
    }
  `
  if (!document.head.querySelector('style[data-confetti-styles]')) {
    style.setAttribute('data-confetti-styles', 'true')
    document.head.appendChild(style)
  }
}
