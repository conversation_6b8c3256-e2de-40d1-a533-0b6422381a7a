export type WebSocketContext =
  | 'transactions'
  | 'holders'
  | 'marketcap'
  | 'price'
  | 'posts'
  | 'tokens'
  | 'solana'

export interface WebSocketMessage {
  type:
    | 'subscribe'
    | 'unsubscribe'
    | 'data'
    | 'error'
    | 'ping'
    | 'pong'
    | 'refresh'
  context?: WebSocketContext
  data?: any
  filters?: Record<string, any>
}

export interface TransactionData {
  id: number
  userId: number
  tokenId: number
  amount: number
  solAmount?: number
  transactionHash: string
  type: 'BUY' | 'SELL' | 'TRANSFER' | 'SWAP'
  isOwner: boolean
  isBot: boolean
  createdAt: string
  user: {
    id: number
    name?: string
    address: string
  }
  token: {
    id: number
    name: string
    symbol: string
    address: string
  }
}

export interface HolderData {
  userId: number
  tokenId: number
  balance: number
  percentage: number
  user: {
    id: number
    name?: string
    address: string
  }
}

export interface MarketCapData {
  tokenId: number
  marketCap: string
  percentage: number
  targetMarketCapSol: number
  isNearMigration: boolean
  tokenAddress: string
}

export interface PriceData {
  tokenId: number
  price: number
  priceChange24h?: number
  volume24h?: number
  priceVelocity?: number
  timestamp: string
  tokenAddress: string
  tokenSymbol?: string
  tokenName?: string
  marketCap?: {
    real: {
      lamports: string
      sol: number
      formatted: string
    }
    target: {
      lamports: string
      sol: number
      base: number
      dynamic: number
      formatted: string
    }
    progress: {
      percentage: number
      isNearMigration: boolean
    }
    sidebarData?: any // Full sidebar data for dynamic calculations
  }
  recentTransactionCount?: number
  metadata?: {
    hasRecentActivity: boolean
    avgTransactionSize: number
    lastTransactionTime: string | null
    priceChangeIndicators?: {
      priceVelocityPerMinute: number
      transactionFrequency: number
      volumeVelocity: number
    }
  }
}

export interface PostData {
  id: number
  content: string
  userId: number
  likeCount: number
  commentCount: number
  createdAt: string
  user: {
    id: number
    name?: string
    address: string
  }
  mentions: Array<{
    id: number
    token?: {
      id: number
      name: string
      symbol: string
      address: string
    }
    hashtag?: {
      id: number
      name: string
    }
    user?: {
      id: number
      name?: string
      address: string
    }
  }>
}

export interface WebSocketContextData {
  transactions: TransactionData[]
  holders: HolderData[]
  marketcap: MarketCapData[]
  price: PriceData[]
  posts: PostData[]
  tokens: Token[]
  solana: any
}

// Global WebSocket connection manager
export class WebSocketManager {
  private static instance: WebSocketManager
  private ws: WebSocket | null = null
  private isConnected = ref(false)
  private subscribers = new Map<string, Set<any>>()
  private contextData = reactive<WebSocketContextData>({
    transactions: [],
    holders: [],
    marketcap: [],
    price: [],
    posts: [],
    tokens: [],
    solana: {}
  })

  // Reconnection logic
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private reconnectDelay = 1000
  private heartbeatInterval: NodeJS.Timeout | null = null
  private reconnectTimeout: NodeJS.Timeout | null = null

  static getInstance(): WebSocketManager {
    if (!WebSocketManager.instance) {
      WebSocketManager.instance = new WebSocketManager()
    }
    return WebSocketManager.instance
  }

  private constructor() {
    // Private constructor for singleton
  }

  connect() {
    if (
      this.ws &&
      (this.ws.readyState === WebSocket.OPEN ||
        this.ws.readyState === WebSocket.CONNECTING)
    ) {
      return // Already connected or connecting
    }

    // Don't connect on server side
    if (typeof window === 'undefined') {
      return
    }

    try {
      // Clear any potentially corrupted subscriptions on fresh connection
      this.clearCorruptedSubscriptions()

      // Get WebSocket URL based on current location
      const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const wsHost = window.location.host
      const wsUrl = `${wsProtocol}//${wsHost}/api/ws`

      this.ws = new WebSocket(wsUrl)

      this.ws.onopen = () => {
        this.isConnected.value = true
        this.reconnectAttempts = 0
        this.reconnectDelay = 1000

        // Resubscribe all active contexts after a small delay to ensure connection is stable
        setTimeout(() => {
          this.resubscribeAll()
        }, 100)
        this.startHeartbeat()
      }

      this.ws.onmessage = event => {
        try {
          // Check if data is a string and not empty
          if (typeof event.data !== 'string' || event.data.trim() === '') {
            console.warn(
              '[WebSocketManager] Received empty or non-string message:',
              event.data
            )
            return
          }

          // Additional check for minimum JSON structure
          if (
            event.data.length < 2 ||
            !event.data.startsWith('{') ||
            !event.data.endsWith('}')
          ) {
            console.warn(
              '[WebSocketManager] Received malformed JSON:',
              event.data
            )
            return
          }

          const message: WebSocketMessage = JSON.parse(event.data)
          this.handleMessage(message)
        } catch (err) {
          console.error('[WebSocketManager] Failed to parse message:', err)
          console.error(
            '[WebSocketManager] Raw data that failed to parse:',
            event.data
          )
        }
      }

      this.ws.onclose = event => {
        this.isConnected.value = false
        this.stopHeartbeat()

        if (
          event.code !== 1000 &&
          this.reconnectAttempts < this.maxReconnectAttempts
        ) {
          this.scheduleReconnect()
        }
      }

      this.ws.onerror = event => {
        console.error('[WebSocketManager] Error:', event)
      }
    } catch (err) {
      console.error('[WebSocketManager] Failed to create WebSocket:', err)
    }
  }

  subscribe(
    context: WebSocketContext,
    filters: Record<string, any> | undefined,
    subscriber: any
  ) {
    const key = this.getSubscriptionKey(context, filters)

    if (!this.subscribers.has(key)) {
      this.subscribers.set(key, new Set())
    }
    this.subscribers.get(key)!.add(subscriber)

    // Send subscription message if connected and ready
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      try {
        const message: WebSocketMessage = {
          type: 'subscribe',
          context,
          filters
        }
        this.ws.send(JSON.stringify(message))
      } catch (error) {
        console.error(
          '[WebSocketManager] Error sending subscribe message:',
          error
        )
      }
    }
  }

  unsubscribe(
    context: WebSocketContext,
    filters: Record<string, any> | undefined,
    subscriber: any
  ) {
    const key = this.getSubscriptionKey(context, filters)
    const subs = this.subscribers.get(key)

    if (subs) {
      subs.delete(subscriber)

      // If no more subscribers for this context/filter combination, unsubscribe
      if (subs.size === 0) {
        this.subscribers.delete(key)

        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
          const message: WebSocketMessage = {
            type: 'unsubscribe',
            context
          }
          try {
            this.ws.send(JSON.stringify(message))
          } catch (error) {
            console.error(
              '[WebSocketManager] Error sending unsubscribe message:',
              error
            )
          }
        }
      }
    }
  }

  refresh(context: WebSocketContext, filters?: Record<string, any>) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      const message: WebSocketMessage = {
        type: 'refresh',
        context,
        filters
      }
      try {
        this.ws.send(JSON.stringify(message))
      } catch (error) {
        console.error(
          '[WebSocketManager] Error sending refresh message:',
          error
        )
      }
    } else {
      console.warn('[WebSocketManager] Cannot refresh: WebSocket not connected')
    }
  }

  private getSubscriptionKey(
    context: WebSocketContext,
    filters?: Record<string, any>
  ): string {
    try {
      const filtersStr = JSON.stringify(filters || {})
      return `${context}:${filtersStr}`
    } catch (error) {
      console.warn(
        '[WebSocketManager] Failed to stringify filters, using empty object:',
        error
      )
      return `${context}:{}`
    }
  }

  private resubscribeAll() {
    for (const key of this.subscribers.keys()) {
      try {
        const parts = key.split(':')
        if (parts.length < 2) {
          console.warn(
            '[WebSocketManager] Invalid subscription key format:',
            key
          )
          continue
        }

        const context = parts[0]
        const filtersStr = parts.slice(1).join(':') // Handle case where filters contain ':'

        let filters: Record<string, any> | undefined
        try {
          const parsedFilters = JSON.parse(filtersStr)
          filters =
            Object.keys(parsedFilters).length > 0 ? parsedFilters : undefined
        } catch (parseError) {
          console.warn(
            '[WebSocketManager] Failed to parse filters for key:',
            key,
            parseError
          )
          filters = undefined
        }

        const message: WebSocketMessage = {
          type: 'subscribe',
          context: context as WebSocketContext,
          filters
        }

        if (this.ws && this.ws.readyState === WebSocket.OPEN) {
          try {
            this.ws.send(JSON.stringify(message))
          } catch (sendError) {
            console.error(
              '[WebSocketManager] Error sending resubscribe message:',
              sendError
            )
          }
        }
      } catch (error) {
        console.error(
          '[WebSocketManager] Error resubscribing to key:',
          key,
          error
        )
      }
    }
  }

  private handleMessage(message: WebSocketMessage) {
    switch (message.type) {
      case 'data':
        if (message.context && message.data) {
          // Only process data for valid WebSocket contexts, ignore connection messages
          const validContexts: WebSocketContext[] = [
            'transactions',
            'holders',
            'marketcap',
            'price',
            'posts',
            'tokens',
            'solana'
          ]

          if (validContexts.includes(message.context as WebSocketContext)) {
            if (message.context === 'solana') {
              // TODO: ignore status message
            }
            this.updateContextData(
              message.context as WebSocketContext,
              message.data
            )
          } else {
          }
        }
        break
      case 'error':
        console.error('[WebSocketManager] Server error:', message.data)
        break
      case 'pong':
        // Heartbeat response
        break
    }
  }

  private updateContextData(context: WebSocketContext, data: any) {
    switch (context) {
      case 'transactions':
        if (Array.isArray(data)) {
          this.contextData.transactions = [
            ...data,
            ...this.contextData.transactions
          ].slice(0, 100)
        } else {
          this.contextData.transactions = [
            data,
            ...this.contextData.transactions
          ].slice(0, 100)
        }
        break

      case 'posts':
        if (Array.isArray(data)) {
          this.contextData.posts = [...data, ...this.contextData.posts].slice(
            0,
            50
          )
        } else {
          this.contextData.posts = [data, ...this.contextData.posts].slice(
            0,
            50
          )
        }
        break

      case 'price':
      case 'marketcap':
        this.contextData[context] = Array.isArray(data) ? data : [data]
        break

      case 'holders':
        this.contextData.holders = data
        break

      case 'tokens':
        if (Array.isArray(data)) {
          this.contextData.tokens = data
        } else {
          // New token created - add to the beginning of the list
          this.contextData.tokens = [data, ...this.contextData.tokens].slice(
            0,
            100
          )
        }
        break
    }
  }

  private startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        try {
          const message: WebSocketMessage = { type: 'ping' }
          this.ws.send(JSON.stringify(message))
        } catch (error) {
          console.error('[WebSocketManager] Error sending heartbeat:', error)
        }
      }
    }, 30000)
  }

  private stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }

  private scheduleReconnect() {
    this.reconnectAttempts++
    const delay = Math.min(
      this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1),
      30000
    )

    this.reconnectTimeout = setTimeout(() => {
      this.connect()
    }, delay)
  }

  private clearCorruptedSubscriptions() {
    // Check all subscription keys and remove any that can't be parsed
    const keysToRemove: string[] = []

    for (const key of this.subscribers.keys()) {
      try {
        const parts = key.split(':')
        if (parts.length < 2) {
          keysToRemove.push(key)
          continue
        }

        const filtersStr = parts.slice(1).join(':')
        JSON.parse(filtersStr) // Test if it can be parsed
      } catch (error) {
        console.warn(
          '[WebSocketManager] Removing corrupted subscription key:',
          key
        )
        keysToRemove.push(key)
      }
    }

    keysToRemove.forEach(key => this.subscribers.delete(key))
  }

  getContextData<T extends WebSocketContext>(
    context: T
  ): WebSocketContextData[T] {
    return this.contextData[context] as WebSocketContextData[T]
  }

  getConnectionStatus() {
    return {
      isConnected: readonly(this.isConnected),
      reconnectAttempts: this.reconnectAttempts
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close(1000, 'Manual disconnect')
    }
    this.stopHeartbeat()
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }
    this.subscribers.clear()
  }
}

export const useWebSocket = <T extends WebSocketContext>(
  context: T,
  filters?: Record<string, any>
) => {
  const manager = WebSocketManager.getInstance()
  const isLoading = ref(true)
  const error = ref<string | null>(null)

  // Get reactive data for this context
  const data = computed(() => manager.getContextData(context))
  const { isConnected } = manager.getConnectionStatus()

  // Auto-connect and subscribe on creation (client-side only)
  onMounted(() => {
    // Double-check we're on client side
    if (typeof window !== 'undefined') {
      manager.connect()
      manager.subscribe(context, filters, {})
    }
    isLoading.value = false
  })

  // Unsubscribe on unmount
  onUnmounted(() => {
    if (typeof window !== 'undefined') {
      manager.unsubscribe(context, filters, {})
    }
  })

  const refresh = () => {
    if (typeof window !== 'undefined') {
      isLoading.value = true
      manager.refresh(context, filters)
      isLoading.value = false
    }
  }

  const updateFilters = (newFilters: Record<string, any>) => {
    if (typeof window !== 'undefined') {
      manager.unsubscribe(context, filters, {})
      filters = newFilters
      manager.subscribe(context, filters, {})
    }
  }

  return {
    // State
    data,
    isConnected,
    isLoading: readonly(isLoading),
    error: readonly(error),

    // Methods
    refresh,
    updateFilters,

    // Context info
    context
  }
}
