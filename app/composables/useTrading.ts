import type { BN } from '@coral-xyz/anchor/dist/browser'

enum TradingMessageType {
  // initial connection message
  CONNECTED,
  // price and mcap data only (eg in the trading for)
  TOKEN_PRICE,
  // token info data (eg in the sidebar)
  TOKEN_INFO,
  // token activity data (eg transactions and holders)
  TOKEN_ACTIVITY,
  // buy token request
  TOKEN_BUY,
  // sell token request
  TOKEN_SELL
}

interface TokenPriceData {
  timestamp?: number // Timestamp of the price data
  priceLamports?: number // Price in lamports
  priceUSD?: number // Price in USD
  priceSol?: number // Price in SOL
  mcap?: number // Current market cap in USD
  currentSolPrice?: number // Current SOL price in USD
}

export interface TokenInfo {
  address: string // Token address
  curveAdress?: string // Curve address for the token
  name?: string // Token name
  symbol?: string // Token symbol
  decimals?: number // Number of decimals
  currentPrice?: TokenPriceData
  targetMcap: number // Target market cap in USD
  volume24h?: number // 24-hour trading volume in USD
  circulatingSupply?: number // Circulating supply of the token
  totalSupply?: number // Total supply of the token should be $1_000_000_000
  solReserves?: number // SOL reserves for bonding curve
  tokenReserves?: number // Token reserves for bonding curve
}

interface TradingMessage {
  type: TradingMessageType
  token: string
  data: any
}

interface TradingOptions {
  includePriceData?: boolean // Whether to include price data in the response
  token: string // Token address to trade
  reconnectInterval?: number // Interval in milliseconds to attempt reconnection
  maxReconnectAttempts?: number // Maximum number of reconnection attempts
  reconnectDelay?: number // Delay in milliseconds before each reconnection attempt
  endpoint?: string // WebSocket endpoint for trading data
  onMessage?: (message: any) => void // Callback for handling incoming messages
  onError?: (error: Event) => void // Callback for handling errors
  onClose?: () => void // Callback for handling connection closure
}

export default function useTrading (options: TradingOptions) {
  const ws = ref<WebSocket | null>(null)
  const tokenInfo = ref<TokenInfo | null>(null)
  const userBalance = ref<BN | null>(null)
  const tokenInfoGetter = computed(() => tokenInfo.value)
  const pingInterval = ref<NodeJS.Timeout | null>(null)

  onMounted(() => {
    if (options.endpoint) {
      ws.value = new WebSocket(options.endpoint)

      ws.value.onopen = () => {
        // ping interval
        pingInterval.value = setInterval(() => {
          if (ws.value?.readyState === WebSocket.OPEN) {
            ws.value.send(JSON.stringify({ type: 'ping' }))
          }
        }, 30000) // Ping every 30 seconds
      }

      ws.value.onmessage = event => {
        const message: TradingMessage = JSON.parse(event.data)
        if (message.token === options.token) {
          tokenInfo.value = message.data as TokenInfo
          options.onMessage?.(tokenInfo.value)
        }

        if (message.type === TradingMessageType.CONNECTED) {
          // send token info request
          ws.value?.send(
            JSON.stringify({
              type: TradingMessageType.TOKEN_INFO,
              token: options.token,
              data: {}
            })
          )
        }
      }

      ws.value.onerror = error => {
        console.error('WebSocket error:', error)
        options.onError?.(error)
      }

      ws.value.onclose = () => {
        console.log('WebSocket connection closed')
        options.onClose?.()
      }
    }
  })

  // Function to send buy token notification
  const notifyTokenBuy = (amount: number, actualPrice?: number) => {
    if (ws.value?.readyState === WebSocket.OPEN) {
      ws.value.send(
        JSON.stringify({
          type: TradingMessageType.TOKEN_BUY,
          token: options.token,
          data: {
            amount,
            actualPrice,
            timestamp: Date.now()
          }
        })
      )
    } else {
      console.warn('WebSocket is not connected')
    }
  }

  // Function to send sell token notification
  const notifyTokenSell = (amount: number, actualPrice?: number) => {
    if (ws.value?.readyState === WebSocket.OPEN) {
      ws.value.send(
        JSON.stringify({
          type: TradingMessageType.TOKEN_SELL,
          token: options.token,
          data: {
            amount,
            actualPrice,
            timestamp: Date.now()
          }
        })
      )
    } else {
      console.warn('WebSocket is not connected')
    }
  }

  // Function to request fresh token info
  const requestTokenInfo = () => {
    if (ws.value?.readyState === WebSocket.OPEN) {
      ws.value.send(
        JSON.stringify({
          type: TradingMessageType.TOKEN_INFO,
          token: options.token,
          data: {}
        })
      )
    } else {
      console.warn('WebSocket is not connected, cannot request token info')
    }
  }

  onUnmounted(() => {
    console.log('Cleaning up WebSocket connection')
    if (ws.value && ws.value.readyState === WebSocket.OPEN) {
      ws.value.close()
      ws.value = null
    }
    if (pingInterval.value) {
      clearInterval(pingInterval.value)
      pingInterval.value = null
    }
  })

  return {
    tokenInfo: tokenInfoGetter,
    balance: computed(() => userBalance.value),
    notifyTokenBuy,
    notifyTokenSell,
    requestTokenInfo
  }
}
