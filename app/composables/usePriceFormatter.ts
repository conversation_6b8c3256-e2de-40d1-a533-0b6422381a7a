export default function usePriceFormatter () {
  // Subscript table for digits and common symbols
  const subscriptTable: { [key: string]: string } = {
    '0': '₀',
    '1': '₁',
    '2': '₂',
    '3': '₃',
    '4': '₄',
    '5': '₅',
    '6': '₆',
    '7': '₇',
    '8': '₈',
    '9': '₉',
    '-': '₋',
    '+': '₊',
    '=': '₌',
    '(': '₍',
    ')': '₎',
    '.': '.' // No dedicated subscript dot, fallback to period
  }

  // Superscript table for digits and common symbols
  const superscriptTable: { [key: string]: string } = {
    '0': '⁰',
    '1': '¹',
    '2': '²',
    '3': '³',
    '4': '⁴',
    '5': '⁵',
    '6': '⁶',
    '7': '⁷',
    '8': '⁸',
    '9': '⁹',
    '-': '⁻',
    '+': '⁺',
    '=': '⁼',
    '(': '⁽',
    ')': '⁾',
    '.': '·' // Use middle dot as a superscript decimal
  }

  // Subscript function using table
  const subscript = (num: number | string) => {
    return num
      .toString()
      .split('')
      .map(ch => subscriptTable[ch] || ch)
      .join('')
  }

  // Superscript function using table
  const superscript = (num: number | string) => {
    return num
      .toString()
      .split('')
      .map(ch => superscriptTable[ch] || ch)
      .join('')
  }

  // Modified: Show the count of zeros in subscript, then a '0', then the rest
  const formatUsdPriceSub = (price: number) => {
    if (isNaN(price)) return '0'
    const [integerPart, fractionalPart] = price.toFixed(8).split('.')
    const leadingZeros = fractionalPart.match(/^0+/)
    if (leadingZeros) {
      // Show count of zeros as subscript, then a literal zero, then rest
      const count = leadingZeros[0].length
      const rest = fractionalPart.slice(count)
      return `${integerPart}.0${superscript(count)}${rest}`
    } else {
      // No leading zeros, just return as normal
      return `${integerPart}.${fractionalPart}`
    }
  }

  return {
    subscript,
    superscript,
    formatUsdPriceSub
  }
}
