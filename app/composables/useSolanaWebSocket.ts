interface SolanaWsMessage {
  type: 'subscribe' | 'unsubscribe' | 'data' | 'error' | 'ping' | 'pong'
  tokenAddress?: string
  data?: any
}

interface CurveUpdate {
  tokenAddress: string
  price: number
  solReserves: string
  tokenReserves: string
  currentPriceLamports: number
  actualLastPrice: number
  actualLastPriceLamports: number
  timestamp: string
  source: string
}

// Global state to ensure singleton behavior
const globalSocket = ref<WebSocket | null>(null)
const globalIsConnected = ref(false)
const globalSubscribedTokens = ref<Set<string>>(new Set())
const globalCurveUpdates = ref<Map<string, CurveUpdate>>(new Map())
const globalCurveUpdateCallbacks = ref<
  Map<string, Set<(update: CurveUpdate) => void>>
>(new Map())
const globalInitialized = ref(false)
let globalPingInterval: ReturnType<typeof setInterval> | null = null

export const useSolanaWebSocket = () => {
  // Use global refs to ensure singleton behavior
  const socket = globalSocket
  const isConnected = globalIsConnected
  const subscribedTokens = globalSubscribedTokens
  const curveUpdates = globalCurveUpdates
  const curveUpdateCallbacks = globalCurveUpdateCallbacks

  // Helper function to refresh user balance when curve updates
  const refreshUserBalanceForToken = async (tokenAddress: string) => {
    try {
      // Only refresh if we're on client side and have a connected wallet
      if (import.meta.server) return

      const { useFinance } = await import('~/stores/finance')
      const financeStore = useFinance()

      await financeStore.refreshTokenBalance(tokenAddress)

      // Emit a global event for UI components to react to balance changes
      if (typeof window !== 'undefined') {
        const balanceUpdateEvent = new CustomEvent('tokenBalanceUpdated', {
          detail: { tokenAddress }
        })
        window.dispatchEvent(balanceUpdateEvent)
      }
    } catch (error) {
      console.error(
        `Failed to refresh user balance for ${tokenAddress}:`,
        error
      )
    }
  }

  const connect = () => {
    // Only connect on client side
    if (import.meta.server) {
      return
    }

    if (socket.value?.readyState === WebSocket.OPEN) {
      return
    }

    if (socket.value?.readyState === WebSocket.CONNECTING) {
      return
    }

    try {
      const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
      const wsUrl = `${protocol}//${window.location.host}/api/ws/sol-ws`

      socket.value = new WebSocket(wsUrl)

      socket.value.onopen = () => {
        console.log('🟢 Solana connected')
        isConnected.value = true

        // Re-subscribe to previously subscribed tokens
        const tokensToSubscribe = Array.from(subscribedTokens.value)
        subscribedTokens.value.clear() // Clear first to avoid duplicate detection

        for (const tokenAddress of tokensToSubscribe) {
          subscribeToToken(tokenAddress)
        }
      }

      socket.value.onmessage = event => {
        try {
          const message: SolanaWsMessage = JSON.parse(event.data)
          handleMessage(message)
        } catch (error) {
          console.error('Error parsing Solana WebSocket message:', error)
        }
      }

      socket.value.onclose = event => {
        console.log('🔴 Solana disconnected (reason: ' + event.reason + ')')
        isConnected.value = false

        // Auto-reconnect after delay
        setTimeout(() => {
          connect()
        }, 3000)
      }

      socket.value.onerror = error => {
        console.error('Solana WebSocket error:', error)
      }
    } catch (error) {
      console.error('Failed to create Solana WebSocket connection:', error)
    }
  }

  const handleMessage = async (message: SolanaWsMessage) => {
    switch (message.type) {
      case 'data':
        if (message.tokenAddress && message.data) {
          if (message.data.status === 'subscribed') {
          } else if (message.data.source === 'solana_ws_curve_update') {
            // This is a curve account update from Solana

            // Store the curve update
            curveUpdates.value.set(
              message.tokenAddress,
              message.data as CurveUpdate
            )

            // Call registered callbacks for this token
            const callbacks = curveUpdateCallbacks.value.get(
              message.tokenAddress
            )
            if (callbacks) {
              callbacks.forEach(callback => {
                try {
                  callback(message.data as CurveUpdate)
                } catch (error) {
                  console.error('Error in curve update callback:', error)
                }
              })
            }

            // Auto-refresh user balance when curve updates (indicates buy/sell activity)
            await refreshUserBalanceForToken(message.tokenAddress)
          }
        } else if (message.data?.status === 'connected') {
          // DO NOTHING FOR NOW
        }
        break

      case 'error':
        console.error('Solana WebSocket error:', message.data)
        break

      case 'pong':
        // Keep-alive response
        break
    }
  }

  const refreshTokenData = async (tokenAddress: string) => {
    try {
      // This method can be called by components to trigger a refresh
      // Components can use this to update their main WebSocket subscriptions
      return true
    } catch (error) {
      console.error(`Failed to refresh token data for ${tokenAddress}:`, error)
      return false
    }
  }

  const subscribeToToken = (tokenAddress: string) => {
    // Skip on server side
    if (import.meta.server) {
      return
    }

    // already subscribed or no address: skip
    if (!tokenAddress || subscribedTokens.value.has(tokenAddress)) {
      return
    }

    if (socket.value?.readyState === WebSocket.OPEN) {
      const message: SolanaWsMessage = {
        type: 'subscribe',
        tokenAddress
      }

      socket.value.send(JSON.stringify(message))
      subscribedTokens.value.add(tokenAddress)
    } else {
      // Store for later subscription when connected
      subscribedTokens.value.add(tokenAddress)
      if (!isConnected.value) {
        connect()
      }
    }
  }

  const unsubscribeFromToken = (tokenAddress: string) => {
    if (!tokenAddress || !subscribedTokens.value.has(tokenAddress)) return

    if (socket.value?.readyState === WebSocket.OPEN) {
      const message: SolanaWsMessage = {
        type: 'unsubscribe',
        tokenAddress
      }

      socket.value.send(JSON.stringify(message))
    }

    subscribedTokens.value.delete(tokenAddress)
    curveUpdates.value.delete(tokenAddress)
    curveUpdateCallbacks.value.delete(tokenAddress)
  }

  // Register a callback for curve updates on a specific token
  const onCurveUpdate = (
    tokenAddress: string,
    callback: (update: CurveUpdate) => void
  ) => {
    if (!curveUpdateCallbacks.value.has(tokenAddress)) {
      curveUpdateCallbacks.value.set(tokenAddress, new Set())
    }
    curveUpdateCallbacks.value.get(tokenAddress)!.add(callback)

    // Return an unsubscribe function
    return () => {
      const callbacks = curveUpdateCallbacks.value.get(tokenAddress)
      if (callbacks) {
        callbacks.delete(callback)
        if (callbacks.size === 0) {
          curveUpdateCallbacks.value.delete(tokenAddress)
        }
      }
    }
  }

  const disconnect = () => {
    if (socket.value) {
      socket.value.close()
      socket.value = null
    }
    isConnected.value = false
    subscribedTokens.value.clear()
    curveUpdates.value.clear()
    curveUpdateCallbacks.value.clear() // Clean up all callbacks
  }

  const ping = () => {
    if (socket.value?.readyState === WebSocket.OPEN) {
      socket.value.send(JSON.stringify({ type: 'ping' }))
    }
  }

  // Auto-connect on mount (only once globally)
  onMounted(() => {
    // Only initialize on client side
    if (import.meta.server) {
      return
    }

    if (!globalInitialized.value) {
      globalInitialized.value = true
      connect()
    } else {
      if (!isConnected.value) {
        connect()
      }
    }
  })

  onNuxtReady(() => {
    // Only set up ping interval once
    if (!globalPingInterval) {
      globalPingInterval = setInterval(() => {
        if (isConnected.value) {
          ping()
        }
      }, 30000)
    }
  })

  // Clean up on unmount
  onUnmounted(() => {
    // Don't clear the global ping interval or disconnect globally
    // Just clean up local callbacks if needed
    curveUpdateCallbacks.value.clear()
  })

  // Getter for specific token curve updates
  const getTokenCurveUpdate = (tokenAddress: string): CurveUpdate | null => {
    return curveUpdates.value.get(tokenAddress) || null
  }

  // Reactive getter for all curve updates
  const allCurveUpdates = computed(() => {
    return Object.fromEntries(curveUpdates.value)
  })

  // Check if subscribed to a token
  const isSubscribedToToken = (tokenAddress: string): boolean => {
    return subscribedTokens.value.has(tokenAddress)
  }

  return {
    // Connection state
    isConnected: readonly(isConnected),
    subscribedTokens: readonly(subscribedTokens),

    // Methods
    connect,
    disconnect,
    subscribeToToken,
    unsubscribeFromToken,
    onCurveUpdate,
    isSubscribedToToken,

    // Data getters
    getTokenCurveUpdate,
    allCurveUpdates: readonly(allCurveUpdates),

    // Utils
    ping,
    refreshTokenData
  }
}
