import { PrismaClient } from '@prisma/client'

// Define a better type for globalThis
declare global {
  var prismaGlobal: PrismaClient | undefined
}

const prismaClientSingleton = () => {
  return new PrismaClient({
    log: process.env.NODE_ENV === 'development' ? ['error', 'warn'] : ['error']
  })
}

// Use global instead of globalThis for better compatibility with Nuxt's server environment
const prisma = global.prismaGlobal ?? prismaClientSingleton()

if (process.env.NODE_ENV !== 'production') global.prismaGlobal = prisma

export default prisma
