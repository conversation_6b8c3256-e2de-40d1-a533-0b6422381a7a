<script setup lang="ts">
import type { NuxtError } from '#app'
import CreatePostForm from '~/components/forms/CreatePostForm.vue'
import type { PostWithUserAsAuthor } from '~/types/database'

definePageMeta({
  async validate (route) {
    // id must be a number
    return !isNaN(Number(route.params.id))
  }
})

const route = useRoute()
const posts = usePostsStore()

// Real post data with replies
const post = ref<
  (PostWithUserAsAuthor & { replies: PostWithUserAsAuthor[] }) | undefined
>()

// Function to refresh the entire post (including replies)
const refreshPost = async () => {
  if (!post.value) return

  try {
    const data = (await $fetch(`/api/posts/${route.params.id}`)) as {
      post: PostWithUserAsAuthor & { replies: PostWithUserAsAuthor[] }
    }
    if (data?.post) {
      post.value.replies = data.post.replies
    }
  } catch (error) {
    console.error('Failed to refresh post:', error)
  }
}

// Load post data
onMounted(async () => {
  // Initialize posts store to load hashtags and tokens
  await posts.initializeStore()

  try {
    const data:
      | {
          post: PostWithUserAsAuthor & { replies: PostWithUserAsAuthor[] }
        }
      | undefined
      | null = await $fetch(`/api/posts/${route.params.id}`)
    post.value = data?.post
  } catch (error: unknown) {
    showError({
      message: 'Error getting post: ' + (error as NuxtError).message,
      status: 404
    })
  }
})

// Poll for replies updates every 5 seconds
// const { pause: pauseRepliesPolling, resume: resumeRepliesPolling } =
//   useTimeoutPoll(
//     refreshPost,
//     10000, // 10 seconds
//     { immediate: false }
//   )

// Start polling on mount and pause when component unmounts
onMounted(() => {
  // resumeRepliesPolling()
})

onUnmounted(() => {
  // pauseRepliesPolling()
})

// Refresh replies when a new reply is added
const onReplyAdded = async (reply: PostWithUserAsAuthor) => {
  // Immediately add the new reply to the list for instant feedback
  if (post.value && reply) {
    post.value.replies.unshift(reply)
  }

  // Also refresh from server to ensure consistency
  await refreshPost()
}

const renderContent = (content: string) => {
  const parsedContent = posts.parseContent(content)

  return parsedContent
    .replace(
      /#(\w+)/g,
      '<span class="text-blue-400 font-semibold cursor-pointer hover:underline">#$1</span>'
    )
    .replace(
      /\$(\w+)/g,
      '<span class="text-green-400 font-semibold cursor-pointer hover:underline">$$$1</span>'
    )
}
</script>

<template>
  <div class="min-h-screen bg-primary-bg">
    <div class="max-w-2xl mx-auto">
      <!-- Header -->
      <div class="p-4 border-b border-gray-700/50">
        <div class="flex items-center gap-3">
          <button
            class="w-8 h-8 rounded-full bg-gray-700/50 hover:bg-gray-700 transition-colors flex items-center justify-center"
            @click="$router.back()"
          >
            <Icon name="lucide:arrow-left" size="16" />
          </button>
          <h1 class="text-lg font-semibold">Post</h1>
        </div>
      </div>

      <!-- Original Post -->
      <div v-if="post" class="border-b border-gray-700/50">
        <div class="p-4">
          <div class="flex gap-3">
            <div
              class="w-10 h-10 rounded-full bg-secondary-bg flex items-center justify-center text-fg-light font-semibold text-sm overflow-hidden"
            >
              <img
                v-if="post.author?.profilePicture"
                :src="post.author.profilePicture"
                :alt="post.author?.name || 'User avatar'"
                class="w-full h-full object-cover"
              />
              <span v-else>{{
                (post.author?.displayName || post.author?.name)?.charAt(0)
              }}</span>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-2 mb-1">
                <span class="font-semibold text-fg-light">{{
                  post.author?.displayName || post.author?.name
                }}</span>
                <span class="text-xs text-gray-400">{{
                  post.author?.name
                }}</span>
                <span class="text-xs text-gray-400">·</span>
                <span class="text-xs text-gray-400">{{
                  formatTime(post.createdAt.toString())
                }}</span>
              </div>
              <div
                class="leading-relaxed mb-3"
                v-html="renderContent(post.content || '')"
              />

              <!-- Post Images -->
              <PostImages
                v-if="post.media && post.media.length > 0"
                :media="post.media"
                :post-id="post.id"
                class="mb-4"
              />

              <PostInteractions :post-id="post.id" :show-reply-button="false" />
            </div>
          </div>
        </div>
      </div>
      <SkeletonsPostExpandedSkeleton v-else />

      <!-- Reply Form -->
      <div class="p-4 border-b border-gray-700/50">
        <CreatePostForm
          placeholder="Write a reply"
          button-text="Reply"
          :is-reply="true"
          :post-id="post?.id"
          @reply-added="onReplyAdded"
        />
      </div>

      <!-- Replies -->
      <div v-if="post" class="divide-y divide-gray-700/50">
        <div
          v-for="reply in post.replies"
          :key="reply.id"
          class="p-4 hover:bg-gray-800/20 transition-colors"
        >
          <div class="flex gap-3">
            <div
              class="w-8 h-8 rounded-full bg-secondary-bg flex items-center justify-center text-fg-light font-semibold text-xs overflow-hidden"
            >
              <img
                v-if="reply.author?.profilePicture"
                :src="reply.author.profilePicture"
                :alt="reply.author?.name || 'User avatar'"
                class="w-full h-full object-cover"
              />
              <span v-else>{{
                (reply.author?.displayName || reply.author?.name)?.charAt(0)
              }}</span>
            </div>
            <div class="flex-1 min-w-0">
              <div class="flex items-center gap-2 mb-1">
                <span class="font-bold text-fg-light text-sm">{{
                  reply.author?.displayName || reply.author?.name
                }}</span>
                <span class="text-xs text-gray-400">{{
                  reply.author?.address?.slice(0, 8) + '...'
                }}</span>
                <span class="text-xs text-gray-400">·</span>
                <span class="text-xs text-gray-400">{{
                  formatTime(reply.createdAt.toString())
                }}</span>
              </div>
              <div
                class="text-gray-200 leading-relaxed mb-2"
                v-html="renderContent(reply.content)"
              />

              <!-- Reply Images -->
              <PostImages
                v-if="reply.media && reply.media.length > 0"
                :media="reply.media"
                :post-id="reply.id"
              />

              <PostInteractions
                :post-id="reply.id"
                :show-reply-button="false"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- Loading more indicator -->
      <!-- <div class="p-4 text-center">
        <button
          class="text-sm text-gray-400 hover:text-gray-300 transition-colors"
        >
          Load more replies
        </button>
      </div> -->
    </div>
  </div>
</template>
