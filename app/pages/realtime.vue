<script setup lang="ts">
// Use the WebSocket composable for real-time token updates
const { data: tokens, isLoading, error } = useTokensComp({ sortBy: 'newest' })

const router = useRouter()

// Track new tokens for subtle animation
const newTokenIds = ref(new Set<number>())

// Gamification data (frontend-only for demo)
const generateMockData = (token: Token) => {
  // Generate consistent but random-looking data based on token ID
  const seed = token.id
  return {
    likes: Math.floor((seed * 7) % 100) + 5,
    views: Math.floor((seed * 23) % 1000) + 50,
    score: Math.floor((seed * 13) % 100) + 1,
    trending: (seed * 17) % 4 === 0, // ~25% chance
    boost: (seed * 11) % 6 === 0 // ~16% chance
  }
}

// Calculate "The Crowned One" (top token of the hour)
const crownedToken = computed(() => {
  if (!tokens.value || tokens.value.length === 0) return null

  // Find token with highest score (mock calculation)
  return tokens.value.reduce((top, current) => {
    const currentData = generateMockData(current)
    const topData = generateMockData(top)
    return currentData.score > topData.score ? current : top
  })
})

const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    // Show a toast or notification here
  } catch (error) {
    console.error('Failed to copy:', error)
  }
}

// Watch for new tokens with reduced animation intensity
let previousTokenCount = 0
watch(
  tokens,
  newTokens => {
    if (newTokens && newTokens.length > 0) {
      if (newTokens.length > previousTokenCount && previousTokenCount > 0) {
        const newCount = newTokens.length - previousTokenCount

        for (let i = 0; i < newCount; i++) {
          const token = newTokens[i]
          newTokenIds.value.add(token.id)

          // Shorter, subtler animation
          setTimeout(() => {
            newTokenIds.value.delete(token.id)
          }, 1000)
        }
      }

      previousTokenCount = newTokens.length
    }
  },
  { immediate: true }
)
</script>

<template>
  <div class="mt-11 max-w-2xl mx-auto">
    <!-- The Crowned One Section -->
    <div v-if="crownedToken" class="mb-4 border-b border-gray-100/5 p-4">
      <div class="flex items-center gap-2 mb-3">
        <span class="text-lg">
          <Icon name="noto:crown" size="20" />
        </span>
        <span class="font-bold text-sm">The Crowned One</span>
        <span class="text-xs text-muted bg-gray-50/10 px-2 py-1 rounded-full"
          >Top Token This Hour</span
        >
      </div>

      <div class="flex items-center gap-3">
        <div
          class="w-10 h-10 rounded-full bg-secondary-bg flex items-center justify-center"
        >
          <span class="text-white font-bold">{{
            crownedToken.symbol.charAt(0)
          }}</span>
        </div>
        <div class="flex-1 space-y-px text-sm">
          <div class="font-bold">{{ crownedToken.name }}</div>
          <div class="flex items-center gap-2 text-xs text-muted">
            <span>${{ crownedToken.symbol }}</span>
            <div class="w-1 h-1 bg-muted rounded-full" />
            <span>{{ generateMockData(crownedToken).likes }} likes</span>
            <div class="w-1 h-1 bg-muted rounded-full" />
            <span>{{ generateMockData(crownedToken).views }} views</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="text-center py-8 text-muted">
      Loading tokens...
    </div>

    <!-- Error state -->
    <div v-if="error" class="text-center py-8 text-red-400">
      {{ error }}
    </div>

    <!-- Token Feed -->
    <div class="flex flex-col rounded overflow-hidden pb-10">
      <!-- Show message if no tokens -->
      <div
        v-if="!isLoading && tokens.length === 0"
        class="text-center py-8 text-muted"
      >
        No tokens yet. Be the first to create one!
      </div>

      <!-- Token list -->
      <template v-for="token in tokens" :key="token.id">
        <!-- Regular token card -->
        <div
          :class="[
            'border-b border-gray-100/5 p-4 gap-4 hover:bg-white/[1%] cursor-pointer transition-all duration-300',
            newTokenIds.has(token.id) ? 'bg-green-500/5' : ''
          ]"
          @click="router.push(`/coin/${token.address}`)"
        >
          <!-- New token indicator -->
          <div
            v-if="newTokenIds.has(token.id)"
            class="flex items-center gap-1 mb-2"
          >
            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
            <span class="text-xs text-green-400 font-semibold">Fresh</span>
          </div>

          <div class="flex items-center gap-3">
            <div
              class="w-10 h-10 rounded-full bg-secondary-bg flex items-center justify-center"
            >
              <span class="text-white font-bold">{{
                token.symbol.charAt(0)
              }}</span>
            </div>
            <div class="flex-1 space-y-px text-sm">
              <div class="flex items-center gap-2">
                <span class="font-bold">{{ token.name }}</span>
                <div
                  v-if="generateMockData(token).trending"
                  class="text-xs bg-orange-500/20 text-orange-400 px-2 py-0.5 rounded-full flex items-center gap-1"
                >
                  <Icon name="stash:chart-trend-up-duotone" size="15" />
                  Trending
                </div>
              </div>
              <div class="flex items-center gap-2 text-xs text-muted">
                <span>${{ token.symbol }}</span>
                <div class="w-1 h-1 bg-muted rounded-full" />
                <span>{{ formatTimeAgo(token.createdAt) }}</span>
                <div class="w-1 h-1 bg-muted rounded-full" />
                <span>{{ generateMockData(token).likes }} likes</span>
              </div>
            </div>
          </div>

          <!-- Action buttons -->
          <div class="flex items-center justify-between text-muted mt-3">
            <button
              class="flex items-center px-2 py-1 cursor-pointer hover:text-blue-400 rounded-full gap-2"
              @click="copyToClipboard(token.address)"
            >
              <Icon name="lucide:copy" size="16" />
              <span class="text-xs">Copy</span>
            </button>
            <NuxtLink
              :to="`/coin/${token.address}`"
              class="flex items-center px-2 py-1 cursor-pointer hover:text-green-400 rounded-full gap-2"
            >
              <Icon name="lucide:arrow-right" size="16" />
              <span class="text-xs">View</span>
            </NuxtLink>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped>
/* No custom styles needed - using project defaults */
</style>
