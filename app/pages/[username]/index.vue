<script setup lang="ts">
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Ta<PERSON>Trigger
} from 'reka-ui'
import type { PostWithRelationAndCounts } from '~/types/database'
import EditProfilePopup from '~/components/popups/EditProfilePopup.vue'
import PostSkeleton from '~/components/skeletons/PostSkeleton.vue'
import FollowButton from '~/components/button/FollowButton.vue'
import ReplyPost from '~/components/ReplyPost.vue'
import PostCard from '~/components/PostCard.vue'

const route = useRoute()
const session = useUserSession()
const username = route.params.username as string

const currentSelectedTab = ref('posts')
const tabStep = ref(0)
const bottomTabsIndShift = computed(() => {
  // Start from center minus half the total width of 3 tabs (3 * 8rem = 24rem / 2 = 12rem)
  // Then add the offset for the current tab
  return `calc(-12rem + ${tabStep.value * 8}rem)`
})

const posts = usePostsStore()

// Fetch user data with client-side loading
const {
  data: userData,
  error: userError,
  pending: userPending
  // refresh: refreshUserData,
} = await useLazyFetch<{
  id: number
  name: string
  username: string
  bio: string
  profileImage: string
  coverImage: string
  followers: number
  following: number
  postsCount: number
  tokensCount: number
  joinDate: string
  address: string
  isFollowing: boolean
}>(`/api/user/${username}`, {
  server: false // Force client-side only
})

const { data: userPostsData, pending: postsPending } = await useLazyFetch(
  `/api/user/${username}/posts`,
  {
    server: false // Force client-side only
  }
)

const { data: userRepliesData, pending: repliesPending } = await useLazyFetch(
  `/api/user/${username}/replies`,
  {
    server: false // Force client-side only
  }
)

if (userError.value) {
  throw createError({
    statusCode: userError.value.statusCode || 404,
    statusMessage: userError.value.statusMessage || 'User not found'
  })
}

// Check if this is the current user's profile
const isOwnProfile = computed(() => {
  return session.user.value?.address === userData.value?.address
})

// Reactive state for follow status
const isFollowing = ref(false)
const followersCount = ref(0)

// Update reactive state when userData changes
watch(
  userData,
  newUserData => {
    if (newUserData) {
      isFollowing.value = newUserData.isFollowing || false
      followersCount.value = newUserData.followers || 0
    }
  },
  { immediate: true }
)

// Handle follow status updates
const handleFollowUpdate = (newFollowingState: boolean) => {
  isFollowing.value = newFollowingState
  // Update followers count optimistically
  if (newFollowingState) {
    followersCount.value += 1
  } else {
    followersCount.value = Math.max(0, followersCount.value - 1)
  }

  // Update the userData reactive ref to reflect the new state
  if (userData.value) {
    userData.value.isFollowing = newFollowingState
    userData.value.followers = followersCount.value
  }
}

// Handle profile update
const handleProfileUpdate = async (profileData: {
  name: string
  bio: string
  profileImage?: string
  coverImage?: string
}) => {
  if (!userData.value) return

  userData.value = {
    ...userData.value,
    name: profileData.name,
    bio: profileData.bio,
    profileImage: profileData.profileImage || userData.value.profileImage,
    coverImage: profileData.coverImage || userData.value.coverImage
  }

  refreshCookie(`userData-${username}`)
}

const userPosts = computed<PostWithRelationAndCounts[]>(() => {
  const raw =
    (userPostsData.value as { posts?: PostWithRelationAndCounts[] } | null)
      ?.posts || []
  interface MediaShape {
    id: number
    url: string
    type: string
    width?: number | null
    height?: number | null
    order: number
  }
  return raw.map(p => ({
    ...p,
    replyToId: p.replyToId ?? null,
    user: {
      id: p.user.id,
      name: p.user.name,
      displayName: p.user.displayName,
      address: p.user.address,
      profilePicture: p.user.profilePicture,
      email: '',
      bio: null,
      coverImage: null,
      website: null,
      twitterHandle: null,
      emailVerified: false,
      onChainId: null,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    createdAt: new Date(p.createdAt),
    updatedAt: new Date(p.updatedAt),
    likeCount: p.likeCount ?? 0,
    replyCount: p.replyCount ?? 0,
    shareCount: p.shareCount ?? 0,
    media: (p.media || []).map((m: MediaShape) => ({
      id: m.id,
      url: m.url,
      type: m.type,
      width: m.width ?? null,
      height: m.height ?? null,
      order: m.order,
      size: null,
      mimeType: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      postId: p.id
    }))
  }))
})

interface ReplyWithParent extends PostWithRelationAndCounts {
  replyTo?: PostWithRelationAndCounts | null
}

const userReplies = computed<ReplyWithParent[]>(() => {
  const raw =
    (userRepliesData.value as { replies?: ReplyWithParent[] } | null)
      ?.replies || []
  interface MediaShape {
    id: number
    url: string
    type: string
    width?: number | null
    height?: number | null
    order: number
  }
  return raw.map(r => ({
    ...r,
    likeCount: r.likeCount ?? 0,
    replyCount: r.replyCount ?? 0,
    shareCount: r.shareCount ?? 0,
    replyTo: r.replyTo
      ? {
          ...r.replyTo,
          replyToId: r.replyTo.replyToId ?? null,
          user: {
            id: r.replyTo.user.id,
            name: r.replyTo.user.name,
            displayName: r.replyTo.user.displayName,
            address: r.replyTo.user.address,
            profilePicture: r.replyTo.user.profilePicture,
            email: '',
            bio: null,
            coverImage: null,
            website: null,
            twitterHandle: null,
            emailVerified: false,
            onChainId: null,
            createdAt: new Date(),
            updatedAt: new Date()
          },
          createdAt: new Date(r.replyTo.createdAt),
          updatedAt: new Date(r.replyTo.updatedAt),
          likeCount: r.replyTo.likeCount ?? 0,
          replyCount: r.replyTo.replyCount ?? 0,
          shareCount: r.replyTo.shareCount ?? 0,
          media: (r.replyTo.media || []).map((m: MediaShape) => ({
            id: m.id,
            url: m.url,
            type: m.type,
            width: m.width ?? null,
            height: m.height ?? null,
            order: m.order,
            size: null,
            mimeType: null,
            createdAt: new Date(),
            updatedAt: new Date(),
            postId: r.replyTo ? r.replyTo.id : 0
          }))
        }
      : null
  }))
})

const userCoins = ref<
  Array<{
    id: number
    symbol: string
    name: string
    price: string
    change: string
    position: string
    value: string
  }>
>([])
</script>

<template>
  <div>
    <!-- User Profile Section -->
    <div v-if="userPending" class="relative">
      <!-- Loading skeleton for cover and profile -->
      <div class="w-full h-24 bg-gray-100/5 animate-pulse" />
      <div
        class="size-20 rounded-full bg-gray-100/10 border-4 border-primary-bg absolute left-1/2 -translate-x-1/2 -bottom-1/2 animate-pulse"
      />
    </div>

    <div v-else-if="userData" class="relative">
      <!-- cover -->
      <div class="w-full h-24 bg-gray-100/5">
        <img
          v-if="userData.coverImage"
          class="w-full h-full object-cover"
          :src="userData.coverImage"
          alt="Cover photo"
        />
      </div>

      <UserAvatar
        class="border-4 border-primary-bg absolute left-1/2 -translate-x-1/2 -bottom-1/2"
        :user="userData"
        size="lg"
      />
    </div>

    <!-- User Info Section -->
    <div v-if="userPending" class="mt-20">
      <!-- Loading skeleton for user info -->
      <div class="flex items-baseline justify-center gap-2">
        <div class="h-6 w-24 bg-gray-100/10 rounded animate-pulse" />
        <div class="h-4 w-16 bg-gray-100/10 rounded animate-pulse" />
      </div>
      <div class="h-4 w-32 bg-gray-100/10 rounded animate-pulse mx-auto mt-2" />
      <div class="flex items-center justify-center gap-4 mt-3">
        <div class="h-3 w-20 bg-gray-100/10 rounded animate-pulse" />
        <div class="h-3 w-20 bg-gray-100/10 rounded animate-pulse" />
        <div class="h-3 w-24 bg-gray-100/10 rounded animate-pulse" />
      </div>
    </div>

    <div v-else-if="userData" class="pt-20 rounded-xl">
      <div class="flex items-baseline justify-center gap-2 font-semibold">
        <h1 class="text-center font-bold text-lg">{{ userData.name }}</h1>
        <div class="text-sm text-muted">@{{ userData.username }}</div>
      </div>
      <p v-if="userData.bio" class="text-center text-sm text-muted mt-1">
        {{ userData.bio }}
      </p>

      <!-- User stats -->
      <div class="flex items-center justify-center gap-4 mt-3 text-xs">
        <div class="flex items-center gap-1">
          <span class="font-semibold">{{
            followersCount?.toLocaleString() || 0
          }}</span>
          <span class="text-muted">followers</span>
        </div>
        <div class="flex items-center gap-1">
          <span class="font-semibold">{{
            userData.following?.toLocaleString() || 0
          }}</span>
          <span class="text-muted">following</span>
        </div>
        <div class="flex items-center gap-1">
          <Icon name="lucide:calendar" size="12" class="text-muted" />
          <span class="text-muted">Joined {{ userData.joinDate }}</span>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-center mt-4 mx-5">
        <EditProfilePopup
          v-if="isOwnProfile"
          :user-data="{
            name: userData.name,
            username: userData.username,
            bio: userData.bio || '',
            profileImage: userData.profileImage || '',
            coverImage: userData.coverImage || ''
          }"
          @update:profile="handleProfileUpdate"
        />

        <!-- Follow/Unfollow Button (only show for other users) -->
        <FollowButton
          v-else-if="session.loggedIn.value && userData.id"
          :user-id="userData.id"
          :is-following="isFollowing"
          @update:is-following="handleFollowUpdate"
        />
      </div>
    </div>

    <TabsRoot v-model="currentSelectedTab" default-value="posts">
      <TabsList
        class="flex relative justify-center mt-6 border-b border-gray-100/5"
      >
        <TabsIndicator
          class="absolute left-1/2 h-[2px] bottom-0 w-32 translate-y-[1px] rounded-full transition-[width,transform] duration-300"
          :style="{
            transform: `translateX(${bottomTabsIndShift})`
          }"
        >
          <div class="bg-blue-500 w-[96px] mx-auto h-full" />
        </TabsIndicator>
        <TabsTrigger
          class="px-2 py-2 w-32 text-center font-semibold text-sm transition-colors duration-300"
          :class="{
            'text-fg-light': currentSelectedTab === 'posts',
            'text-muted': currentSelectedTab !== 'posts'
          }"
          value="posts"
          @click="tabStep = 0"
        >
          Posts
        </TabsTrigger>
        <TabsTrigger
          class="px-2 py-2 w-32 text-center font-semibold text-sm transition-colors duration-300"
          :class="{
            'text-fg-light': currentSelectedTab === 'replies',
            'text-muted': currentSelectedTab !== 'replies'
          }"
          value="replies"
          @click="tabStep = 1"
        >
          Replies
        </TabsTrigger>
        <TabsTrigger
          class="px-2 py-2 w-32 text-center font-semibold text-sm transition-colors duration-300"
          :class="{
            'text-fg-light': currentSelectedTab === 'coins',
            'text-muted': currentSelectedTab !== 'coins'
          }"
          value="coins"
          @click="tabStep = 2"
        >
          Coins
        </TabsTrigger>
      </TabsList>

      <!-- Tab Contents -->
      <TabsContent value="posts" class="mt-0">
        <div v-if="postsPending">
          <!-- Loading skeletons for posts -->
          <PostSkeleton v-for="i in 3" :key="i" />
        </div>
        <div
          v-else-if="userPosts.length === 0"
          class="text-center text-muted py-8"
        >
          <Icon
            name="lucide:message-circle"
            size="24"
            class="mx-auto mb-2 opacity-50"
          />
          <p class="text-sm">No posts yet</p>
        </div>
        <div v-else>
          <PostCard v-for="post in userPosts" :key="post.id" :post="post" />
        </div>
      </TabsContent>

      <TabsContent value="replies" class="mt-0">
        <div v-if="repliesPending">
          <!-- Loading skeletons for replies -->
          <PostSkeleton v-for="i in 3" :key="i" />
        </div>
        <div
          v-else-if="userReplies.length === 0"
          class="text-center text-muted py-8"
        >
          <Icon name="lucide:reply" size="24" class="mx-auto mb-2 opacity-50" />
          <p class="text-sm">No replies yet</p>
        </div>
        <div v-else>
          <!-- Each element in userReplies is a reply post that includes its original post in replyTo -->
          <ReplyPost
            v-for="reply in userReplies"
            :key="reply.id"
            :reply="reply"
            :original-post="reply.replyTo || undefined"
          />
        </div>
      </TabsContent>

      <TabsContent value="coins" class="mt-0">
        <div v-if="userCoins.length === 0" class="text-center text-muted py-8">
          <Icon name="lucide:coins" size="24" class="mx-auto mb-2 opacity-50" />
          <p class="text-sm">No coins owned or created</p>
        </div>
        <div v-else class="p-4">
          <!-- Portfolio Summary -->
          <div class="rounded-lg p-3 mb-4 border border-gray-100/5">
            <div class="grid grid-cols-3 gap-4 text-center">
              <div>
                <div class="text-lg font-bold text-green-400">148.5 SOL</div>
                <div class="text-xs text-muted">Total Value</div>
              </div>
              <div>
                <div class="text-lg font-bold text-green-400">+5.8%</div>
                <div class="text-xs text-muted">1h Change</div>
              </div>
              <div>
                <div class="text-lg font-bold">3</div>
                <div class="text-xs text-muted">Owned</div>
              </div>
            </div>
          </div>

          <!-- Coin Positions -->
          <div class="space-y-2">
            <div class="text-xs font-semibold text-muted mb-2">Your Coins</div>
            <div
              v-for="coin in userCoins"
              :key="coin.id"
              class="flex items-center justify-between p-3 bg-gray-100/5 rounded-lg hover:bg-gray-100/10 transition cursor-pointer border border-gray-100/5"
            >
              <div class="flex items-center gap-3">
                <div
                  class="w-8 h-8 rounded-full bg-secondary-bg flex items-center justify-center"
                >
                  <span class="text-white font-bold text-xs">{{
                    coin.symbol.charAt(0)
                  }}</span>
                </div>
                <div>
                  <div class="font-semibold text-sm">${{ coin.symbol }}</div>
                  <div class="text-xs text-muted">{{ coin.position }}</div>
                </div>
              </div>
              <div class="text-right">
                <div class="font-semibold text-sm">{{ coin.value }}</div>
                <div
                  :class="[
                    'text-xs font-semibold',
                    coin.change.startsWith('+')
                      ? 'text-green-400'
                      : 'text-red-400'
                  ]"
                >
                  {{ coin.change }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </TabsContent>
    </TabsRoot>

    <PostMediaOverlay v-if="posts.postOverlayVisible" />
  </div>
</template>
