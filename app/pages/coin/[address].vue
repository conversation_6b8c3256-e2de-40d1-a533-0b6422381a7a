<script setup lang="ts">
import {
  create<PERSON><PERSON>,
  type IChartApi,
  type SolidColor
} from 'lightweight-charts'
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TabsIndicator,
  TabsList,
  <PERSON>bsRoot,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  TooltipProvider
} from 'reka-ui'
// import type { TransactionSerialized } from '~/types/database'

const tabStep = ref(0)
const layout = useLayout()
const route = useRoute()
const currentSelectedTab = ref('transactions')
const address = route.params.address as string
const chartContainer = ref<HTMLDivElement | null>(null)
let chart: IChartApi | null = null

const priceWsEndpoint = computed(() => {
  if (import.meta.client) {
    const wss = window.location.protocol === 'https:' ? 'wss://' : 'ws://'
    return `${wss}${window.location.host}/api/ws/trading/${address || 'error'}`
  }

  return undefined
})

const trading = useTrading({
  token: address || '',
  endpoint: priceWsEndpoint.value ,
})
const { data: transactions, refresh } = await useFetch(
  `/api/fin/transactions/${address}`,
  {
    watch: [trading.tokenInfo]
  }
)

// this one will be used for chart data and token activity (transactions)
// const trading = useTrading({
//   token: address,
//   includePriceData: false,
// })

layout.setIsTokenPage(true)

onMounted(async () => {
  // Update activities every 30 seconds to simulate real-time updates
  // const interval = setInterval(() => {
  //   const newActivity = generateLiveActivities()[0]
  //   liveActivities.value.unshift(newActivity)
  //   // Keep only the latest 20 activities
  //   if (liveActivities.value.length > 20) {
  //     liveActivities.value = liveActivities.value.slice(0, 20)
  //   }
  // }, 30000)

  // Store interval for cleanup
  // onBeforeUnmount(() => {
  //   clearInterval(interval)
  // })

  // Chart setup
  chart = createChart(chartContainer.value!, {
    width: chartContainer.value!.clientWidth,
    height: chartContainer.value!.clientHeight,
    layout: {
      background: {
        color: '#11242e'
      } as SolidColor,
      textColor: '#d1d5db'
    },
    grid: {
      vertLines: {
        color: '#374151'
      },
      horzLines: {
        color: '#374151'
      }
    },
    rightPriceScale: {
      borderColor: '#374151'
    },
    timeScale: {
      borderColor: '#374151'
    }
  })

  // Create the candlestick series using the correct API for v5
  // const candlestickSeries = chart.addSeries(CandlestickSeries, {
  //   upColor: '#10b981',
  //   downColor: '#ef4444',
  //   borderDownColor: '#ef4444',
  //   borderUpColor: '#10b981',
  //   wickDownColor: '#ef4444',
  //   wickUpColor: '#10b981',
  // })

  // const dummyData = generateDummyData()
  // candlestickSeries.setData(dummyData as any)

  // // Add volume series
  // const volumeSeries = chart.addSeries(HistogramSeries, {
  //   color: '#6b7280',
  //   priceFormat: {
  //     type: 'volume',
  //   },
  //   priceScaleId: '',
  // })

  // // Generate volume data
  // const volumeData = dummyData.map((candle) => ({
  //   time: candle.time,
  //   value: Math.floor(Math.random() * 1000000) + 100000,
  //   color: candle.close > candle.open ? '#10b981' : '#ef4444',
  // }))

  window.addEventListener('resize', handleResize)

  // volumeSeries.setData(volumeData as any)

  // Fit content to show all data nicely
  // chart.timeScale().fitContent()

  // Handle window resize

  // layout.isLoading = true
  // try {
  //   token.value = await getToken(address)
  // } catch (error) {
  //   console.error('Error fetching token:', error)
  // } finally {
  //   layout.setIsTokenPage(true)
  //   setTimeout(() => (layout.isLoading = false), 1000)
  // }
})

const handleResize = () => {
  if (chart && chartContainer.value) {
    chart.applyOptions({
      width: chartContainer.value.clientWidth,
      height: chartContainer.value.clientHeight
    })
  }
}

// Cleanup on unmount
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  if (chart) {
    chart.remove()
  }

  chart = null
  layout.setIsTokenPage(false)
})

provide<string>('currentTokenAddress', address)
const tokenInfo = trading.tokenInfo
</script>

<template>
  <TooltipProvider>
    <div class="flex gap-3 w-full h-screen overflow-hidden p-2">
      <!-- Left sidebar - Token Feed -->
      <div
        class="hidden bg-gray-50/5 max-w-sm w-full h-full rounded-lg md:flex flex-col overflow-hidden"
      >
        <div class="p-3 border-b border-gray-100/5 shrink-0">
          <h3 class="font-semibold text-sm">Activity</h3>
        </div>
        <div class="flex-1 overflow-y-auto scrollbar">
          <div class="space-y-1 p-3" />
        </div>
      </div>

      <!-- Main content area -->
      <div class="flex-1 flex flex-col gap-3 min-w-0">
        <!-- Chart -->
        <div
          class="h-[500px] bg-gray-100/10 rounded-lg border border-white/10 overflow-hidden"
        >
          <div ref="chartContainer" class="chart h-full w-full" />
        </div>

        <!-- Tabs section -->
        <div class="bg-gray-50/5 rounded-lg flex-1 flex flex-col min-h-0">
          <TabsRoot
            v-model="currentSelectedTab"
            default-value="transactions"
            class="flex flex-col h-full"
          >
            <TabsList
              class="flex py-2 px-3 relative border-b border-gray-100/5 shrink-0"
            >
              <TabsIndicator
                class="absolute left-3 h-[2px] bottom-0 w-24 translate-y-[1px] rounded-full transition-[width,transform] duration-300"
                :style="{
                  transform: `translateX(${tabStep * 96}px)`
                }"
              >
                <div class="bg-blue-500 w-full h-full" />
              </TabsIndicator>
              <TabsTrigger
                class="px-2 py-1 w-24 text-center font-semibold text-xs"
                :class="{
                  'text-fg-light': currentSelectedTab === 'transactions',
                  'text-muted': currentSelectedTab !== 'transactions'
                }"
                value="transactions"
                @click="tabStep = 0"
              >
                Transactions
              </TabsTrigger>
              <TabsTrigger
                class="px-2 py-1 w-24 text-center font-semibold text-xs"
                :class="{
                  'text-fg-light': currentSelectedTab === 'holders',
                  'text-muted': currentSelectedTab !== 'holders'
                }"
                value="holders"
                @click="tabStep = 1"
              >
                Holders
              </TabsTrigger>
              <!-- <TabsTrigger
                @click="tabStep = 2"
                class="px-2 py-1 w-24 text-center font-semibold text-xs"
                :class="{
                  'text-fg-light': currentSelectedTab === 'trades',
                  'text-muted': currentSelectedTab !== 'trades',
                }"
                value="trades"
              >
                Trades
              </TabsTrigger>
              <TabsTrigger
                @click="tabStep = 3"
                class="px-2 py-1 w-24 text-center font-semibold text-xs"
                :class="{
                  'text-fg-light': currentSelectedTab === 'info',
                  'text-muted': currentSelectedTab !== 'info',
                }"
                value="info"
              >
                Info
              </TabsTrigger> -->
            </TabsList>

            <div class="flex-1 overflow-hidden">
              <TabsContent
                value="transactions"
                class="h-full overflow-y-auto scrollbar p-3"
              >
                <TransactionsTable
                  :transactions="transactions"
                  :token-info="tokenInfo"
                />
              </TabsContent>
              <TabsContent
                value="holders"
                class="h-full overflow-y-auto scrollbar p-3"
              >
                <HoldersTable :token-info="tokenInfo" />
              </TabsContent>
              <!-- <TabsContent
                value="trades"
                class="h-full overflow-y-auto scrollbar p-3"
              >
                <TradesTable />
              </TabsContent>
              <TabsContent
                value="info"
                class="h-full overflow-y-auto scrollbar p-3"
              >
                <TokenInfoTable />
              </TabsContent> -->
            </div>
          </TabsRoot>
        </div>
      </div>

      <!-- Right sidebar - Trading -->
      <div class="bg-gray-50/5 max-w-sm w-full rounded-lg">
        <TradingSidebar :token-info="tokenInfo" />
      </div>
    </div>
  </TooltipProvider>
</template>
