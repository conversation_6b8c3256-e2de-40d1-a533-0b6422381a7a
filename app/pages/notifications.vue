<script setup lang="ts">
import { formatTime } from '~/utils/time-utils'

// Use the notifications store
const store = useNotificationsStore()

// Load notifications on mount and mark them as read
onMounted(async () => {
  // Set viewing state to true
  store.setViewingNotifications(true)

  // Fetch initial notifications
  await store.fetchNotifications()

  // Also get unread count
  await store.getUnreadCount()

  // Automatically mark all notifications as read when user visits the page
  if (store.unreadCount > 0) {
    await store.markAllAsRead()
  }
})

// Clean up when leaving the page
onUnmounted(() => {
  store.setViewingNotifications(false)
})

const getNotificationIcon = (type: string) => {
  switch (type) {
    case 'comment':
      return 'lucide:message-circle'
    case 'like':
      return 'lucide:heart'
    case 'follow':
      return 'lucide:user-plus'
    case 'mention':
      return 'lucide:at-sign'
    case 'share':
      return 'lucide:repeat'
    case 'token':
      return 'mdi:dollar'
    default:
      return 'lucide:bell'
  }
}

const getNotificationColor = (type: string) => {
  switch (type) {
    case 'comment':
      return 'text-secondary-bg'
    case 'like':
      return 'text-secondary-bg-light'
    case 'follow':
      return 'text-primary-btn-bg'
    case 'mention':
      return 'text-secondary-bg'
    case 'share':
      return 'text-primary-btn-bg'
    case 'token':
      return 'text-secondary-bg'
    default:
      return 'text-muted'
  }
}

const navigateToPost = (postId?: number) => {
  if (postId) {
    navigateTo(`/post/${postId}`)
  }
}

const navigateToProfile = (username: string) => {
  navigateTo(`/${username}`)
}
</script>

<template>
  <div class="max-w-2xl w-full">
    <!-- Header -->
    <div class="border-b border-gray-100/5 p-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center gap-3">
          <h1 class="text-xl font-bold text-fg-light">Notifications</h1>
          <span
            v-if="store.unreadCount > 0"
            class="bg-primary-btn-bg text-fg-light text-xs px-2 py-1 rounded-full"
          >
            {{ store.unreadCount }}
          </span>
        </div>
        <!-- Removed manual "Mark all as read" button since notifications are auto-marked -->
      </div>
    </div>

    <!-- Notifications List -->
    <div class="flex flex-col">
      <!-- Loading state -->
      <div
        v-if="store.isLoading && store.isInitialLoad"
        class="p-8 text-center"
      >
        <div
          class="animate-spin w-8 h-8 border-2 border-primary-btn-bg border-t-transparent rounded-full mx-auto mb-4"
        />
        <p class="text-muted">Loading notifications...</p>
      </div>

      <!-- Empty state -->
      <div
        v-else-if="store.notifications.length === 0 && !store.isLoading"
        class="p-4 text-center"
      >
        <Icon name="lucide:bell" size="48" class="text-muted mx-auto mb-4" />
        <p class="text-muted">No notifications yet</p>
      </div>

      <!-- Notifications list with smooth transitions -->
      <template v-else>
        <TransitionGroup
          name="notification"
          tag="div"
          class="notifications-container"
        >
          <div
            v-for="notification in store.notifications"
            :key="notification.id"
            :class="[
              'border-b border-gray-100/5 p-4 gap-4 hover:bg-white/[1%] cursor-pointer transition-all duration-200',
              !notification.read ? 'bg-white/[0.5%]' : ''
            ]"
          >
            <div class="flex gap-3">
              <!-- Notification Icon -->
              <div
                :class="[
                  'flex-shrink-0 mt-1',
                  getNotificationColor(notification.type)
                ]"
              >
                <Icon
                  :name="getNotificationIcon(notification.type)"
                  size="16"
                />
              </div>

              <!-- User Avatar -->
              <UserAvatar
                :user="notification.sourceUser"
                size="sm"
                class="flex-shrink-0"
              />

              <!-- Notification Content -->
              <div class="flex-1 min-w-0">
                <div class="flex items-start justify-between gap-2">
                  <div class="flex-1">
                    <!-- Main notification text -->
                    <div class="text-sm">
                      <button
                        class="font-bold hover:underline text-fg-light"
                        @click.stop="
                          navigateToProfile(notification.sourceUser.username)
                        "
                      >
                        {{ notification.sourceUser.displayName }}
                      </button>
                      <span class="text-fg-light ml-1">{{
                        notification.content
                      }}</span>
                    </div>

                    <!-- Post content preview for post-related notifications -->
                    <div
                      v-if="notification.post"
                      class="mt-2 p-3 bg-white/[1%] border border-gray-100/5 hover:bg-white/[2%] transition-colors cursor-pointer rounded"
                      @click.stop="navigateToPost(notification.post.id)"
                    >
                      <p class="text-sm text-muted line-clamp-2">
                        {{ notification.post.content }}
                      </p>
                    </div>

                    <!-- Token info for token notifications -->
                    <div
                      v-if="notification.token"
                      class="mt-2 p-3 bg-white/[1%] border border-gray-100/5 hover:bg-white/[2%] transition-colors cursor-pointer rounded flex items-center gap-2"
                    >
                      <Icon
                        name="lucide:coins"
                        size="16"
                        class="text-secondary-bg"
                      />
                      <span class="font-bold text-secondary-bg"
                        >${{ notification.token.symbol }}</span
                      >
                      <span class="text-sm text-muted">{{
                        notification.token.name
                      }}</span>
                    </div>

                    <!-- Time -->
                    <div
                      class="flex items-center gap-1 text-xs text-muted mt-1"
                    >
                      <div>{{ formatTime(notification.createdAt) }}</div>
                    </div>
                  </div>

                  <!-- Unread indicator -->
                  <div v-if="!notification.read" class="flex-shrink-0 mt-1">
                    <div class="w-2 h-2 bg-primary-btn-bg rounded-full" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </TransitionGroup>
      </template>
    </div>
  </div>
</template>

<style scoped>
/* Notification animation styles */
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s ease;
}

.notification-enter-from {
  opacity: 0;
  transform: translateY(-20px);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100px);
}

.notification-move {
  transition: transform 0.3s ease;
}

.notifications-container {
  position: relative;
}

/* Loading spinner animation */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
