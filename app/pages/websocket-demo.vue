<script setup lang="ts">
import { useMarketCap, formatAddress } from '~/composables/useWebsocketHelpers'

definePageMeta({
  middleware: ['dev']
})

// For demo purposes, let's use a test token address
const demoTokenAddress = 'EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v' // USDC address as example
const demoTokenSymbol = 'DEMO'

// Test market cap data
const { data: marketCapData, isConnected: marketCapConnected } =
  useMarketCap(demoTokenAddress)
</script>

<template>
  <div class="min-h-screen bg-primary-bg p-6">
    <div class="max-w-7xl mx-auto">
      <!-- Page Header -->
      <header class="mb-8">
        <h1 class="text-3xl font-bold mb-2">WebSocket Data Feeds Demo</h1>
        <p class="text-muted">
          Real-time data feeds using Nitro WebSockets with contextual data
          management
        </p>
      </header>

      <!-- Main Grid Layout -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left Column: Transactions & Posts -->
        <div class="lg:col-span-2 space-y-6">
          <!-- Global Transactions Feed -->
          <div class="bg-gray-50/5 rounded-xl p-6">
            <WebsocketTransactionsFeed :max-items="8" />
          </div>

          <!-- Posts Feed -->
          <div class="bg-gray-50/5 rounded-xl p-6">
            <WebsocketPostsFeed
              :token-symbol="demoTokenSymbol"
              :max-items="5"
            />
          </div>
        </div>

        <!-- Right Column: Price & Market Data -->
        <div class="space-y-6">
          <!-- Price Feed -->
          <div class="bg-gray-50/5 rounded-xl p-6">
            <WebsocketPriceFeed
              :token-address="demoTokenAddress"
              :show-chart="true"
            />
          </div>

          <!-- Market Cap Info -->
          <div class="bg-gray-50/5 rounded-xl p-6">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold">Market Cap</h3>
              <div class="flex items-center gap-2 text-sm">
                <div
                  :class="[
                    'w-2 h-2 rounded-full',
                    marketCapConnected ? 'bg-green-400' : 'bg-red-400'
                  ]"
                />
                <span class="text-muted">
                  {{ marketCapConnected ? 'Live' : 'Disconnected' }}
                </span>
              </div>
            </div>

            <div v-if="marketCapData.length > 0" class="space-y-4">
              <div
                v-for="data in marketCapData.slice(0, 3)"
                :key="data.tokenId"
                class="border border-gray-50/10 rounded-lg p-4"
              >
                <div class="flex justify-between items-start mb-2">
                  <span class="font-semibold text-sm">{{
                    formatAddress(data.tokenAddress)
                  }}</span>
                  <span
                    v-if="data.isNearMigration"
                    class="text-xs bg-orange-400/20 text-orange-400 px-2 py-1 rounded"
                  >
                    Near Migration
                  </span>
                </div>

                <div class="space-y-2">
                  <div class="text-xl font-bold">{{ data.marketCap }}</div>
                  <div class="w-full bg-gray-700 rounded-full h-2">
                    <div
                      class="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500"
                      :style="{ width: `${Math.min(data.percentage, 100)}%` }"
                    />
                  </div>
                  <div class="flex justify-between text-xs text-muted">
                    <span>{{ data.percentage.toFixed(1) }}% to target</span>
                    <span>{{ data.targetMarketCapSol }} SOL</span>
                  </div>
                </div>
              </div>
            </div>

            <div v-else class="text-center py-8 text-muted">
              <Icon
                name="i-heroicons-chart-pie"
                class="w-12 h-12 mx-auto mb-2 opacity-50"
              />
              <p>No market cap data available</p>
            </div>
          </div>

          <!-- Token-Specific Transactions -->
          <div class="bg-gray-50/5 rounded-xl p-6">
            <h3 class="text-lg font-semibold mb-4">Token Transactions</h3>
            <WebsocketTransactionsFeed
              :token-address="demoTokenAddress"
              :max-items="5"
            />
          </div>
        </div>
      </div>

      <!-- Footer Info -->
      <footer class="mt-12 text-center text-muted text-sm">
        <p>
          This demo shows real-time WebSocket data feeds for different contexts.
        </p>
        <p class="mt-1">
          Data includes: transactions, holders, market cap, price, and posts
          mentioning tokens.
        </p>
      </footer>
    </div>
  </div>
</template>

<style scoped>
/* Custom styles for the demo page */
.bg-primary-bg {
  background-color: #031219;
}
</style>
