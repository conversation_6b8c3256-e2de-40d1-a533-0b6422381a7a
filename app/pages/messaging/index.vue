<script setup>
const layout = useLayout()

const currentUser = ref(null)
const haveContent = ref(true)

onMounted(() => {
  layout.setIsChatPage(true)
})

onUnmounted(() => {
  layout.setIsChatPage(false)
})
</script>

<template>
  <main class="w-5xl flex flex-1">
    <div
      class="flex flex-col max-w-xl h-screen w-full border-r border-gray-100/10"
    >
      <header
        class="h-16 w-full border-b border-gray-100/10 flex items-center px-4"
      >
        <div class="flex items-center gap-4">
          <UserAvatar size="sm" :user="currentUser" />
          <div>
            <h4 class="font-bold tracking-wide">Username</h4>
            <div
              class="text-xs text-green-500 flex items-center gap-1 font-semibold tracking-wide leading-0"
            >
              <Icon name="material-symbols:circle" size="10" />
              <span class="h-fit">Online</span>
            </div>
          </div>
        </div>
      </header>

      <!-- Messages -->
      <ChatMessageList />

      <!-- Message input -->
      <div
        class="border rounded border-gray-100/10 p-2 m-2 flex items-center max-h-32 h-16"
      >
        <div contenteditable class="focus:outline-none h-full flex-1 p-2"></div>
        <button
          class="p-1 hover:cursor-pointer flex items-center justify-center rounded-md w-10 h-10"
          :class="{
            'opacity-50 cursor-not-allowed': !haveContent,
            'bg-primary-btn-bg hover:bg-primary-btn-bg/90': haveContent
          }"
        >
          <Icon name="charm:paper-plane" size="20" />
        </button>
      </div>
    </div>

    <div></div>
  </main>
</template>
