<script setup lang="ts">
definePageMeta({
  layout: 'none',
  title: 'Coming Soon'
})

useHead({
  title: 'Coming Soon - Mazenut',
  meta: [
    {
      name: 'description',
      content: 'Mazenut is coming soon! Stay tuned for updates.'
    },
    {
      name: 'keywords',
      content: 'coming soon, Mazenut, crypto, tokens, solana, memecoins'
    },
    { property: 'og:title', content: 'Mazenut' },
    { property: 'og:description', content: 'Memecoins Redefined' },
    { property: 'og:image', content: '/og-cards/coming-soon.png' },
    { property: 'og:url', content: 'https://mazenut.fun/coming-soon' },
    { property: 'og:type', content: 'website' },

    { name: 'twitter:card', content: 'summary_large_image' },
    { name: 'twitter:title', content: '<PERSON><PERSON><PERSON>' },
    { name: 'twitter:description', content: 'Memecoins Redefined' },
    { name: 'twitter:image', content: '/og-cards/coming-soon.png' }
  ]
})

const toast = useToast()
const email = ref('')
const submit = () => {
  if (!email.value) {
    toast.error('Please enter your email address.')
    email.value = ''
    return
  }

  $fetch('/api/email/sub', {
    method: 'POST',
    body: {
      email: email.value
    }
  })
    .then(() => {
      toast.success('Thank you! We will notify you when we launch.')
      email.value = ''
    })
    .catch(() => {
      toast.error('Failed to submit your email. Please try again later.')
    })
}
</script>

<template>
  <div
    class="min-h-screen flex items-center justify-center px-4 mt-2 md:px-0 md:mt-0 relative overflow-hidden"
  >
    <div
      class="absolute left-1/4 w-px h-full top-0 bg-gray-100/10 hidden xl:block"
    />
    <div
      class="absolute right-1/4 w-px h-full top-0 bg-gray-100/10 hidden xl:block"
    />

    <div class="text-center max-w-2xl mx-auto relative z-10">
      <div class="mb-8">
        <h1
          class="font-display text-4xl md:text-8xl font-bold text-fg-light mb-4"
        >
          coming soon
        </h1>
        <div class="w-24 h-1 bg-primary-bg mx-auto rounded-full" />
      </div>

      <div class="mb-12">
        <p class="font-mono text-lg md:text-xl text-secondary-bg/90 mb-4">
          Something amazing is brewing...
        </p>
        <p class="font-display text-base text-secondary-bg/90 max-w-md mx-auto">
          We're working hard to bring you something incredible. Stay tuned for
          updates!
        </p>
      </div>

      <div class="mb-12">
        <div class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
          <input
            v-model="email"
            type="email"
            placeholder="Enter your email"
            class="flex-1 px-4 py-3 bg-accent/20 border border-primary-btn-bg text-fg-light placeholder-muted rounded focus:outline-none focus:ring-2 focus:ring-primary-btn-bg font-mono"
          />
          <button
            class="px-6 py-3 bg-primary-btn-bg hover:bg-primary-btn-bg/90 text-fg-light font-display font-semibold rounded transition-all duration-200 hover:-translate-y-0.5"
            @click="submit"
          >
            Notify Me
          </button>
        </div>
      </div>

      <div class="flex justify-center space-x-6">
        <a
          href="https://x.com/mazenutt"
          class="w-8 md:w-12 h-8 md:h-12 bg-secondary-bg hover:bg-secondary-bg/80 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105"
          aria-label="X"
        >
          <Icon name="ri:twitter-x-fill" class="text-lg md:text-2xl" />
        </a>

        <a
          href="https://www.instagram.com/mazenutt/"
          class="w-8 md:w-12 h-8 md:h-12 bg-secondary-bg hover:bg-secondary-bg/80 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105"
          aria-label="Instagram"
        >
          <Icon name="lucide:instagram" class="text-lg md:text-2xl" />
        </a>

        <a
          href="https://discord.gg/AaDFvW6HbG"
          class="w-8 md:w-12 h-8 md:h-12 bg-secondary-bg hover:bg-secondary-bg/80 rounded-full flex items-center justify-center transition-all duration-200 hover:scale-105"
          aria-label="Discord"
        >
          <Icon name="ic:baseline-discord" class="text-lg md:text-2xl" />
        </a>
      </div>

      <div class="md:mt-16 mt-8 pt-4 md:pt-8 border-t border-gray-100/10">
        <p class="font-mono text-sm text-secondary-bg/90">
          © 2025. All rights reserved.
        </p>
      </div>
    </div>
  </div>
</template>
